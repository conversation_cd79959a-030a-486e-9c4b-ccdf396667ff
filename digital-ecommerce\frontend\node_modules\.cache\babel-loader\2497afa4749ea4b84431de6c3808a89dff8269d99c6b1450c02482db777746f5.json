{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ProductReviews.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { StarIcon, ChatBubbleLeftIcon, HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst mockReviews = [{\n  id: 1,\n  user: {\n    name: '<PERSON>',\n    avatar: 'https://via.placeholder.com/40',\n    verified: true\n  },\n  rating: 5,\n  title: 'Excellent quality and fast delivery!',\n  comment: 'I absolutely love these headphones. The sound quality is amazing and they are very comfortable to wear for long periods. Highly recommend!',\n  date: '2024-01-15',\n  helpful: 12,\n  notHelpful: 1,\n  productName: 'Wireless Headphones'\n}, {\n  id: 2,\n  user: {\n    name: '<PERSON>',\n    avatar: 'https://via.placeholder.com/40',\n    verified: false\n  },\n  rating: 4,\n  title: 'Good value for money',\n  comment: 'The smart watch works well and has all the features I need. Battery life could be better, but overall satisfied with the purchase.',\n  date: '2024-01-12',\n  helpful: 8,\n  notHelpful: 2,\n  productName: 'Smart Watch'\n}, {\n  id: 3,\n  user: {\n    name: 'Emily Davis',\n    avatar: 'https://via.placeholder.com/40',\n    verified: true\n  },\n  rating: 5,\n  title: 'Perfect for outdoor activities',\n  comment: 'This Bluetooth speaker is waterproof and the sound quality is incredible. Perfect for beach trips and pool parties!',\n  date: '2024-01-10',\n  helpful: 15,\n  notHelpful: 0,\n  productName: 'Bluetooth Speaker'\n}, {\n  id: 4,\n  user: {\n    name: 'David Wilson',\n    avatar: 'https://via.placeholder.com/40',\n    verified: true\n  },\n  rating: 3,\n  title: 'Average product',\n  comment: 'The product is okay but not exceptional. It does what it promises but there are better alternatives in the market.',\n  date: '2024-01-08',\n  helpful: 5,\n  notHelpful: 3,\n  productName: 'Phone Case'\n}];\nconst ProductReviews = () => {\n  _s();\n  const [reviews, setReviews] = useState(mockReviews);\n  const [showWriteReview, setShowWriteReview] = useState(false);\n  const [newReview, setNewReview] = useState({\n    rating: 5,\n    title: '',\n    comment: ''\n  });\n  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;\n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\n    rating,\n    count: reviews.filter(review => review.rating === rating).length,\n    percentage: reviews.filter(review => review.rating === rating).length / reviews.length * 100\n  }));\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const renderStars = (rating, size = 'w-5 h-5') => {\n    return [...Array(5)].map((_, i) => i < rating ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n      className: `${size} text-light-orange-400`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n      className: `${size} text-light-orange-200`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this));\n  };\n  const handleSubmitReview = e => {\n    e.preventDefault();\n    const review = {\n      id: reviews.length + 1,\n      user: {\n        name: 'You',\n        avatar: 'https://via.placeholder.com/40',\n        verified: false\n      },\n      rating: newReview.rating,\n      title: newReview.title,\n      comment: newReview.comment,\n      date: new Date().toISOString().split('T')[0],\n      helpful: 0,\n      notHelpful: 0,\n      productName: 'Current Product'\n    };\n    setReviews([review, ...reviews]);\n    setNewReview({\n      rating: 5,\n      title: '',\n      comment: ''\n    });\n    setShowWriteReview(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftIcon, {\n            className: \"w-6 h-6 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), \"Customer Reviews\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowWriteReview(true),\n          className: \"bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-colors text-sm font-medium\",\n          children: \"Write Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-light-orange-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center md:text-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center md:justify-start space-x-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl font-bold text-light-orange-800\",\n              children: averageRating.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: renderStars(Math.round(averageRating))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-light-orange-600\",\n            children: [\"Based on \", reviews.length, \" review\", reviews.length !== 1 ? 's' : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: ratingDistribution.map(({\n            rating,\n            count,\n            percentage\n          }) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 w-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-light-orange-700\",\n                children: rating\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-4 h-4 text-light-orange-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 bg-light-orange-100 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-light-orange-400 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-light-orange-600 w-8\",\n              children: count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, rating, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: reviews.map(review => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-light-orange-100 pb-6 last:border-b-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: review.user.avatar,\n              alt: review.user.name,\n              className: \"w-12 h-12 rounded-full border-2 border-light-orange-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-light-orange-800\",\n                  children: review.user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), review.user.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full border border-green-200\",\n                  children: \"Verified Purchase\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: renderStars(review.rating, 'w-4 h-4')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-light-orange-600\",\n                  children: formatDate(review.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-medium text-light-orange-800 mb-2\",\n                children: review.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-light-orange-700 mb-3\",\n                children: review.comment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-light-orange-600\",\n                  children: [\"For: \", review.productName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(HandThumbUpIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Helpful (\", review.helpful, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(HandThumbDownIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"(\", review.notHelpful, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)\n        }, review.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), showWriteReview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"Write a Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowWriteReview(false),\n              className: \"text-white hover:text-light-orange-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmitReview,\n          className: \"p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-1\",\n              children: [1, 2, 3, 4, 5].map(rating => /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setNewReview({\n                  ...newReview,\n                  rating\n                }),\n                className: \"focus:outline-none\",\n                children: rating <= newReview.rating ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                  className: \"w-8 h-8 text-light-orange-400 hover:text-light-orange-500 transition-colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: \"w-8 h-8 text-light-orange-200 hover:text-light-orange-300 transition-colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 25\n                }, this)\n              }, rating, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Review Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newReview.title,\n              onChange: e => setNewReview({\n                ...newReview,\n                title: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Summarize your experience\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Your Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newReview.comment,\n              onChange: e => setNewReview({\n                ...newReview,\n                comment: e.target.value\n              }),\n              rows: 4,\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors resize-none\",\n              placeholder: \"Tell others about your experience with this product\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3 pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowWriteReview(false),\n              className: \"flex-1 px-6 py-3 border border-light-orange-300 text-light-orange-700 rounded-lg hover:bg-light-orange-50 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"flex-1 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold\",\n              children: \"Submit Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductReviews, \"thQ9MGMVK29J5Ohk5d9l62ky4WE=\");\n_c = ProductReviews;\nexport default ProductReviews;\nvar _c;\n$RefreshReg$(_c, \"ProductReviews\");", "map": {"version": 3, "names": ["React", "useState", "StarIcon", "ChatBubbleLeftIcon", "HandThumbUpIcon", "HandThumbDownIcon", "StarIconSolid", "jsxDEV", "_jsxDEV", "mockReviews", "id", "user", "name", "avatar", "verified", "rating", "title", "comment", "date", "helpful", "notHelpful", "productName", "ProductReviews", "_s", "reviews", "setReviews", "showWriteReview", "setShowWriteReview", "newReview", "set<PERSON>ew<PERSON><PERSON>iew", "averageRating", "reduce", "sum", "review", "length", "ratingDistribution", "map", "count", "filter", "percentage", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "renderStars", "size", "Array", "_", "i", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleSubmitReview", "e", "preventDefault", "toISOString", "split", "children", "onClick", "toFixed", "Math", "round", "style", "width", "src", "alt", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ProductReviews.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { StarIcon, ChatBubbleLeftIcon, HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline';\r\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\r\n\r\nconst mockReviews = [\r\n  {\r\n    id: 1,\r\n    user: {\r\n      name: '<PERSON>',\r\n      avatar: 'https://via.placeholder.com/40',\r\n      verified: true\r\n    },\r\n    rating: 5,\r\n    title: 'Excellent quality and fast delivery!',\r\n    comment: 'I absolutely love these headphones. The sound quality is amazing and they are very comfortable to wear for long periods. Highly recommend!',\r\n    date: '2024-01-15',\r\n    helpful: 12,\r\n    notHelpful: 1,\r\n    productName: 'Wireless Headphones'\r\n  },\r\n  {\r\n    id: 2,\r\n    user: {\r\n      name: '<PERSON>',\r\n      avatar: 'https://via.placeholder.com/40',\r\n      verified: false\r\n    },\r\n    rating: 4,\r\n    title: 'Good value for money',\r\n    comment: 'The smart watch works well and has all the features I need. Battery life could be better, but overall satisfied with the purchase.',\r\n    date: '2024-01-12',\r\n    helpful: 8,\r\n    notHelpful: 2,\r\n    productName: 'Smart Watch'\r\n  },\r\n  {\r\n    id: 3,\r\n    user: {\r\n      name: '<PERSON>',\r\n      avatar: 'https://via.placeholder.com/40',\r\n      verified: true\r\n    },\r\n    rating: 5,\r\n    title: 'Perfect for outdoor activities',\r\n    comment: 'This Bluetooth speaker is waterproof and the sound quality is incredible. Perfect for beach trips and pool parties!',\r\n    date: '2024-01-10',\r\n    helpful: 15,\r\n    notHelpful: 0,\r\n    productName: 'Bluetooth Speaker'\r\n  },\r\n  {\r\n    id: 4,\r\n    user: {\r\n      name: 'David Wilson',\r\n      avatar: 'https://via.placeholder.com/40',\r\n      verified: true\r\n    },\r\n    rating: 3,\r\n    title: 'Average product',\r\n    comment: 'The product is okay but not exceptional. It does what it promises but there are better alternatives in the market.',\r\n    date: '2024-01-08',\r\n    helpful: 5,\r\n    notHelpful: 3,\r\n    productName: 'Phone Case'\r\n  }\r\n];\r\n\r\nconst ProductReviews = () => {\r\n  const [reviews, setReviews] = useState(mockReviews);\r\n  const [showWriteReview, setShowWriteReview] = useState(false);\r\n  const [newReview, setNewReview] = useState({\r\n    rating: 5,\r\n    title: '',\r\n    comment: ''\r\n  });\r\n\r\n  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;\r\n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\r\n    rating,\r\n    count: reviews.filter(review => review.rating === rating).length,\r\n    percentage: (reviews.filter(review => review.rating === rating).length / reviews.length) * 100\r\n  }));\r\n\r\n  const formatDate = (dateString) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const renderStars = (rating, size = 'w-5 h-5') => {\r\n    return [...Array(5)].map((_, i) => (\r\n      i < rating ? (\r\n        <StarIconSolid key={i} className={`${size} text-light-orange-400`} />\r\n      ) : (\r\n        <StarIcon key={i} className={`${size} text-light-orange-200`} />\r\n      )\r\n    ));\r\n  };\r\n\r\n  const handleSubmitReview = (e) => {\r\n    e.preventDefault();\r\n    const review = {\r\n      id: reviews.length + 1,\r\n      user: {\r\n        name: 'You',\r\n        avatar: 'https://via.placeholder.com/40',\r\n        verified: false\r\n      },\r\n      rating: newReview.rating,\r\n      title: newReview.title,\r\n      comment: newReview.comment,\r\n      date: new Date().toISOString().split('T')[0],\r\n      helpful: 0,\r\n      notHelpful: 0,\r\n      productName: 'Current Product'\r\n    };\r\n\r\n    setReviews([review, ...reviews]);\r\n    setNewReview({ rating: 5, title: '', comment: '' });\r\n    setShowWriteReview(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden\">\r\n      {/* Header */}\r\n      <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h2 className=\"text-xl font-bold text-white flex items-center\">\r\n            <ChatBubbleLeftIcon className=\"w-6 h-6 mr-2\" />\r\n            Customer Reviews\r\n          </h2>\r\n          <button\r\n            onClick={() => setShowWriteReview(true)}\r\n            className=\"bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-colors text-sm font-medium\"\r\n          >\r\n            Write Review\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Reviews Summary */}\r\n      <div className=\"p-6 border-b border-light-orange-100\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div className=\"text-center md:text-left\">\r\n            <div className=\"flex items-center justify-center md:justify-start space-x-2 mb-2\">\r\n              <span className=\"text-3xl font-bold text-light-orange-800\">\r\n                {averageRating.toFixed(1)}\r\n              </span>\r\n              <div className=\"flex\">\r\n                {renderStars(Math.round(averageRating))}\r\n              </div>\r\n            </div>\r\n            <p className=\"text-light-orange-600\">\r\n              Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            {ratingDistribution.map(({ rating, count, percentage }) => (\r\n              <div key={rating} className=\"flex items-center space-x-3\">\r\n                <div className=\"flex items-center space-x-1 w-12\">\r\n                  <span className=\"text-sm text-light-orange-700\">{rating}</span>\r\n                  <StarIconSolid className=\"w-4 h-4 text-light-orange-400\" />\r\n                </div>\r\n                <div className=\"flex-1 bg-light-orange-100 rounded-full h-2\">\r\n                  <div\r\n                    className=\"bg-light-orange-400 h-2 rounded-full transition-all duration-300\"\r\n                    style={{ width: `${percentage}%` }}\r\n                  ></div>\r\n                </div>\r\n                <span className=\"text-sm text-light-orange-600 w-8\">{count}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Reviews List */}\r\n      <div className=\"p-6\">\r\n        <div className=\"space-y-6\">\r\n          {reviews.map((review) => (\r\n            <div key={review.id} className=\"border-b border-light-orange-100 pb-6 last:border-b-0\">\r\n              <div className=\"flex items-start space-x-4\">\r\n                <img\r\n                  src={review.user.avatar}\r\n                  alt={review.user.name}\r\n                  className=\"w-12 h-12 rounded-full border-2 border-light-orange-200\"\r\n                />\r\n                <div className=\"flex-1\">\r\n                  <div className=\"flex items-center space-x-2 mb-2\">\r\n                    <h4 className=\"font-semibold text-light-orange-800\">{review.user.name}</h4>\r\n                    {review.user.verified && (\r\n                      <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full border border-green-200\">\r\n                        Verified Purchase\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center space-x-3 mb-2\">\r\n                    <div className=\"flex\">\r\n                      {renderStars(review.rating, 'w-4 h-4')}\r\n                    </div>\r\n                    <span className=\"text-sm text-light-orange-600\">{formatDate(review.date)}</span>\r\n                  </div>\r\n\r\n                  <h5 className=\"font-medium text-light-orange-800 mb-2\">{review.title}</h5>\r\n                  <p className=\"text-light-orange-700 mb-3\">{review.comment}</p>\r\n\r\n                  <div className=\"flex items-center space-x-4 text-sm\">\r\n                    <span className=\"text-light-orange-600\">For: {review.productName}</span>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <button className=\"flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors\">\r\n                        <HandThumbUpIcon className=\"w-4 h-4\" />\r\n                        <span>Helpful ({review.helpful})</span>\r\n                      </button>\r\n                      <button className=\"flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors\">\r\n                        <HandThumbDownIcon className=\"w-4 h-4\" />\r\n                        <span>({review.notHelpful})</span>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Write Review Modal */}\r\n      {showWriteReview && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4\">\r\n            <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-bold text-white\">Write a Review</h2>\r\n                <button\r\n                  onClick={() => setShowWriteReview(false)}\r\n                  className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <form onSubmit={handleSubmitReview} className=\"p-6 space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Rating\r\n                </label>\r\n                <div className=\"flex space-x-1\">\r\n                  {[1, 2, 3, 4, 5].map((rating) => (\r\n                    <button\r\n                      key={rating}\r\n                      type=\"button\"\r\n                      onClick={() => setNewReview({...newReview, rating})}\r\n                      className=\"focus:outline-none\"\r\n                    >\r\n                      {rating <= newReview.rating ? (\r\n                        <StarIconSolid className=\"w-8 h-8 text-light-orange-400 hover:text-light-orange-500 transition-colors\" />\r\n                      ) : (\r\n                        <StarIcon className=\"w-8 h-8 text-light-orange-200 hover:text-light-orange-300 transition-colors\" />\r\n                      )}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Review Title\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newReview.title}\r\n                  onChange={(e) => setNewReview({...newReview, title: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Summarize your experience\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Your Review\r\n                </label>\r\n                <textarea\r\n                  value={newReview.comment}\r\n                  onChange={(e) => setNewReview({...newReview, comment: e.target.value})}\r\n                  rows={4}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors resize-none\"\r\n                  placeholder=\"Tell others about your experience with this product\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex space-x-3 pt-4\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowWriteReview(false)}\r\n                  className=\"flex-1 px-6 py-3 border border-light-orange-300 text-light-orange-700 rounded-lg hover:bg-light-orange-50 transition-colors\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"flex-1 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold\"\r\n                >\r\n                  Submit Review\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductReviews;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC9G,SAASH,QAAQ,IAAII,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,WAAW,GAAG,CAClB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;IACJC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,gCAAgC;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,sCAAsC;EAC7CC,OAAO,EAAE,4IAA4I;EACrJC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;IACJC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE,gCAAgC;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,sBAAsB;EAC7BC,OAAO,EAAE,oIAAoI;EAC7IC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;IACJC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,gCAAgC;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,gCAAgC;EACvCC,OAAO,EAAE,qHAAqH;EAC9HC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;IACJC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,gCAAgC;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,iBAAiB;EACxBC,OAAO,EAAE,oHAAoH;EAC7HC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAACQ,WAAW,CAAC;EACnD,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC;IACzCc,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMa,aAAa,GAAGN,OAAO,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAAClB,MAAM,EAAE,CAAC,CAAC,GAAGS,OAAO,CAACU,MAAM;EAC9F,MAAMC,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrB,MAAM,KAAK;IACxDA,MAAM;IACNsB,KAAK,EAAEb,OAAO,CAACc,MAAM,CAACL,MAAM,IAAIA,MAAM,CAAClB,MAAM,KAAKA,MAAM,CAAC,CAACmB,MAAM;IAChEK,UAAU,EAAGf,OAAO,CAACc,MAAM,CAACL,MAAM,IAAIA,MAAM,CAAClB,MAAM,KAAKA,MAAM,CAAC,CAACmB,MAAM,GAAGV,OAAO,CAACU,MAAM,GAAI;EAC7F,CAAC,CAAC,CAAC;EAEH,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAChC,MAAM,EAAEiC,IAAI,GAAG,SAAS,KAAK;IAChD,OAAO,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,CAAC,EAAEC,CAAC,KAC5BA,CAAC,GAAGpC,MAAM,gBACRP,OAAA,CAACF,aAAa;MAAS8C,SAAS,EAAE,GAAGJ,IAAI;IAAyB,GAA9CG,CAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAA+C,CAAC,gBAErEhD,OAAA,CAACN,QAAQ;MAASkD,SAAS,EAAE,GAAGJ,IAAI;IAAyB,GAA9CG,CAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAA+C,CAElE,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAM1B,MAAM,GAAG;MACbvB,EAAE,EAAEc,OAAO,CAACU,MAAM,GAAG,CAAC;MACtBvB,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,gCAAgC;QACxCC,QAAQ,EAAE;MACZ,CAAC;MACDC,MAAM,EAAEa,SAAS,CAACb,MAAM;MACxBC,KAAK,EAAEY,SAAS,CAACZ,KAAK;MACtBC,OAAO,EAAEW,SAAS,CAACX,OAAO;MAC1BC,IAAI,EAAE,IAAIwB,IAAI,CAAC,CAAC,CAACkB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5C1C,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE;IACf,CAAC;IAEDI,UAAU,CAAC,CAACQ,MAAM,EAAE,GAAGT,OAAO,CAAC,CAAC;IAChCK,YAAY,CAAC;MAAEd,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;IACnDU,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,oBACEnB,OAAA;IAAK4C,SAAS,EAAC,8EAA8E;IAAAU,QAAA,gBAE3FtD,OAAA;MAAK4C,SAAS,EAAC,sEAAsE;MAAAU,QAAA,eACnFtD,OAAA;QAAK4C,SAAS,EAAC,mCAAmC;QAAAU,QAAA,gBAChDtD,OAAA;UAAI4C,SAAS,EAAC,gDAAgD;UAAAU,QAAA,gBAC5DtD,OAAA,CAACL,kBAAkB;YAACiD,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAAC,IAAI,CAAE;UACxCyB,SAAS,EAAC,kHAAkH;UAAAU,QAAA,EAC7H;QAED;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK4C,SAAS,EAAC,sCAAsC;MAAAU,QAAA,eACnDtD,OAAA;QAAK4C,SAAS,EAAC,uCAAuC;QAAAU,QAAA,gBACpDtD,OAAA;UAAK4C,SAAS,EAAC,0BAA0B;UAAAU,QAAA,gBACvCtD,OAAA;YAAK4C,SAAS,EAAC,kEAAkE;YAAAU,QAAA,gBAC/EtD,OAAA;cAAM4C,SAAS,EAAC,0CAA0C;cAAAU,QAAA,EACvDhC,aAAa,CAACkC,OAAO,CAAC,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACPhD,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAU,QAAA,EAClBf,WAAW,CAACkB,IAAI,CAACC,KAAK,CAACpC,aAAa,CAAC;YAAC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhD,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAU,QAAA,GAAC,WAC1B,EAACtC,OAAO,CAACU,MAAM,EAAC,SAAO,EAACV,OAAO,CAACU,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhD,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAU,QAAA,EACvB3B,kBAAkB,CAACC,GAAG,CAAC,CAAC;YAAErB,MAAM;YAAEsB,KAAK;YAAEE;UAAW,CAAC,kBACpD/B,OAAA;YAAkB4C,SAAS,EAAC,6BAA6B;YAAAU,QAAA,gBACvDtD,OAAA;cAAK4C,SAAS,EAAC,kCAAkC;cAAAU,QAAA,gBAC/CtD,OAAA;gBAAM4C,SAAS,EAAC,+BAA+B;gBAAAU,QAAA,EAAE/C;cAAM;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/DhD,OAAA,CAACF,aAAa;gBAAC8C,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNhD,OAAA;cAAK4C,SAAS,EAAC,6CAA6C;cAAAU,QAAA,eAC1DtD,OAAA;gBACE4C,SAAS,EAAC,kEAAkE;gBAC5Ee,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAG7B,UAAU;gBAAI;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhD,OAAA;cAAM4C,SAAS,EAAC,mCAAmC;cAAAU,QAAA,EAAEzB;YAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAX1DzC,MAAM;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK4C,SAAS,EAAC,KAAK;MAAAU,QAAA,eAClBtD,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAU,QAAA,EACvBtC,OAAO,CAACY,GAAG,CAAEH,MAAM,iBAClBzB,OAAA;UAAqB4C,SAAS,EAAC,uDAAuD;UAAAU,QAAA,eACpFtD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAU,QAAA,gBACzCtD,OAAA;cACE6D,GAAG,EAAEpC,MAAM,CAACtB,IAAI,CAACE,MAAO;cACxByD,GAAG,EAAErC,MAAM,CAACtB,IAAI,CAACC,IAAK;cACtBwC,SAAS,EAAC;YAAyD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACFhD,OAAA;cAAK4C,SAAS,EAAC,QAAQ;cAAAU,QAAA,gBACrBtD,OAAA;gBAAK4C,SAAS,EAAC,kCAAkC;gBAAAU,QAAA,gBAC/CtD,OAAA;kBAAI4C,SAAS,EAAC,qCAAqC;kBAAAU,QAAA,EAAE7B,MAAM,CAACtB,IAAI,CAACC;gBAAI;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC1EvB,MAAM,CAACtB,IAAI,CAACG,QAAQ,iBACnBN,OAAA;kBAAM4C,SAAS,EAAC,oFAAoF;kBAAAU,QAAA,EAAC;gBAErG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENhD,OAAA;gBAAK4C,SAAS,EAAC,kCAAkC;gBAAAU,QAAA,gBAC/CtD,OAAA;kBAAK4C,SAAS,EAAC,MAAM;kBAAAU,QAAA,EAClBf,WAAW,CAACd,MAAM,CAAClB,MAAM,EAAE,SAAS;gBAAC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNhD,OAAA;kBAAM4C,SAAS,EAAC,+BAA+B;kBAAAU,QAAA,EAAEtB,UAAU,CAACP,MAAM,CAACf,IAAI;gBAAC;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eAENhD,OAAA;gBAAI4C,SAAS,EAAC,wCAAwC;gBAAAU,QAAA,EAAE7B,MAAM,CAACjB;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EhD,OAAA;gBAAG4C,SAAS,EAAC,4BAA4B;gBAAAU,QAAA,EAAE7B,MAAM,CAAChB;cAAO;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE9DhD,OAAA;gBAAK4C,SAAS,EAAC,qCAAqC;gBAAAU,QAAA,gBAClDtD,OAAA;kBAAM4C,SAAS,EAAC,uBAAuB;kBAAAU,QAAA,GAAC,OAAK,EAAC7B,MAAM,CAACZ,WAAW;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEhD,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAU,QAAA,gBAC1CtD,OAAA;oBAAQ4C,SAAS,EAAC,iGAAiG;oBAAAU,QAAA,gBACjHtD,OAAA,CAACJ,eAAe;sBAACgD,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvChD,OAAA;sBAAAsD,QAAA,GAAM,WAAS,EAAC7B,MAAM,CAACd,OAAO,EAAC,GAAC;oBAAA;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACThD,OAAA;oBAAQ4C,SAAS,EAAC,iGAAiG;oBAAAU,QAAA,gBACjHtD,OAAA,CAACH,iBAAiB;sBAAC+C,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzChD,OAAA;sBAAAsD,QAAA,GAAM,GAAC,EAAC7B,MAAM,CAACb,UAAU,EAAC,GAAC;oBAAA;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAzCEvB,MAAM,CAACvB,EAAE;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0Cd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,eAAe,iBACdlB,OAAA;MAAK4C,SAAS,EAAC,4EAA4E;MAAAU,QAAA,eACzFtD,OAAA;QAAK4C,SAAS,EAAC,sDAAsD;QAAAU,QAAA,gBACnEtD,OAAA;UAAK4C,SAAS,EAAC,mFAAmF;UAAAU,QAAA,eAChGtD,OAAA;YAAK4C,SAAS,EAAC,mCAAmC;YAAAU,QAAA,gBAChDtD,OAAA;cAAI4C,SAAS,EAAC,8BAA8B;cAAAU,QAAA,EAAC;YAAc;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEhD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAAC,KAAK,CAAE;cACzCyB,SAAS,EAAC,0DAA0D;cAAAU,QAAA,eAEpEtD,OAAA;gBAAK4C,SAAS,EAAC,SAAS;gBAACmB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAX,QAAA,eAC5EtD,OAAA;kBAAMkE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA;UAAMsE,QAAQ,EAAErB,kBAAmB;UAACL,SAAS,EAAC,eAAe;UAAAU,QAAA,gBAC3DtD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAO4C,SAAS,EAAC,wDAAwD;cAAAU,QAAA,EAAC;YAE1E;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK4C,SAAS,EAAC,gBAAgB;cAAAU,QAAA,EAC5B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC1B,GAAG,CAAErB,MAAM,iBAC1BP,OAAA;gBAEEuE,IAAI,EAAC,QAAQ;gBACbhB,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC;kBAAC,GAAGD,SAAS;kBAAEb;gBAAM,CAAC,CAAE;gBACpDqC,SAAS,EAAC,oBAAoB;gBAAAU,QAAA,EAE7B/C,MAAM,IAAIa,SAAS,CAACb,MAAM,gBACzBP,OAAA,CAACF,aAAa;kBAAC8C,SAAS,EAAC;gBAA6E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEzGhD,OAAA,CAACN,QAAQ;kBAACkD,SAAS,EAAC;gBAA6E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACpG,GATIzC,MAAM;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUL,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAO4C,SAAS,EAAC,wDAAwD;cAAAU,QAAA,EAAC;YAE1E;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEuE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEpD,SAAS,CAACZ,KAAM;cACvBiE,QAAQ,EAAGvB,CAAC,IAAK7B,YAAY,CAAC;gBAAC,GAAGD,SAAS;gBAAEZ,KAAK,EAAE0C,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrE5B,SAAS,EAAC,qJAAqJ;cAC/J+B,WAAW,EAAC,2BAA2B;cACvCC,QAAQ;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAO4C,SAAS,EAAC,wDAAwD;cAAAU,QAAA,EAAC;YAE1E;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACEwE,KAAK,EAAEpD,SAAS,CAACX,OAAQ;cACzBgE,QAAQ,EAAGvB,CAAC,IAAK7B,YAAY,CAAC;gBAAC,GAAGD,SAAS;gBAAEX,OAAO,EAAEyC,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cACvEK,IAAI,EAAE,CAAE;cACRjC,SAAS,EAAC,iKAAiK;cAC3K+B,WAAW,EAAC,qDAAqD;cACjEC,QAAQ;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhD,OAAA;YAAK4C,SAAS,EAAC,qBAAqB;YAAAU,QAAA,gBAClCtD,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACbhB,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAAC,KAAK,CAAE;cACzCyB,SAAS,EAAC,6HAA6H;cAAAU,QAAA,EACxI;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThD,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACb3B,SAAS,EAAC,mMAAmM;cAAAU,QAAA,EAC9M;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjC,EAAA,CA7PID,cAAc;AAAAgE,EAAA,GAAdhE,cAAc;AA+PpB,eAAeA,cAAc;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}