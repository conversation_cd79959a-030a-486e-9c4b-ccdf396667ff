{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\ToastContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { CheckCircleIcon, ExclamationCircleIcon, InformationCircleIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToastContext = /*#__PURE__*/createContext();\nexport const useToast = () => {\n  _s();\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n_s(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst Toast = ({\n  toast,\n  onRemove\n}) => {\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"w-5 h-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(XCircleIcon, {\n          className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(ExclamationCircleIcon, {\n          className: \"w-5 h-5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n          className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getBgColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200';\n      default:\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: -50,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      y: 0,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      y: -50,\n      scale: 0.9\n    },\n    className: `flex items-center justify-between p-4 rounded-lg border shadow-lg ${getBgColor()}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3\",\n      children: [getIcon(), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-900\",\n          children: toast.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), toast.message && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 mt-1\",\n          children: toast.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onRemove(toast.id),\n      className: \"p-1 rounded-full text-gray-400 hover:text-gray-600\",\n      children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n        className: \"w-4 h-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c = Toast;\nexport const ToastProvider = ({\n  children\n}) => {\n  _s2();\n  const [toasts, setToasts] = useState([]);\n  const addToast = useCallback(toast => {\n    const id = Date.now() + Math.random();\n    const newToast = {\n      id,\n      ...toast\n    };\n    setToasts(prev => [...prev, newToast]);\n\n    // Auto remove after duration\n    const duration = toast.duration || 5000;\n    setTimeout(() => {\n      removeToast(id);\n    }, duration);\n    return id;\n  }, []);\n  const removeToast = useCallback(id => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n  const showSuccess = useCallback((title, message, duration) => {\n    return addToast({\n      type: 'success',\n      title,\n      message,\n      duration\n    });\n  }, [addToast]);\n  const showError = useCallback((title, message, duration) => {\n    return addToast({\n      type: 'error',\n      title,\n      message,\n      duration\n    });\n  }, [addToast]);\n  const showWarning = useCallback((title, message, duration) => {\n    return addToast({\n      type: 'warning',\n      title,\n      message,\n      duration\n    });\n  }, [addToast]);\n  const showInfo = useCallback((title, message, duration) => {\n    return addToast({\n      type: 'info',\n      title,\n      message,\n      duration\n    });\n  }, [addToast]);\n  const value = {\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    removeToast\n  };\n  return /*#__PURE__*/_jsxDEV(ToastContext.Provider, {\n    value: value,\n    children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: toasts.map(toast => /*#__PURE__*/_jsxDEV(Toast, {\n          toast: toast,\n          onRemove: removeToast\n        }, toast.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s2(ToastProvider, \"CCEQwvLCrMMhwdy8jecChmEeP78=\");\n_c2 = ToastProvider;\nvar _c, _c2;\n$RefreshReg$(_c, \"Toast\");\n$RefreshReg$(_c2, \"ToastProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "motion", "AnimatePresence", "CheckCircleIcon", "ExclamationCircleIcon", "InformationCircleIcon", "XCircleIcon", "XMarkIcon", "jsxDEV", "_jsxDEV", "ToastContext", "useToast", "_s", "context", "Error", "Toast", "toast", "onRemove", "getIcon", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getBgColor", "div", "initial", "opacity", "y", "scale", "animate", "exit", "children", "title", "message", "onClick", "id", "_c", "ToastProvider", "_s2", "toasts", "setToasts", "addToast", "Date", "now", "Math", "random", "newToast", "prev", "duration", "setTimeout", "removeToast", "filter", "showSuccess", "showError", "showWarning", "showInfo", "value", "Provider", "map", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/ToastContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  CheckCircleIcon,\n  ExclamationCircleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\nconst ToastContext = createContext();\n\nexport const useToast = () => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\nconst Toast = ({ toast, onRemove }) => {\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />;\n      case 'error':\n        return <XCircleIcon className=\"w-5 h-5 text-red-500\" />;\n      case 'warning':\n        return <ExclamationCircleIcon className=\"w-5 h-5 text-yellow-500\" />;\n      default:\n        return <InformationCircleIcon className=\"w-5 h-5 text-blue-500\" />;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200';\n      default:\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -50, scale: 0.9 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -50, scale: 0.9 }}\n      className={`flex items-center justify-between p-4 rounded-lg border shadow-lg ${getBgColor()}`}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <div>\n          <p className=\"text-sm font-medium text-gray-900\">\n            {toast.title}\n          </p>\n          {toast.message && (\n            <p className=\"text-sm text-gray-600 mt-1\">\n              {toast.message}\n            </p>\n          )}\n        </div>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"p-1 rounded-full text-gray-400 hover:text-gray-600\"\n      >\n        <XMarkIcon className=\"w-4 h-4\" />\n      </button>\n    </motion.div>\n  );\n};\n\nexport const ToastProvider = ({ children }) => {\n  const [toasts, setToasts] = useState([]);\n\n  const addToast = useCallback((toast) => {\n    const id = Date.now() + Math.random();\n    const newToast = { id, ...toast };\n    \n    setToasts(prev => [...prev, newToast]);\n\n    // Auto remove after duration\n    const duration = toast.duration || 5000;\n    setTimeout(() => {\n      removeToast(id);\n    }, duration);\n\n    return id;\n  }, []);\n\n  const removeToast = useCallback((id) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n\n  const showSuccess = useCallback((title, message, duration) => {\n    return addToast({ type: 'success', title, message, duration });\n  }, [addToast]);\n\n  const showError = useCallback((title, message, duration) => {\n    return addToast({ type: 'error', title, message, duration });\n  }, [addToast]);\n\n  const showWarning = useCallback((title, message, duration) => {\n    return addToast({ type: 'warning', title, message, duration });\n  }, [addToast]);\n\n  const showInfo = useCallback((title, message, duration) => {\n    return addToast({ type: 'info', title, message, duration });\n  }, [addToast]);\n\n  const value = {\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    removeToast\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      {children}\n      \n      {/* Toast Container */}\n      <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\">\n        <AnimatePresence>\n          {toasts.map(toast => (\n            <Toast\n              key={toast.id}\n              toast={toast}\n              onRemove={removeToast}\n            />\n          ))}\n        </AnimatePresence>\n      </div>\n    </ToastContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,WAAW,EACXC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,YAAY,gBAAGb,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMc,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGf,UAAU,CAACY,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,MAAMI,KAAK,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQF,KAAK,CAACG,IAAI;MAChB,KAAK,SAAS;QACZ,oBAAOV,OAAA,CAACN,eAAe;UAACiB,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,OAAO;QACV,oBAAOf,OAAA,CAACH,WAAW;UAACc,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,SAAS;QACZ,oBAAOf,OAAA,CAACL,qBAAqB;UAACgB,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE;QACE,oBAAOf,OAAA,CAACJ,qBAAqB;UAACe,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtE;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQT,KAAK,CAACG,IAAI;MAChB,KAAK,SAAS;QACZ,OAAO,8BAA8B;MACvC,KAAK,OAAO;QACV,OAAO,0BAA0B;MACnC,KAAK,SAAS;QACZ,OAAO,gCAAgC;MACzC;QACE,OAAO,4BAA4B;IACvC;EACF,CAAC;EAED,oBACEV,OAAA,CAACR,MAAM,CAACyB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE;IAC5CC,OAAO,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IACxCE,IAAI,EAAE;MAAEJ,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE;IACzCV,SAAS,EAAE,qEAAqEK,UAAU,CAAC,CAAC,EAAG;IAAAQ,QAAA,gBAE/FxB,OAAA;MAAKW,SAAS,EAAC,6BAA6B;MAAAa,QAAA,GACzCf,OAAO,CAAC,CAAC,eACVT,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAGW,SAAS,EAAC,mCAAmC;UAAAa,QAAA,EAC7CjB,KAAK,CAACkB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EACHR,KAAK,CAACmB,OAAO,iBACZ1B,OAAA;UAAGW,SAAS,EAAC,4BAA4B;UAAAa,QAAA,EACtCjB,KAAK,CAACmB;QAAO;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNf,OAAA;MACE2B,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAACD,KAAK,CAACqB,EAAE,CAAE;MAClCjB,SAAS,EAAC,oDAAoD;MAAAa,QAAA,eAE9DxB,OAAA,CAACF,SAAS;QAACa,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;AAACc,EAAA,GAvDIvB,KAAK;AAyDX,OAAO,MAAMwB,aAAa,GAAGA,CAAC;EAAEN;AAAS,CAAC,KAAK;EAAAO,GAAA;EAC7C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM4C,QAAQ,GAAG3C,WAAW,CAAEgB,KAAK,IAAK;IACtC,MAAMqB,EAAE,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;IACrC,MAAMC,QAAQ,GAAG;MAAEX,EAAE;MAAE,GAAGrB;IAAM,CAAC;IAEjC0B,SAAS,CAACO,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;;IAEtC;IACA,MAAME,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ,IAAI,IAAI;IACvCC,UAAU,CAAC,MAAM;MACfC,WAAW,CAACf,EAAE,CAAC;IACjB,CAAC,EAAEa,QAAQ,CAAC;IAEZ,OAAOb,EAAE;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,WAAW,GAAGpD,WAAW,CAAEqC,EAAE,IAAK;IACtCK,SAAS,CAACO,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACrC,KAAK,IAAIA,KAAK,CAACqB,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,WAAW,GAAGtD,WAAW,CAAC,CAACkC,KAAK,EAAEC,OAAO,EAAEe,QAAQ,KAAK;IAC5D,OAAOP,QAAQ,CAAC;MAAExB,IAAI,EAAE,SAAS;MAAEe,KAAK;MAAEC,OAAO;MAAEe;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMY,SAAS,GAAGvD,WAAW,CAAC,CAACkC,KAAK,EAAEC,OAAO,EAAEe,QAAQ,KAAK;IAC1D,OAAOP,QAAQ,CAAC;MAAExB,IAAI,EAAE,OAAO;MAAEe,KAAK;MAAEC,OAAO;MAAEe;IAAS,CAAC,CAAC;EAC9D,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMa,WAAW,GAAGxD,WAAW,CAAC,CAACkC,KAAK,EAAEC,OAAO,EAAEe,QAAQ,KAAK;IAC5D,OAAOP,QAAQ,CAAC;MAAExB,IAAI,EAAE,SAAS;MAAEe,KAAK;MAAEC,OAAO;MAAEe;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMc,QAAQ,GAAGzD,WAAW,CAAC,CAACkC,KAAK,EAAEC,OAAO,EAAEe,QAAQ,KAAK;IACzD,OAAOP,QAAQ,CAAC;MAAExB,IAAI,EAAE,MAAM;MAAEe,KAAK;MAAEC,OAAO;MAAEe;IAAS,CAAC,CAAC;EAC7D,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMe,KAAK,GAAG;IACZJ,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,QAAQ;IACRL;EACF,CAAC;EAED,oBACE3C,OAAA,CAACC,YAAY,CAACiD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzB,QAAA,GACjCA,QAAQ,eAGTxB,OAAA;MAAKW,SAAS,EAAC,oDAAoD;MAAAa,QAAA,eACjExB,OAAA,CAACP,eAAe;QAAA+B,QAAA,EACbQ,MAAM,CAACmB,GAAG,CAAC5C,KAAK,iBACfP,OAAA,CAACM,KAAK;UAEJC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEmC;QAAY,GAFjBpC,KAAK,CAACqB,EAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGd,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE5B,CAAC;AAACgB,GAAA,CAhEWD,aAAa;AAAAsB,GAAA,GAAbtB,aAAa;AAAA,IAAAD,EAAA,EAAAuB,GAAA;AAAAC,YAAA,CAAAxB,EAAA;AAAAwB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}