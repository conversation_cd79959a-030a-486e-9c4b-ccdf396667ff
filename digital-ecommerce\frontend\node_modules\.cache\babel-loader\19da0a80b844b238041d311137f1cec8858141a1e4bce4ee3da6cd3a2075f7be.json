{"ast": null, "code": "export var numericUnicodeMap = {\n  0: 65533,\n  128: 8364,\n  130: 8218,\n  131: 402,\n  132: 8222,\n  133: 8230,\n  134: 8224,\n  135: 8225,\n  136: 710,\n  137: 8240,\n  138: 352,\n  139: 8249,\n  140: 338,\n  142: 381,\n  145: 8216,\n  146: 8217,\n  147: 8220,\n  148: 8221,\n  149: 8226,\n  150: 8211,\n  151: 8212,\n  152: 732,\n  153: 8482,\n  154: 353,\n  155: 8250,\n  156: 339,\n  158: 382,\n  159: 376\n};", "map": {"version": 3, "names": ["numericUnicodeMap"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\html-entities\\src\\numeric-unicode-map.ts"], "sourcesContent": ["export const numericUnicodeMap: Record<number, number> = {\n    0: 65533,\n    128: 8364,\n    130: 8218,\n    131: 402,\n    132: 8222,\n    133: 8230,\n    134: 8224,\n    135: 8225,\n    136: 710,\n    137: 8240,\n    138: 352,\n    139: 8249,\n    140: 338,\n    142: 381,\n    145: 8216,\n    146: 8217,\n    147: 8220,\n    148: 8221,\n    149: 8226,\n    150: 8211,\n    151: 8212,\n    152: 732,\n    153: 8482,\n    154: 353,\n    155: 8250,\n    156: 339,\n    158: 382,\n    159: 376\n};\n"], "mappings": "AAAA,OAAO,IAAMA,iBAAiB,GAA2B;EACrD,CAAC,EAAE,KAAK;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE;CACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}