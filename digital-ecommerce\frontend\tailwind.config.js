module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}', './public/index.html'],
  // Dark mode removed - light theme only
  theme: {
    extend: {
      colors: {
        'light-orange': {
          50: '#FFF8F1',
          100: '#FFF3E0',
          200: '#FFE0B2',
          300: '#FFCC80',
          400: '#FFB74D',
          500: '#FFB366', // User's preferred light orange
          600: '#FB8C00',
          700: '#F57C00',
          800: '#EF6C00',
          900: '#E65100',
        },

      },
      // Custom gradients for light theme
      backgroundImage: {
        'light-gradient': 'linear-gradient(135deg, #FFF8F1 0%, #ffffff 100%)',
      },
      // Enhanced transitions for theme switching
      transitionProperty: {
        'theme': 'background-color, border-color, color, fill, stroke, opacity, box-shadow, transform',
      },
      transitionDuration: {
        '400': '400ms',
      },
    },
  },
  // Variants removed - light theme only
  plugins: [],
}