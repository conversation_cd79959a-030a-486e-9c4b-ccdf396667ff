{"ast": null, "code": "const focusableElements = new Set([\"BUTTON\", \"INPUT\", \"SELECT\", \"TEXTAREA\", \"A\"]);\nfunction isElementKeyboardAccessible(element) {\n  return focusableElements.has(element.tagName) || element.tabIndex !== -1;\n}\nexport { isElementKeyboardAccessible };", "map": {"version": 3, "names": ["focusableElements", "Set", "isElementKeyboardAccessible", "element", "has", "tagName", "tabIndex"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs"], "sourcesContent": ["const focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\nexport { isElementKeyboardAccessible };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,UAAU,EACV,GAAG,CACN,CAAC;AACF,SAASC,2BAA2BA,CAACC,OAAO,EAAE;EAC1C,OAAQH,iBAAiB,CAACI,GAAG,CAACD,OAAO,CAACE,OAAO,CAAC,IAC1CF,OAAO,CAACG,QAAQ,KAAK,CAAC,CAAC;AAC/B;AAEA,SAASJ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}