{"ast": null, "code": "import { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { spring } from '../generators/spring/index.mjs';\nconst transitionTypeMap = {\n  decay: inertia,\n  inertia,\n  tween: keyframes,\n  keyframes: keyframes,\n  spring\n};\nfunction replaceTransitionType(transition) {\n  if (typeof transition.type === \"string\") {\n    transition.type = transitionTypeMap[transition.type];\n  }\n}\nexport { replaceTransitionType };", "map": {"version": 3, "names": ["inertia", "keyframes", "spring", "transitionTypeMap", "decay", "tween", "replaceTransitionType", "transition", "type"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/utils/replace-transition-type.mjs"], "sourcesContent": ["import { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { spring } from '../generators/spring/index.mjs';\n\nconst transitionTypeMap = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nfunction replaceTransitionType(transition) {\n    if (typeof transition.type === \"string\") {\n        transition.type = transitionTypeMap[transition.type];\n    }\n}\n\nexport { replaceTransitionType };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,MAAM,QAAQ,gCAAgC;AAEvD,MAAMC,iBAAiB,GAAG;EACtBC,KAAK,EAAEJ,OAAO;EACdA,OAAO;EACPK,KAAK,EAAEJ,SAAS;EAChBA,SAAS,EAAEA,SAAS;EACpBC;AACJ,CAAC;AACD,SAASI,qBAAqBA,CAACC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,CAACC,IAAI,KAAK,QAAQ,EAAE;IACrCD,UAAU,CAACC,IAAI,GAAGL,iBAAiB,CAACI,UAAU,CAACC,IAAI,CAAC;EACxD;AACJ;AAEA,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}