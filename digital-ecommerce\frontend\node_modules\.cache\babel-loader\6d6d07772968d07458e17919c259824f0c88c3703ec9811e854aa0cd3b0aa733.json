{"ast": null, "code": "import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an SVG element in a way\n * that works across iframes\n */\nfunction isSVGElement(element) {\n  return isObject(element) && \"ownerSVGElement\" in element;\n}\nexport { isSVGElement };", "map": {"version": 3, "names": ["isObject", "isSVGElement", "element"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/is-svg-element.mjs"], "sourcesContent": ["import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an SVG element in a way\n * that works across iframes\n */\nfunction isSVGElement(element) {\n    return isObject(element) && \"ownerSVGElement\" in element;\n}\n\nexport { isSVGElement };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;;AAEvC;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC3B,OAAOF,QAAQ,CAACE,OAAO,CAAC,IAAI,iBAAiB,IAAIA,OAAO;AAC5D;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}