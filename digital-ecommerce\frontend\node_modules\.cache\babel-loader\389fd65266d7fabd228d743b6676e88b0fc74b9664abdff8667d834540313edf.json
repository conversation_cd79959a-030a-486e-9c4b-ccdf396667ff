{"ast": null, "code": "import { isSVGElement } from './is-svg-element.mjs';\n\n/**\n * Checks if an element is specifically an SVGSVGElement (the root SVG element)\n * in a way that works across iframes\n */\nfunction isSVGSVGElement(element) {\n  return isSVGElement(element) && element.tagName === \"svg\";\n}\nexport { isSVGSVGElement };", "map": {"version": 3, "names": ["isSVGElement", "isSVGSVGElement", "element", "tagName"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs"], "sourcesContent": ["import { isSVGElement } from './is-svg-element.mjs';\n\n/**\n * Checks if an element is specifically an SVGSVGElement (the root SVG element)\n * in a way that works across iframes\n */\nfunction isSVGSVGElement(element) {\n    return isSVGElement(element) && element.tagName === \"svg\";\n}\n\nexport { isSVGSVGElement };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,sBAAsB;;AAEnD;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,OAAOF,YAAY,CAACE,OAAO,CAAC,IAAIA,OAAO,CAACC,OAAO,KAAK,KAAK;AAC7D;AAEA,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}