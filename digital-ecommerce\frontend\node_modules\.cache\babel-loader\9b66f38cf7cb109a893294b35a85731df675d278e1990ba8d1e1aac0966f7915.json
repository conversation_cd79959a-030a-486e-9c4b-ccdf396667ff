{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ModernNavigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst ModernNavigation = () => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigation = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon,\n    description: 'Welcome to ShopHub',\n    color: 'text-blue-600'\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon,\n    description: 'Browse all products',\n    color: 'text-purple-600',\n    megaMenu: true,\n    categories: [{\n      name: 'Electronics',\n      href: '/products?category=electronics',\n      icon: '💻',\n      description: 'Laptops, phones, tablets & more',\n      featured: true\n    }, {\n      name: 'Software',\n      href: '/products?category=software',\n      icon: '💿',\n      description: 'Professional software & licenses'\n    }, {\n      name: 'Gaming',\n      href: '/products?category=gaming',\n      icon: '🎮',\n      description: 'Gaming gear & accessories',\n      featured: true\n    }, {\n      name: 'Accessories',\n      href: '/products?category=accessories',\n      icon: '🎧',\n      description: 'Headphones, keyboards & more'\n    }],\n    featuredProducts: [{\n      name: 'MacBook Pro M3',\n      price: '$1,999',\n      image: '/api/placeholder/100/100'\n    }, {\n      name: 'Gaming Headset',\n      price: '$199',\n      image: '/api/placeholder/100/100'\n    }]\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon,\n    description: 'Instant downloads',\n    color: 'text-green-600',\n    badge: {\n      text: 'Instant',\n      color: 'bg-green-500'\n    }\n  }, {\n    name: 'PC Gaming',\n    href: '/pc-gaming',\n    icon: TagIcon,\n    description: 'Gaming hardware & software',\n    color: 'text-red-600',\n    badge: {\n      text: 'Hot',\n      color: 'bg-red-500'\n    }\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon,\n    description: 'Learn about us',\n    color: 'text-gray-600'\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon,\n    description: 'Get in touch',\n    color: 'text-indigo-600'\n  }];\n  const userNavigation = [{\n    name: 'Your Profile',\n    href: '/account',\n    icon: UserIcon\n  }, {\n    name: 'Order History',\n    href: '/orders',\n    icon: ShoppingBagIcon\n  }, {\n    name: 'Wishlist',\n    href: '/wishlist',\n    icon: HeartIcon\n  }, {\n    name: 'Settings',\n    href: '/settings',\n    icon: Cog6ToothIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Disclosure, {\n      as: \"nav\",\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${isScrolled ? 'backdrop-blur-xl shadow-xl border-b' : 'backdrop-blur-sm'}`,\n      style: {\n        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',\n        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',\n        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none',\n        minHeight: '80px'\n      },\n      children: ({\n        open\n      }) => /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex h-20 items-center justify-between gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center flex-shrink-0 w-48\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"flex items-center space-x-3 group\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    rotate: 360,\n                    scale: 1.1\n                  },\n                  transition: {\n                    duration: 0.6,\n                    type: \"spring\",\n                    stiffness: 200\n                  },\n                  className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                    className: \"w-7 h-7 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold transition-all duration-300 theme-transition whitespace-nowrap\",\n                    style: {\n                      color: 'var(--text-primary)'\n                    },\n                    children: \"ShopHub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs font-medium transition-all duration-300 theme-transition whitespace-nowrap\",\n                    style: {\n                      color: 'var(--accent-primary)'\n                    },\n                    children: \"Premium Store\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:flex flex-1 justify-center max-w-2xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: navigation.map(item => {\n                  var _item$categories;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    whileHover: {\n                      y: -2\n                    },\n                    transition: {\n                      duration: 0.2\n                    },\n                    className: \"relative group\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: item.href,\n                      className: classNames(isActive(item.href) ? 'text-white shadow-lg gaming-glow' : 'hover:shadow-lg', 'relative flex items-center space-x-2 px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition group whitespace-nowrap'),\n                      style: {\n                        backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'var(--bg-secondary)',\n                        color: isActive(item.href) ? 'white' : 'var(--text-primary)',\n                        borderColor: 'var(--border-primary)',\n                        border: '1px solid'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                        className: `w-4 h-4 ${isActive(item.href) ? 'text-white' : item.color || 'text-gray-500'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"relative z-10 font-medium\",\n                        children: item.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 27\n                      }, this), item.badge && /*#__PURE__*/_jsxDEV(motion.span, {\n                        initial: {\n                          scale: 0\n                        },\n                        animate: {\n                          scale: 1\n                        },\n                        className: `ml-1 px-1.5 py-0.5 text-xs font-bold rounded-full text-white ${item.badge.color || 'bg-red-500'} pulse-glow`,\n                        children: item.badge.text || item.badge\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 207,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 27\n                      }, this), isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                        layoutId: \"activeTab\",\n                        className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-white\",\n                        initial: false,\n                        transition: {\n                          type: \"spring\",\n                          stiffness: 500,\n                          damping: 30\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this), item.megaMenu && /*#__PURE__*/_jsxDEV(motion.div, {\n                      initial: {\n                        opacity: 0,\n                        y: 10,\n                        scale: 0.95\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                      },\n                      exit: {\n                        opacity: 0,\n                        y: 10,\n                        scale: 0.95\n                      },\n                      transition: {\n                        duration: 0.2\n                      },\n                      className: \"absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-[600px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"rounded-3xl shadow-2xl border backdrop-blur-xl theme-transition overflow-hidden\",\n                        style: {\n                          backgroundColor: 'var(--bg-primary)',\n                          borderColor: 'var(--border-primary)',\n                          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-6 border-b theme-transition\",\n                          style: {\n                            borderBottomColor: 'var(--border-primary)',\n                            background: 'linear-gradient(135deg, var(--accent-primary), var(--accent-secondary))'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Product Categories\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 252,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/80 text-sm\",\n                            children: \"Discover our premium collection of tech products\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 255,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 247,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-6\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-6\",\n                            children: (_item$categories = item.categories) === null || _item$categories === void 0 ? void 0 : _item$categories.map(category => /*#__PURE__*/_jsxDEV(motion.div, {\n                              whileHover: {\n                                scale: 1.02,\n                                y: -2\n                              },\n                              transition: {\n                                duration: 0.2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: category.href,\n                                className: \"block p-4 rounded-2xl transition-all duration-300 gaming-glow group/item\",\n                                style: {\n                                  backgroundColor: 'var(--bg-secondary)',\n                                  border: '1px solid var(--border-primary)'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"flex items-center space-x-3 mb-2\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-3xl\",\n                                    children: category.icon\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 277,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"font-semibold theme-transition flex items-center\",\n                                      style: {\n                                        color: 'var(--text-primary)'\n                                      },\n                                      children: [category.name, category.featured && /*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"ml-2 px-2 py-0.5 text-xs font-bold rounded-full bg-yellow-400 text-yellow-900\",\n                                        children: \"Featured\"\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 283,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 279,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"text-xs theme-transition mt-1\",\n                                      style: {\n                                        color: 'var(--text-secondary)'\n                                      },\n                                      children: category.description\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 288,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 278,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 276,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"flex items-center justify-between\",\n                                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs font-medium theme-transition\",\n                                    style: {\n                                      color: 'var(--accent-primary)'\n                                    },\n                                    children: \"Explore \\u2192\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 297,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 296,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 268,\n                                columnNumber: 39\n                              }, this)\n                            }, category.name, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 263,\n                              columnNumber: 37\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 261,\n                            columnNumber: 33\n                          }, this), item.featuredProducts && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"border-t pt-4 theme-transition\",\n                            style: {\n                              borderTopColor: 'var(--border-primary)'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                              className: \"font-semibold mb-3 theme-transition\",\n                              style: {\n                                color: 'var(--text-primary)'\n                              },\n                              children: \"Featured Products\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 311,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"grid grid-cols-2 gap-3\",\n                              children: item.featuredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center space-x-3 p-3 rounded-xl transition-colors\",\n                                style: {\n                                  backgroundColor: 'var(--bg-tertiary)'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: product.image,\n                                  alt: product.name,\n                                  className: \"w-12 h-12 rounded-lg object-cover\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 320,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"flex-1 min-w-0\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                                    className: \"text-sm font-medium truncate theme-transition\",\n                                    style: {\n                                      color: 'var(--text-primary)'\n                                    },\n                                    children: product.name\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 326,\n                                    columnNumber: 45\n                                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                    className: \"text-sm font-bold theme-transition\",\n                                    style: {\n                                      color: 'var(--accent-primary)'\n                                    },\n                                    children: product.price\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 330,\n                                    columnNumber: 45\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 325,\n                                  columnNumber: 43\n                                }, this)]\n                              }, index, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 317,\n                                columnNumber: 41\n                              }, this))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 315,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 309,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 260,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 27\n                    }, this)]\n                  }, item.name, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center flex-shrink-0 w-96\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"relative w-full group\",\n                whileHover: {\n                  scale: 1.01\n                },\n                transition: {\n                  duration: 0.2\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none z-10\",\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: searchQuery ? 360 : 0\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                      className: \"h-5 w-5 transition-colors duration-300\",\n                      style: {\n                        color: searchQuery ? 'var(--accent-primary)' : 'var(--text-secondary)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search products...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: \"w-full pl-12 pr-16 py-3 rounded-xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition gaming-glow\",\n                  style: {\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: searchQuery ? 'var(--accent-primary)' : 'var(--border-primary)',\n                    color: 'var(--text-primary)',\n                    fontSize: '14px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 right-0 pr-4 flex items-center space-x-2\",\n                  children: [searchQuery && /*#__PURE__*/_jsxDEV(motion.button, {\n                    initial: {\n                      scale: 0\n                    },\n                    animate: {\n                      scale: 1\n                    },\n                    onClick: () => setSearchQuery(''),\n                    className: \"p-1 rounded-full transition-colors\",\n                    style: {\n                      color: 'var(--text-secondary)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M6 18L18 6M6 6l12 12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-6 w-px bg-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"kbd\", {\n                    className: \"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded-lg border theme-transition\",\n                    style: {\n                      backgroundColor: 'var(--bg-tertiary)',\n                      borderColor: 'var(--border-secondary)',\n                      color: 'var(--text-muted)'\n                    },\n                    children: \"\\u2318K\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), searchQuery && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  className: \"absolute top-full left-0 right-0 mt-3 rounded-2xl shadow-2xl border backdrop-blur-xl z-50 theme-transition overflow-hidden\",\n                  style: {\n                    backgroundColor: 'var(--bg-primary)',\n                    borderColor: 'var(--border-primary)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-semibold mb-3 theme-transition flex items-center\",\n                        style: {\n                          color: 'var(--text-primary)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                          className: \"w-4 h-4 mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 31\n                        }, this), \"Quick Suggestions\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: ['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets', 'Wireless Keyboards'].map(suggestion => /*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            x: 4\n                          },\n                          className: \"w-full text-left px-4 py-3 rounded-xl transition-all duration-200 gaming-glow group\",\n                          style: {\n                            backgroundColor: 'var(--bg-secondary)',\n                            color: 'var(--text-primary)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"font-medium\",\n                              children: suggestion\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 443,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity\",\n                              fill: \"none\",\n                              stroke: \"currentColor\",\n                              viewBox: \"0 0 24 24\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5l7 7-7 7\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 446,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 444,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 442,\n                            columnNumber: 35\n                          }, this)\n                        }, suggestion, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 433,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border-t pt-4 theme-transition\",\n                      style: {\n                        borderTopColor: 'var(--border-primary)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-semibold mb-3 theme-transition\",\n                        style: {\n                          color: 'var(--text-primary)'\n                        },\n                        children: \"Popular Categories\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 457,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: ['Electronics', 'Gaming', 'Software', 'Accessories'].map(category => /*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          className: \"px-3 py-2 rounded-lg text-sm font-medium transition-all\",\n                          style: {\n                            backgroundColor: 'var(--accent-primary)',\n                            color: 'white'\n                          },\n                          children: category\n                        }, category, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 463,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 461,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: /*#__PURE__*/_jsxDEV(ThemeToggle, {\n                  className: \"theme-toggle-desktop\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/wishlist\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1,\n                    y: -2\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"relative p-3 rounded-2xl transition-all duration-300 gaming-glow\",\n                  style: {\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-primary)',\n                    border: '1px solid',\n                    color: 'var(--text-primary)'\n                  },\n                  title: \"Wishlist\",\n                  children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    initial: {\n                      scale: 0\n                    },\n                    animate: {\n                      scale: 1\n                    },\n                    className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center pulse-glow\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"relative\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Menu, {\n                as: \"div\",\n                className: \"relative ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Menu.Button, {\n                    className: `relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"sr-only\",\n                      children: \"Open user menu\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 27\n                    }, this), user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"h-8 w-8 rounded-full ring-2 ring-white/20\",\n                      src: user.profilePicture,\n                      alt: \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden md:block text-sm font-medium\",\n                      children: (user === null || user === void 0 ? void 0 : user.firstName) || 'Account'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Transition, {\n                  as: Fragment,\n                  enter: \"transition ease-out duration-100\",\n                  enterFrom: \"transform opacity-0 scale-95\",\n                  enterTo: \"transform opacity-100 scale-100\",\n                  leave: \"transition ease-in duration-75\",\n                  leaveFrom: \"transform opacity-100 scale-100\",\n                  leaveTo: \"transform opacity-0 scale-95\",\n                  children: /*#__PURE__*/_jsxDEV(Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"w-12 h-12 rounded-full ring-2 ring-white/30\",\n                          src: user.profilePicture,\n                          alt: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 570,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                            className: \"w-6 h-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 577,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 576,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"font-semibold text-white\",\n                            children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 581,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/80\",\n                            children: user === null || user === void 0 ? void 0 : user.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 584,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 580,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"py-2\",\n                      children: [userNavigation.map(item => /*#__PURE__*/_jsxDEV(Menu.Item, {\n                        children: ({\n                          active\n                        }) => /*#__PURE__*/_jsxDEV(Link, {\n                          to: item.href,\n                          className: classNames(active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700', 'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'),\n                          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                            className: \"w-5 h-5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 601,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: item.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 594,\n                          columnNumber: 35\n                        }, this)\n                      }, item.name, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-100 mt-2 pt-2\",\n                        children: /*#__PURE__*/_jsxDEV(Menu.Item, {\n                          children: ({\n                            active\n                          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: logout,\n                            className: classNames(active ? 'bg-red-50 text-red-600' : 'text-red-600', 'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'),\n                            children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                              className: \"w-5 h-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 617,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Sign out\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 618,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 610,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 608,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: `px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'}`,\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                  className: `relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'} focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: open ? 180 : 0\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: open ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Disclosure.Panel, {\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto',\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeInOut\"\n            },\n            className: \"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1 px-6 pb-6 pt-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.1\n                },\n                className: \"relative mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search for products...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: \"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: navigation.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.1 * (index + 2)\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: Link,\n                    to: item.href,\n                    className: classNames(isActive(item.href) ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg' : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600', 'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'),\n                    children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                      className: classNames(isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500', 'w-6 h-6')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-lg\",\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this)\n                }, item.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 19\n              }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.4\n                },\n                className: \"flex space-x-4 pt-6\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\",\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernNavigation, \"nqhB+dQNtp/nfK+k0r5VlnahLcE=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = ModernNavigation;\nexport default ModernNavigation;\nvar _c;\n$RefreshReg$(_c, \"ModernNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "Link", "useLocation", "motion", "Disclosure", "<PERSON><PERSON>", "Transition", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "Cog6ToothIcon", "ArrowRightOnRectangleIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "ModernNavigation", "_s", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "window", "href", "encodeURIComponent", "handleScroll", "scrollY", "addEventListener", "removeEventListener", "navigation", "name", "icon", "description", "color", "megaMenu", "categories", "featured", "featuredProducts", "price", "image", "badge", "text", "userNavigation", "isActive", "path", "pathname", "children", "as", "className", "style", "backgroundColor", "borderBottomColor", "boxShadow", "minHeight", "open", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "_item$categories", "y", "borderColor", "border", "span", "initial", "animate", "layoutId", "damping", "opacity", "exit", "background", "category", "borderTopColor", "product", "index", "src", "alt", "placeholder", "value", "onChange", "target", "onKeyDown", "key", "fontSize", "button", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "suggestion", "x", "whileTap", "ThemeToggle", "title", "<PERSON><PERSON>", "profilePicture", "firstName", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Items", "lastName", "email", "<PERSON><PERSON>", "active", "Panel", "height", "ease", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ModernNavigation.js"], "sourcesContent": ["import React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst ModernNavigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    {\n      name: 'Home',\n      href: '/',\n      icon: HomeIcon,\n      description: 'Welcome to ShopHub',\n      color: 'text-blue-600'\n    },\n    {\n      name: 'Products',\n      href: '/products',\n      icon: TagIcon,\n      description: 'Browse all products',\n      color: 'text-purple-600',\n      megaMenu: true,\n      categories: [\n        {\n          name: 'Electronics',\n          href: '/products?category=electronics',\n          icon: '💻',\n          description: 'Laptops, phones, tablets & more',\n          featured: true\n        },\n        {\n          name: 'Software',\n          href: '/products?category=software',\n          icon: '💿',\n          description: 'Professional software & licenses'\n        },\n        {\n          name: 'Gaming',\n          href: '/products?category=gaming',\n          icon: '🎮',\n          description: 'Gaming gear & accessories',\n          featured: true\n        },\n        {\n          name: 'Accessories',\n          href: '/products?category=accessories',\n          icon: '🎧',\n          description: 'Headphones, keyboards & more'\n        }\n      ],\n      featuredProducts: [\n        { name: 'MacBook Pro M3', price: '$1,999', image: '/api/placeholder/100/100' },\n        { name: 'Gaming Headset', price: '$199', image: '/api/placeholder/100/100' }\n      ]\n    },\n    {\n      name: 'Digital',\n      href: '/digital-products',\n      icon: TagIcon,\n      description: 'Instant downloads',\n      color: 'text-green-600',\n      badge: { text: 'Instant', color: 'bg-green-500' }\n    },\n    {\n      name: 'PC Gaming',\n      href: '/pc-gaming',\n      icon: TagIcon,\n      description: 'Gaming hardware & software',\n      color: 'text-red-600',\n      badge: { text: 'Hot', color: 'bg-red-500' }\n    },\n    {\n      name: 'About',\n      href: '/about',\n      icon: InformationCircleIcon,\n      description: 'Learn about us',\n      color: 'text-gray-600'\n    },\n    {\n      name: 'Contact',\n      href: '/contact',\n      icon: PhoneIcon,\n      description: 'Get in touch',\n      color: 'text-indigo-600'\n    }\n  ];\n\n  const userNavigation = [\n    { name: 'Your Profile', href: '/account', icon: UserIcon },\n    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },\n    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },\n    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <Disclosure as=\"nav\" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${\n        isScrolled\n          ? 'backdrop-blur-xl shadow-xl border-b'\n          : 'backdrop-blur-sm'\n      }`}\n      style={{\n        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',\n        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',\n        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none',\n        minHeight: '80px'\n      }}>\n        {({ open }) => (\n          <>\n            <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n              <div className=\"flex h-20 items-center justify-between gap-8\">\n                {/* Logo - Fixed Width */}\n                <div className=\"flex items-center flex-shrink-0 w-48\">\n                  <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n                    <motion.div\n                      whileHover={{ rotate: 360, scale: 1.1 }}\n                      transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                      className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                    >\n                      <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n                    </motion.div>\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-2xl font-bold transition-all duration-300 theme-transition whitespace-nowrap\"\n                            style={{ color: 'var(--text-primary)' }}>\n                        ShopHub\n                      </span>\n                      <span className=\"text-xs font-medium transition-all duration-300 theme-transition whitespace-nowrap\"\n                            style={{ color: 'var(--accent-primary)' }}>\n                        Premium Store\n                      </span>\n                    </div>\n                  </Link>\n                </div>\n\n                {/* Enhanced Desktop Navigation - Flex Grow */}\n                <div className=\"hidden lg:flex flex-1 justify-center max-w-2xl\">\n                  <div className=\"flex items-center space-x-1\">\n                    {navigation.map((item) => (\n                      <motion.div\n                        key={item.name}\n                        whileHover={{ y: -2 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"relative group\"\n                      >\n                        <Link\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'text-white shadow-lg gaming-glow'\n                              : 'hover:shadow-lg',\n                            'relative flex items-center space-x-2 px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition group whitespace-nowrap'\n                          )}\n                          style={{\n                            backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'var(--bg-secondary)',\n                            color: isActive(item.href) ? 'white' : 'var(--text-primary)',\n                            borderColor: 'var(--border-primary)',\n                            border: '1px solid'\n                          }}\n                        >\n                          <item.icon className={`w-4 h-4 ${isActive(item.href) ? 'text-white' : item.color || 'text-gray-500'}`} />\n                          <span className=\"relative z-10 font-medium\">{item.name}</span>\n                          {item.badge && (\n                            <motion.span\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              className={`ml-1 px-1.5 py-0.5 text-xs font-bold rounded-full text-white ${item.badge.color || 'bg-red-500'} pulse-glow`}\n                            >\n                              {item.badge.text || item.badge}\n                            </motion.span>\n                          )}\n\n                          {/* Hover Effect */}\n                          <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n\n                          {/* Active Indicator */}\n                          {isActive(item.href) && (\n                            <motion.div\n                              layoutId=\"activeTab\"\n                              className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-white\"\n                              initial={false}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                            />\n                          )}\n                        </Link>\n\n                        {/* Enhanced Mega Menu for Products */}\n                        {item.megaMenu && (\n                          <motion.div\n                            initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                            animate={{ opacity: 1, y: 0, scale: 1 }}\n                            exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                            transition={{ duration: 0.2 }}\n                            className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-[600px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50\"\n                          >\n                            <div className=\"rounded-3xl shadow-2xl border backdrop-blur-xl theme-transition overflow-hidden\"\n                                 style={{\n                                   backgroundColor: 'var(--bg-primary)',\n                                   borderColor: 'var(--border-primary)',\n                                   boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)'\n                                 }}>\n\n                              {/* Header */}\n                              <div className=\"p-6 border-b theme-transition\"\n                                   style={{\n                                     borderBottomColor: 'var(--border-primary)',\n                                     background: 'linear-gradient(135deg, var(--accent-primary), var(--accent-secondary))'\n                                   }}>\n                                <h3 className=\"text-xl font-bold text-white mb-2\">\n                                  Product Categories\n                                </h3>\n                                <p className=\"text-white/80 text-sm\">\n                                  Discover our premium collection of tech products\n                                </p>\n                              </div>\n\n                              <div className=\"p-6\">\n                                <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                                  {item.categories?.map((category) => (\n                                    <motion.div\n                                      key={category.name}\n                                      whileHover={{ scale: 1.02, y: -2 }}\n                                      transition={{ duration: 0.2 }}\n                                    >\n                                      <Link\n                                        to={category.href}\n                                        className=\"block p-4 rounded-2xl transition-all duration-300 gaming-glow group/item\"\n                                        style={{\n                                          backgroundColor: 'var(--bg-secondary)',\n                                          border: '1px solid var(--border-primary)'\n                                        }}\n                                      >\n                                        <div className=\"flex items-center space-x-3 mb-2\">\n                                          <span className=\"text-3xl\">{category.icon}</span>\n                                          <div className=\"flex-1\">\n                                            <div className=\"font-semibold theme-transition flex items-center\"\n                                                 style={{ color: 'var(--text-primary)' }}>\n                                              {category.name}\n                                              {category.featured && (\n                                                <span className=\"ml-2 px-2 py-0.5 text-xs font-bold rounded-full bg-yellow-400 text-yellow-900\">\n                                                  Featured\n                                                </span>\n                                              )}\n                                            </div>\n                                            <div className=\"text-xs theme-transition mt-1\"\n                                                 style={{ color: 'var(--text-secondary)' }}>\n                                              {category.description}\n                                            </div>\n                                          </div>\n                                        </div>\n\n                                        {/* Hover Arrow */}\n                                        <div className=\"flex items-center justify-between\">\n                                          <span className=\"text-xs font-medium theme-transition\"\n                                                style={{ color: 'var(--accent-primary)' }}>\n                                            Explore →\n                                          </span>\n                                        </div>\n                                      </Link>\n                                    </motion.div>\n                                  ))}\n                                </div>\n\n                                {/* Featured Products Section */}\n                                {item.featuredProducts && (\n                                  <div className=\"border-t pt-4 theme-transition\"\n                                       style={{ borderTopColor: 'var(--border-primary)' }}>\n                                    <h4 className=\"font-semibold mb-3 theme-transition\"\n                                        style={{ color: 'var(--text-primary)' }}>\n                                      Featured Products\n                                    </h4>\n                                    <div className=\"grid grid-cols-2 gap-3\">\n                                      {item.featuredProducts.map((product, index) => (\n                                        <div key={index}\n                                             className=\"flex items-center space-x-3 p-3 rounded-xl transition-colors\"\n                                             style={{ backgroundColor: 'var(--bg-tertiary)' }}>\n                                          <img\n                                            src={product.image}\n                                            alt={product.name}\n                                            className=\"w-12 h-12 rounded-lg object-cover\"\n                                          />\n                                          <div className=\"flex-1 min-w-0\">\n                                            <p className=\"text-sm font-medium truncate theme-transition\"\n                                               style={{ color: 'var(--text-primary)' }}>\n                                              {product.name}\n                                            </p>\n                                            <p className=\"text-sm font-bold theme-transition\"\n                                               style={{ color: 'var(--accent-primary)' }}>\n                                              {product.price}\n                                            </p>\n                                          </div>\n                                        </div>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          </motion.div>\n                        )}\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Premium Search Bar - Fixed Width */}\n                <div className=\"hidden md:flex items-center flex-shrink-0 w-96\">\n                  <motion.div\n                    className=\"relative w-full group\"\n                    whileHover={{ scale: 1.01 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    {/* Search Icon */}\n                    <div className=\"absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none z-10\">\n                      <motion.div\n                        animate={{ rotate: searchQuery ? 360 : 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <MagnifyingGlassIcon className=\"h-5 w-5 transition-colors duration-300\"\n                                             style={{ color: searchQuery ? 'var(--accent-primary)' : 'var(--text-secondary)' }} />\n                      </motion.div>\n                    </div>\n\n                    {/* Search Input */}\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search products...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-16 py-3 rounded-xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition gaming-glow\"\n                      style={{\n                        backgroundColor: 'var(--bg-secondary)',\n                        borderColor: searchQuery ? 'var(--accent-primary)' : 'var(--border-primary)',\n                        color: 'var(--text-primary)',\n                        fontSize: '14px'\n                      }}\n                    />\n\n                    {/* Right Side Actions */}\n                    <div className=\"absolute inset-y-0 right-0 pr-4 flex items-center space-x-2\">\n                      {searchQuery && (\n                        <motion.button\n                          initial={{ scale: 0 }}\n                          animate={{ scale: 1 }}\n                          onClick={() => setSearchQuery('')}\n                          className=\"p-1 rounded-full transition-colors\"\n                          style={{ color: 'var(--text-secondary)' }}\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                          </svg>\n                        </motion.button>\n                      )}\n\n                      <div className=\"h-6 w-px bg-gray-300\"></div>\n\n                      <kbd className=\"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded-lg border theme-transition\"\n                           style={{\n                             backgroundColor: 'var(--bg-tertiary)',\n                             borderColor: 'var(--border-secondary)',\n                             color: 'var(--text-muted)'\n                           }}>\n                        ⌘K\n                      </kbd>\n                    </div>\n\n                    {/* Enhanced Search Suggestions */}\n                    {searchQuery && (\n                      <motion.div\n                        initial={{ opacity: 0, y: 10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: 10 }}\n                        className=\"absolute top-full left-0 right-0 mt-3 rounded-2xl shadow-2xl border backdrop-blur-xl z-50 theme-transition overflow-hidden\"\n                        style={{\n                          backgroundColor: 'var(--bg-primary)',\n                          borderColor: 'var(--border-primary)'\n                        }}\n                      >\n                        <div className=\"p-6\">\n                          {/* Quick Suggestions */}\n                          <div className=\"mb-4\">\n                            <div className=\"text-sm font-semibold mb-3 theme-transition flex items-center\"\n                                 style={{ color: 'var(--text-primary)' }}>\n                              <MagnifyingGlassIcon className=\"w-4 h-4 mr-2\" />\n                              Quick Suggestions\n                            </div>\n                            <div className=\"space-y-2\">\n                              {['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets', 'Wireless Keyboards'].map((suggestion) => (\n                                <motion.button\n                                  key={suggestion}\n                                  whileHover={{ x: 4 }}\n                                  className=\"w-full text-left px-4 py-3 rounded-xl transition-all duration-200 gaming-glow group\"\n                                  style={{\n                                    backgroundColor: 'var(--bg-secondary)',\n                                    color: 'var(--text-primary)'\n                                  }}\n                                >\n                                  <div className=\"flex items-center justify-between\">\n                                    <span className=\"font-medium\">{suggestion}</span>\n                                    <svg className=\"w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                         fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                                    </svg>\n                                  </div>\n                                </motion.button>\n                              ))}\n                            </div>\n                          </div>\n\n                          {/* Popular Categories */}\n                          <div className=\"border-t pt-4 theme-transition\"\n                               style={{ borderTopColor: 'var(--border-primary)' }}>\n                            <div className=\"text-sm font-semibold mb-3 theme-transition\"\n                                 style={{ color: 'var(--text-primary)' }}>\n                              Popular Categories\n                            </div>\n                            <div className=\"flex flex-wrap gap-2\">\n                              {['Electronics', 'Gaming', 'Software', 'Accessories'].map((category) => (\n                                <motion.button\n                                  key={category}\n                                  whileHover={{ scale: 1.05 }}\n                                  whileTap={{ scale: 0.95 }}\n                                  className=\"px-3 py-2 rounded-lg text-sm font-medium transition-all\"\n                                  style={{\n                                    backgroundColor: 'var(--accent-primary)',\n                                    color: 'white'\n                                  }}\n                                >\n                                  {category}\n                                </motion.button>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </motion.div>\n                </div>\n\n                {/* Enhanced Right Side Actions */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Theme Toggle */}\n                  <motion.div\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <ThemeToggle className=\"theme-toggle-desktop\" />\n                  </motion.div>\n\n                  {/* Wishlist */}\n                  <Link to=\"/wishlist\">\n                    <motion.button\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"relative p-3 rounded-2xl transition-all duration-300 gaming-glow\"\n                      style={{\n                        backgroundColor: 'var(--bg-secondary)',\n                        borderColor: 'var(--border-primary)',\n                        border: '1px solid',\n                        color: 'var(--text-primary)'\n                      }}\n                      title=\"Wishlist\"\n                    >\n                      <HeartIcon className=\"w-5 h-5\" />\n                      {/* Wishlist Count Badge */}\n                      <motion.span\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center pulse-glow\"\n                      >\n                        3\n                      </motion.span>\n                    </motion.button>\n                  </Link>\n\n                  {/* Shopping Cart */}\n                  <motion.div\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"relative\"\n                  >\n                    <ShoppingCart />\n                  </motion.div>\n\n                  {/* User menu */}\n                  {isAuthenticated ? (\n                    <Menu as=\"div\" className=\"relative ml-3\">\n                      <div>\n                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                          isScrolled\n                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'\n                        }`}>\n                          <span className=\"sr-only\">Open user menu</span>\n                          {user?.profilePicture ? (\n                            <img\n                              className=\"h-8 w-8 rounded-full ring-2 ring-white/20\"\n                              src={user.profilePicture}\n                              alt=\"\"\n                            />\n                          ) : (\n                            <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                              <UserIcon className=\"w-5 h-5 text-white\" />\n                            </div>\n                          )}\n                          <span className=\"hidden md:block text-sm font-medium\">\n                            {user?.firstName || 'Account'}\n                          </span>\n                          <ChevronDownIcon className=\"w-4 h-4\" />\n                        </Menu.Button>\n                      </div>\n                      <Transition\n                        as={Fragment}\n                        enter=\"transition ease-out duration-100\"\n                        enterFrom=\"transform opacity-0 scale-95\"\n                        enterTo=\"transform opacity-100 scale-100\"\n                        leave=\"transition ease-in duration-75\"\n                        leaveFrom=\"transform opacity-100 scale-100\"\n                        leaveTo=\"transform opacity-0 scale-95\"\n                      >\n                        <Menu.Items className=\"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\">\n                          {/* User info header */}\n                          <div className=\"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                            <div className=\"flex items-center space-x-3\">\n                              {user?.profilePicture ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full ring-2 ring-white/30\"\n                                  src={user.profilePicture}\n                                  alt=\"\"\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                  <UserIcon className=\"w-6 h-6 text-white\" />\n                                </div>\n                              )}\n                              <div>\n                                <p className=\"font-semibold text-white\">\n                                  {user?.firstName} {user?.lastName}\n                                </p>\n                                <p className=\"text-sm text-white/80\">{user?.email}</p>\n                              </div>\n                            </div>\n                          </div>\n                          \n                          {/* Menu items */}\n                          <div className=\"py-2\">\n                            {userNavigation.map((item) => (\n                              <Menu.Item key={item.name}>\n                                {({ active }) => (\n                                  <Link\n                                    to={item.href}\n                                    className={classNames(\n                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',\n                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <item.icon className=\"w-5 h-5\" />\n                                    <span>{item.name}</span>\n                                  </Link>\n                                )}\n                              </Menu.Item>\n                            ))}\n                            <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                              <Menu.Item>\n                                {({ active }) => (\n                                  <button\n                                    onClick={logout}\n                                    className={classNames(\n                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',\n                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n                                    <span>Sign out</span>\n                                  </button>\n                                )}\n                              </Menu.Item>\n                            </div>\n                          </div>\n                        </Menu.Items>\n                      </Transition>\n                    </Menu>\n                  ) : (\n                    <div className=\"flex items-center space-x-3\">\n                      <Link to=\"/login\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${\n                            isScrolled\n                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'\n                              : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'\n                          }`}\n                        >\n                          Sign In\n                        </motion.button>\n                      </Link>\n                      <Link to=\"/register\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                        >\n                          Sign Up\n                        </motion.button>\n                      </Link>\n                    </div>\n                  )}\n\n                  {/* Mobile menu button */}\n                  <div className=\"lg:hidden\">\n                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'\n                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>\n                      <span className=\"sr-only\">Open main menu</span>\n                      <motion.div\n                        animate={{ rotate: open ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {open ? (\n                          <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        ) : (\n                          <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        )}\n                      </motion.div>\n                    </Disclosure.Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu */}\n            <Disclosure.Panel className=\"lg:hidden\">\n              <motion.div\n                initial={{ opacity: 0, height: 0, y: -20 }}\n                animate={{ opacity: 1, height: 'auto', y: 0 }}\n                exit={{ opacity: 0, height: 0, y: -20 }}\n                transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                className=\"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\"\n              >\n                <div className=\"space-y-1 px-6 pb-6 pt-6\">\n                  {/* Mobile Search */}\n                  <motion.div \n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 }}\n                    className=\"relative mb-6\"\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                    />\n                  </motion.div>\n\n                  {/* Mobile Navigation */}\n                  <div className=\"space-y-3\">\n                    {navigation.map((item, index) => (\n                      <motion.div\n                        key={item.name}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: 0.1 * (index + 2) }}\n                      >\n                        <Disclosure.Button\n                          as={Link}\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'\n                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',\n                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'\n                          )}\n                        >\n                          <item.icon className={classNames(\n                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',\n                            'w-6 h-6'\n                          )} />\n                          <span className=\"font-semibold text-lg\">{item.name}</span>\n                        </Disclosure.Button>\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  {/* Mobile Auth Buttons */}\n                  {!isAuthenticated && (\n                    <motion.div \n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 }}\n                      className=\"flex space-x-4 pt-6\"\n                    >\n                      <Link to=\"/login\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\"\n                        >\n                          Sign In\n                        </Disclosure.Button>\n                      </Link>\n                      <Link to=\"/register\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\"\n                        >\n                          Sign Up\n                        </Disclosure.Button>\n                      </Link>\n                    </motion.div>\n                  )}\n                </div>\n              </motion.div>\n            </Disclosure.Panel>\n          </>\n        )}\n      </Disclosure>\n\n      {/* Spacer */}\n      <div className=\"h-20\"></div>\n    </>\n  );\n};\n\nexport default ModernNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,IAAI,EAAEC,UAAU,QAAQ,mBAAmB;AAChE,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,yBAAyB,QACpB,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAvB,QAAA,IAAAwB,SAAA;AAElD,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMsC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIR,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MACtBC,MAAM,CAACR,QAAQ,CAACS,IAAI,GAAG,oBAAoBC,kBAAkB,CAACZ,WAAW,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE;IACrF;EACF,CAAC;EAED5C,SAAS,CAAC,MAAM;IACd,MAAMgD,YAAY,GAAGA,CAAA,KAAM;MACzBd,aAAa,CAACW,MAAM,CAACI,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDJ,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMH,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,UAAU,GAAG,CACjB;IACEC,IAAI,EAAE,MAAM;IACZP,IAAI,EAAE,GAAG;IACTQ,IAAI,EAAExC,QAAQ;IACdyC,WAAW,EAAE,oBAAoB;IACjCC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBP,IAAI,EAAE,WAAW;IACjBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,qBAAqB;IAClCC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,CACV;MACEL,IAAI,EAAE,aAAa;MACnBP,IAAI,EAAE,gCAAgC;MACtCQ,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,iCAAiC;MAC9CI,QAAQ,EAAE;IACZ,CAAC,EACD;MACEN,IAAI,EAAE,UAAU;MAChBP,IAAI,EAAE,6BAA6B;MACnCQ,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,IAAI,EAAE,QAAQ;MACdP,IAAI,EAAE,2BAA2B;MACjCQ,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,2BAA2B;MACxCI,QAAQ,EAAE;IACZ,CAAC,EACD;MACEN,IAAI,EAAE,aAAa;MACnBP,IAAI,EAAE,gCAAgC;MACtCQ,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE;IACf,CAAC,CACF;IACDK,gBAAgB,EAAE,CAChB;MAAEP,IAAI,EAAE,gBAAgB;MAAEQ,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAA2B,CAAC,EAC9E;MAAET,IAAI,EAAE,gBAAgB;MAAEQ,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAA2B,CAAC;EAEhF,CAAC,EACD;IACET,IAAI,EAAE,SAAS;IACfP,IAAI,EAAE,mBAAmB;IACzBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,mBAAmB;IAChCC,KAAK,EAAE,gBAAgB;IACvBO,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAER,KAAK,EAAE;IAAe;EAClD,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBP,IAAI,EAAE,YAAY;IAClBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,4BAA4B;IACzCC,KAAK,EAAE,cAAc;IACrBO,KAAK,EAAE;MAAEC,IAAI,EAAE,KAAK;MAAER,KAAK,EAAE;IAAa;EAC5C,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbP,IAAI,EAAE,QAAQ;IACdQ,IAAI,EAAErC,qBAAqB;IAC3BsC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,SAAS;IACfP,IAAI,EAAE,UAAU;IAChBQ,IAAI,EAAEtC,SAAS;IACfuC,WAAW,EAAE,cAAc;IAC3BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMS,cAAc,GAAG,CACrB;IAAEZ,IAAI,EAAE,cAAc;IAAEP,IAAI,EAAE,UAAU;IAAEQ,IAAI,EAAE1C;EAAS,CAAC,EAC1D;IAAEyC,IAAI,EAAE,eAAe;IAAEP,IAAI,EAAE,SAAS;IAAEQ,IAAI,EAAE5C;EAAgB,CAAC,EACjE;IAAE2C,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEzC;EAAU,CAAC,EACxD;IAAEwC,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEnC;EAAc,CAAC,CAC7D;EAED,MAAM+C,QAAQ,GAAIC,IAAI,IAAK9B,QAAQ,CAAC+B,QAAQ,KAAKD,IAAI;EAErD,oBACE3C,OAAA,CAAAC,SAAA;IAAA4C,QAAA,gBACE7C,OAAA,CAACnB,UAAU;MAACiE,EAAE,EAAC,KAAK;MAACC,SAAS,EAAE,gFAC9BtC,UAAU,GACN,qCAAqC,GACrC,kBAAkB,EACrB;MACHuC,KAAK,EAAE;QACLC,eAAe,EAAExC,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;QACvEyC,iBAAiB,EAAEzC,UAAU,GAAG,uBAAuB,GAAG,aAAa;QACvE0C,SAAS,EAAE1C,UAAU,GAAG,kBAAkB,GAAG,MAAM;QACnD2C,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,EACCA,CAAC;QAAEQ;MAAK,CAAC,kBACRrD,OAAA,CAAAC,SAAA;QAAA4C,QAAA,gBACE7C,OAAA;UAAK+C,SAAS,EAAC,gCAAgC;UAAAF,QAAA,eAC7C7C,OAAA;YAAK+C,SAAS,EAAC,8CAA8C;YAAAF,QAAA,gBAE3D7C,OAAA;cAAK+C,SAAS,EAAC,sCAAsC;cAAAF,QAAA,eACnD7C,OAAA,CAACtB,IAAI;gBAAC4E,EAAE,EAAC,GAAG;gBAACP,SAAS,EAAC,mCAAmC;gBAAAF,QAAA,gBACxD7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;kBACTC,UAAU,EAAE;oBAAEC,MAAM,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBACxCC,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE;kBAAI,CAAE;kBAC9Df,SAAS,EAAC,2MAA2M;kBAAAF,QAAA,gBAErN7C,OAAA,CAACd,eAAe;oBAAC6D,SAAS,EAAC;kBAAoB;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDlE,OAAA;oBAAK+C,SAAS,EAAC;kBAA6E;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACblE,OAAA;kBAAK+C,SAAS,EAAC,eAAe;kBAAAF,QAAA,gBAC5B7C,OAAA;oBAAM+C,SAAS,EAAC,mFAAmF;oBAC7FC,KAAK,EAAE;sBAAEhB,KAAK,EAAE;oBAAsB,CAAE;oBAAAa,QAAA,EAAC;kBAE/C;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlE,OAAA;oBAAM+C,SAAS,EAAC,oFAAoF;oBAC9FC,KAAK,EAAE;sBAAEhB,KAAK,EAAE;oBAAwB,CAAE;oBAAAa,QAAA,EAAC;kBAEjD;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNlE,OAAA;cAAK+C,SAAS,EAAC,gDAAgD;cAAAF,QAAA,eAC7D7C,OAAA;gBAAK+C,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,EACzCjB,UAAU,CAACuC,GAAG,CAAEC,IAAI;kBAAA,IAAAC,gBAAA;kBAAA,oBACnBrE,OAAA,CAACpB,MAAM,CAAC2E,GAAG;oBAETC,UAAU,EAAE;sBAAEc,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACtBX,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAC9Bb,SAAS,EAAC,gBAAgB;oBAAAF,QAAA,gBAE1B7C,OAAA,CAACtB,IAAI;sBACH4E,EAAE,EAAEc,IAAI,CAAC9C,IAAK;sBACdyB,SAAS,EAAE7C,UAAU,CACnBwC,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GACf,kCAAkC,GAClC,iBAAiB,EACrB,wJACF,CAAE;sBACF0B,KAAK,EAAE;wBACLC,eAAe,EAAEP,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GAAG,uBAAuB,GAAG,qBAAqB;wBACtFU,KAAK,EAAEU,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GAAG,OAAO,GAAG,qBAAqB;wBAC5DiD,WAAW,EAAE,uBAAuB;wBACpCC,MAAM,EAAE;sBACV,CAAE;sBAAA3B,QAAA,gBAEF7C,OAAA,CAACoE,IAAI,CAACtC,IAAI;wBAACiB,SAAS,EAAE,WAAWL,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GAAG,YAAY,GAAG8C,IAAI,CAACpC,KAAK,IAAI,eAAe;sBAAG;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACzGlE,OAAA;wBAAM+C,SAAS,EAAC,2BAA2B;wBAAAF,QAAA,EAAEuB,IAAI,CAACvC;sBAAI;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC7DE,IAAI,CAAC7B,KAAK,iBACTvC,OAAA,CAACpB,MAAM,CAAC6F,IAAI;wBACVC,OAAO,EAAE;0BAAEhB,KAAK,EAAE;wBAAE,CAAE;wBACtBiB,OAAO,EAAE;0BAAEjB,KAAK,EAAE;wBAAE,CAAE;wBACtBX,SAAS,EAAE,gEAAgEqB,IAAI,CAAC7B,KAAK,CAACP,KAAK,IAAI,YAAY,aAAc;wBAAAa,QAAA,EAExHuB,IAAI,CAAC7B,KAAK,CAACC,IAAI,IAAI4B,IAAI,CAAC7B;sBAAK;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACd,eAGDlE,OAAA;wBAAK+C,SAAS,EAAC;sBAA4J;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAGjLxB,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,iBAClBtB,OAAA,CAACpB,MAAM,CAAC2E,GAAG;wBACTqB,QAAQ,EAAC,WAAW;wBACpB7B,SAAS,EAAC,qFAAqF;wBAC/F2B,OAAO,EAAE,KAAM;wBACff,UAAU,EAAE;0BAAEE,IAAI,EAAE,QAAQ;0BAAEC,SAAS,EAAE,GAAG;0BAAEe,OAAO,EAAE;wBAAG;sBAAE;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7D,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC,EAGNE,IAAI,CAACnC,QAAQ,iBACZjC,OAAA,CAACpB,MAAM,CAAC2E,GAAG;sBACTmB,OAAO,EAAE;wBAAEI,OAAO,EAAE,CAAC;wBAAER,CAAC,EAAE,EAAE;wBAAEZ,KAAK,EAAE;sBAAK,CAAE;sBAC5CiB,OAAO,EAAE;wBAAEG,OAAO,EAAE,CAAC;wBAAER,CAAC,EAAE,CAAC;wBAAEZ,KAAK,EAAE;sBAAE,CAAE;sBACxCqB,IAAI,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAER,CAAC,EAAE,EAAE;wBAAEZ,KAAK,EAAE;sBAAK,CAAE;sBACzCC,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI,CAAE;sBAC9Bb,SAAS,EAAC,uKAAuK;sBAAAF,QAAA,eAEjL7C,OAAA;wBAAK+C,SAAS,EAAC,iFAAiF;wBAC3FC,KAAK,EAAE;0BACLC,eAAe,EAAE,mBAAmB;0BACpCsB,WAAW,EAAE,uBAAuB;0BACpCpB,SAAS,EAAE;wBACb,CAAE;wBAAAN,QAAA,gBAGL7C,OAAA;0BAAK+C,SAAS,EAAC,+BAA+B;0BACzCC,KAAK,EAAE;4BACLE,iBAAiB,EAAE,uBAAuB;4BAC1C8B,UAAU,EAAE;0BACd,CAAE;0BAAAnC,QAAA,gBACL7C,OAAA;4BAAI+C,SAAS,EAAC,mCAAmC;4BAAAF,QAAA,EAAC;0BAElD;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLlE,OAAA;4BAAG+C,SAAS,EAAC,uBAAuB;4BAAAF,QAAA,EAAC;0BAErC;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eAENlE,OAAA;0BAAK+C,SAAS,EAAC,KAAK;0BAAAF,QAAA,gBAClB7C,OAAA;4BAAK+C,SAAS,EAAC,6BAA6B;4BAAAF,QAAA,GAAAwB,gBAAA,GACzCD,IAAI,CAAClC,UAAU,cAAAmC,gBAAA,uBAAfA,gBAAA,CAAiBF,GAAG,CAAEc,QAAQ,iBAC7BjF,OAAA,CAACpB,MAAM,CAAC2E,GAAG;8BAETC,UAAU,EAAE;gCAAEE,KAAK,EAAE,IAAI;gCAAEY,CAAC,EAAE,CAAC;8BAAE,CAAE;8BACnCX,UAAU,EAAE;gCAAEC,QAAQ,EAAE;8BAAI,CAAE;8BAAAf,QAAA,eAE9B7C,OAAA,CAACtB,IAAI;gCACH4E,EAAE,EAAE2B,QAAQ,CAAC3D,IAAK;gCAClByB,SAAS,EAAC,0EAA0E;gCACpFC,KAAK,EAAE;kCACLC,eAAe,EAAE,qBAAqB;kCACtCuB,MAAM,EAAE;gCACV,CAAE;gCAAA3B,QAAA,gBAEF7C,OAAA;kCAAK+C,SAAS,EAAC,kCAAkC;kCAAAF,QAAA,gBAC/C7C,OAAA;oCAAM+C,SAAS,EAAC,UAAU;oCAAAF,QAAA,EAAEoC,QAAQ,CAACnD;kCAAI;oCAAAiC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC,eACjDlE,OAAA;oCAAK+C,SAAS,EAAC,QAAQ;oCAAAF,QAAA,gBACrB7C,OAAA;sCAAK+C,SAAS,EAAC,kDAAkD;sCAC5DC,KAAK,EAAE;wCAAEhB,KAAK,EAAE;sCAAsB,CAAE;sCAAAa,QAAA,GAC1CoC,QAAQ,CAACpD,IAAI,EACboD,QAAQ,CAAC9C,QAAQ,iBAChBnC,OAAA;wCAAM+C,SAAS,EAAC,+EAA+E;wCAAAF,QAAA,EAAC;sCAEhG;wCAAAkB,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAM,CACP;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACE,CAAC,eACNlE,OAAA;sCAAK+C,SAAS,EAAC,+BAA+B;sCACzCC,KAAK,EAAE;wCAAEhB,KAAK,EAAE;sCAAwB,CAAE;sCAAAa,QAAA,EAC5CoC,QAAQ,CAAClD;oCAAW;sCAAAgC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAClB,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eAGNlE,OAAA;kCAAK+C,SAAS,EAAC,mCAAmC;kCAAAF,QAAA,eAChD7C,OAAA;oCAAM+C,SAAS,EAAC,sCAAsC;oCAChDC,KAAK,EAAE;sCAAEhB,KAAK,EAAE;oCAAwB,CAAE;oCAAAa,QAAA,EAAC;kCAEjD;oCAAAkB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACJ,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF;4BAAC,GAtCFe,QAAQ,CAACpD,IAAI;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAuCR,CACb;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC,EAGLE,IAAI,CAAChC,gBAAgB,iBACpBpC,OAAA;4BAAK+C,SAAS,EAAC,gCAAgC;4BAC1CC,KAAK,EAAE;8BAAEkC,cAAc,EAAE;4BAAwB,CAAE;4BAAArC,QAAA,gBACtD7C,OAAA;8BAAI+C,SAAS,EAAC,qCAAqC;8BAC/CC,KAAK,EAAE;gCAAEhB,KAAK,EAAE;8BAAsB,CAAE;8BAAAa,QAAA,EAAC;4BAE7C;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACLlE,OAAA;8BAAK+C,SAAS,EAAC,wBAAwB;8BAAAF,QAAA,EACpCuB,IAAI,CAAChC,gBAAgB,CAAC+B,GAAG,CAAC,CAACgB,OAAO,EAAEC,KAAK,kBACxCpF,OAAA;gCACK+C,SAAS,EAAC,8DAA8D;gCACxEC,KAAK,EAAE;kCAAEC,eAAe,EAAE;gCAAqB,CAAE;gCAAAJ,QAAA,gBACpD7C,OAAA;kCACEqF,GAAG,EAAEF,OAAO,CAAC7C,KAAM;kCACnBgD,GAAG,EAAEH,OAAO,CAACtD,IAAK;kCAClBkB,SAAS,EAAC;gCAAmC;kCAAAgB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC9C,CAAC,eACFlE,OAAA;kCAAK+C,SAAS,EAAC,gBAAgB;kCAAAF,QAAA,gBAC7B7C,OAAA;oCAAG+C,SAAS,EAAC,+CAA+C;oCACzDC,KAAK,EAAE;sCAAEhB,KAAK,EAAE;oCAAsB,CAAE;oCAAAa,QAAA,EACxCsC,OAAO,CAACtD;kCAAI;oCAAAkC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACZ,CAAC,eACJlE,OAAA;oCAAG+C,SAAS,EAAC,oCAAoC;oCAC9CC,KAAK,EAAE;sCAAEhB,KAAK,EAAE;oCAAwB,CAAE;oCAAAa,QAAA,EAC1CsC,OAAO,CAAC9C;kCAAK;oCAAA0B,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACb,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACD,CAAC;8BAAA,GAjBEkB,KAAK;gCAAArB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAkBV,CACN;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACb;kBAAA,GA/JIE,IAAI,CAACvC,IAAI;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgKJ,CAAC;gBAAA,CACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlE,OAAA;cAAK+C,SAAS,EAAC,gDAAgD;cAAAF,QAAA,eAC7D7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;gBACTR,SAAS,EAAC,uBAAuB;gBACjCS,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAf,QAAA,gBAG9B7C,OAAA;kBAAK+C,SAAS,EAAC,2EAA2E;kBAAAF,QAAA,eACxF7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;oBACToB,OAAO,EAAE;sBAAElB,MAAM,EAAE9C,WAAW,GAAG,GAAG,GAAG;oBAAE,CAAE;oBAC3CgD,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAf,QAAA,eAE9B7C,OAAA,CAACb,mBAAmB;sBAAC4D,SAAS,EAAC,wCAAwC;sBAClDC,KAAK,EAAE;wBAAEhB,KAAK,EAAErB,WAAW,GAAG,uBAAuB,GAAG;sBAAwB;oBAAE;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNlE,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACX0B,WAAW,EAAC,oBAAoB;kBAChCC,KAAK,EAAE7E,WAAY;kBACnB8E,QAAQ,EAAGvE,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACwE,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGzE,CAAC,IAAKA,CAAC,CAAC0E,GAAG,KAAK,OAAO,IAAI3E,YAAY,CAACC,CAAC,CAAE;kBACvD6B,SAAS,EAAC,mJAAmJ;kBAC7JC,KAAK,EAAE;oBACLC,eAAe,EAAE,qBAAqB;oBACtCsB,WAAW,EAAE5D,WAAW,GAAG,uBAAuB,GAAG,uBAAuB;oBAC5EqB,KAAK,EAAE,qBAAqB;oBAC5B6D,QAAQ,EAAE;kBACZ;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGFlE,OAAA;kBAAK+C,SAAS,EAAC,6DAA6D;kBAAAF,QAAA,GACzElC,WAAW,iBACVX,OAAA,CAACpB,MAAM,CAACkH,MAAM;oBACZpB,OAAO,EAAE;sBAAEhB,KAAK,EAAE;oBAAE,CAAE;oBACtBiB,OAAO,EAAE;sBAAEjB,KAAK,EAAE;oBAAE,CAAE;oBACtBqC,OAAO,EAAEA,CAAA,KAAMnF,cAAc,CAAC,EAAE,CAAE;oBAClCmC,SAAS,EAAC,oCAAoC;oBAC9CC,KAAK,EAAE;sBAAEhB,KAAK,EAAE;oBAAwB,CAAE;oBAAAa,QAAA,eAE1C7C,OAAA;sBAAK+C,SAAS,EAAC,SAAS;sBAACiD,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAArD,QAAA,eAC5E7C,OAAA;wBAAMmG,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAsB;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAChB,eAEDlE,OAAA;oBAAK+C,SAAS,EAAC;kBAAsB;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAE5ClE,OAAA;oBAAK+C,SAAS,EAAC,qGAAqG;oBAC/GC,KAAK,EAAE;sBACLC,eAAe,EAAE,oBAAoB;sBACrCsB,WAAW,EAAE,yBAAyB;sBACtCvC,KAAK,EAAE;oBACT,CAAE;oBAAAa,QAAA,EAAC;kBAER;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLvD,WAAW,iBACVX,OAAA,CAACpB,MAAM,CAAC2E,GAAG;kBACTmB,OAAO,EAAE;oBAAEI,OAAO,EAAE,CAAC;oBAAER,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAER,CAAC,EAAE;kBAAE,CAAE;kBAC9BS,IAAI,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAER,CAAC,EAAE;kBAAG,CAAE;kBAC5BvB,SAAS,EAAC,4HAA4H;kBACtIC,KAAK,EAAE;oBACLC,eAAe,EAAE,mBAAmB;oBACpCsB,WAAW,EAAE;kBACf,CAAE;kBAAA1B,QAAA,eAEF7C,OAAA;oBAAK+C,SAAS,EAAC,KAAK;oBAAAF,QAAA,gBAElB7C,OAAA;sBAAK+C,SAAS,EAAC,MAAM;sBAAAF,QAAA,gBACnB7C,OAAA;wBAAK+C,SAAS,EAAC,+DAA+D;wBACzEC,KAAK,EAAE;0BAAEhB,KAAK,EAAE;wBAAsB,CAAE;wBAAAa,QAAA,gBAC3C7C,OAAA,CAACb,mBAAmB;0BAAC4D,SAAS,EAAC;wBAAc;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,qBAElD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlE,OAAA;wBAAK+C,SAAS,EAAC,WAAW;wBAAAF,QAAA,EACvB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,CAACsB,GAAG,CAAEoC,UAAU,iBAC9FvG,OAAA,CAACpB,MAAM,CAACkH,MAAM;0BAEZtC,UAAU,EAAE;4BAAEgD,CAAC,EAAE;0BAAE,CAAE;0BACrBzD,SAAS,EAAC,qFAAqF;0BAC/FC,KAAK,EAAE;4BACLC,eAAe,EAAE,qBAAqB;4BACtCjB,KAAK,EAAE;0BACT,CAAE;0BAAAa,QAAA,eAEF7C,OAAA;4BAAK+C,SAAS,EAAC,mCAAmC;4BAAAF,QAAA,gBAChD7C,OAAA;8BAAM+C,SAAS,EAAC,aAAa;8BAAAF,QAAA,EAAE0D;4BAAU;8BAAAxC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eACjDlE,OAAA;8BAAK+C,SAAS,EAAC,8DAA8D;8BACxEiD,IAAI,EAAC,MAAM;8BAACC,MAAM,EAAC,cAAc;8BAACC,OAAO,EAAC,WAAW;8BAAArD,QAAA,eACxD7C,OAAA;gCAAMmG,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAACC,CAAC,EAAC;8BAAc;gCAAAvC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GAdDqC,UAAU;0BAAAxC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAeF,CAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNlE,OAAA;sBAAK+C,SAAS,EAAC,gCAAgC;sBAC1CC,KAAK,EAAE;wBAAEkC,cAAc,EAAE;sBAAwB,CAAE;sBAAArC,QAAA,gBACtD7C,OAAA;wBAAK+C,SAAS,EAAC,6CAA6C;wBACvDC,KAAK,EAAE;0BAAEhB,KAAK,EAAE;wBAAsB,CAAE;wBAAAa,QAAA,EAAC;sBAE9C;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlE,OAAA;wBAAK+C,SAAS,EAAC,sBAAsB;wBAAAF,QAAA,EAClC,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC,CAACsB,GAAG,CAAEc,QAAQ,iBACjEjF,OAAA,CAACpB,MAAM,CAACkH,MAAM;0BAEZtC,UAAU,EAAE;4BAAEE,KAAK,EAAE;0BAAK,CAAE;0BAC5B+C,QAAQ,EAAE;4BAAE/C,KAAK,EAAE;0BAAK,CAAE;0BAC1BX,SAAS,EAAC,yDAAyD;0BACnEC,KAAK,EAAE;4BACLC,eAAe,EAAE,uBAAuB;4BACxCjB,KAAK,EAAE;0BACT,CAAE;0BAAAa,QAAA,EAEDoC;wBAAQ,GATJA,QAAQ;0BAAAlB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAUA,CAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNlE,OAAA;cAAK+C,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBAE1C7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;gBACTC,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5B+C,QAAQ,EAAE;kBAAE/C,KAAK,EAAE;gBAAK,CAAE;gBAAAb,QAAA,eAE1B7C,OAAA,CAAC0G,WAAW;kBAAC3D,SAAS,EAAC;gBAAsB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAGblE,OAAA,CAACtB,IAAI;gBAAC4E,EAAE,EAAC,WAAW;gBAAAT,QAAA,eAClB7C,OAAA,CAACpB,MAAM,CAACkH,MAAM;kBACZtC,UAAU,EAAE;oBAAEE,KAAK,EAAE,GAAG;oBAAEY,CAAC,EAAE,CAAC;kBAAE,CAAE;kBAClCmC,QAAQ,EAAE;oBAAE/C,KAAK,EAAE;kBAAK,CAAE;kBAC1BX,SAAS,EAAC,kEAAkE;kBAC5EC,KAAK,EAAE;oBACLC,eAAe,EAAE,qBAAqB;oBACtCsB,WAAW,EAAE,uBAAuB;oBACpCC,MAAM,EAAE,WAAW;oBACnBxC,KAAK,EAAE;kBACT,CAAE;kBACF2E,KAAK,EAAC,UAAU;kBAAA9D,QAAA,gBAEhB7C,OAAA,CAACX,SAAS;oBAAC0D,SAAS,EAAC;kBAAS;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEjClE,OAAA,CAACpB,MAAM,CAAC6F,IAAI;oBACVC,OAAO,EAAE;sBAAEhB,KAAK,EAAE;oBAAE,CAAE;oBACtBiB,OAAO,EAAE;sBAAEjB,KAAK,EAAE;oBAAE,CAAE;oBACtBX,SAAS,EAAC,mIAAmI;oBAAAF,QAAA,EAC9I;kBAED;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAGPlE,OAAA,CAACpB,MAAM,CAAC2E,GAAG;gBACTC,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5B+C,QAAQ,EAAE;kBAAE/C,KAAK,EAAE;gBAAK,CAAE;gBAC1BX,SAAS,EAAC,UAAU;gBAAAF,QAAA,eAEpB7C,OAAA,CAACH,YAAY;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGZnD,eAAe,gBACdf,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,KAAK;gBAACC,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBACtC7C,OAAA;kBAAA6C,QAAA,eACE7C,OAAA,CAAClB,IAAI,CAAC8H,MAAM;oBAAC7D,SAAS,EAAE,+FACtBtC,UAAU,GACN,oFAAoF,GACpF,qHAAqH,EACxH;oBAAAoC,QAAA,gBACD7C,OAAA;sBAAM+C,SAAS,EAAC,SAAS;sBAAAF,QAAA,EAAC;oBAAc;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC9CpD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+F,cAAc,gBACnB7G,OAAA;sBACE+C,SAAS,EAAC,2CAA2C;sBACrDsC,GAAG,EAAEvE,IAAI,CAAC+F,cAAe;sBACzBvB,GAAG,EAAC;oBAAE;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,gBAEFlE,OAAA;sBAAK+C,SAAS,EAAC,mHAAmH;sBAAAF,QAAA,eAChI7C,OAAA,CAACZ,QAAQ;wBAAC2D,SAAS,EAAC;sBAAoB;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACN,eACDlE,OAAA;sBAAM+C,SAAS,EAAC,qCAAqC;sBAAAF,QAAA,EAClD,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,SAAS,KAAI;oBAAS;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACPlE,OAAA,CAACN,eAAe;sBAACqD,SAAS,EAAC;oBAAS;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACNlE,OAAA,CAACjB,UAAU;kBACT+D,EAAE,EAAErE,QAAS;kBACbsI,KAAK,EAAC,kCAAkC;kBACxCC,SAAS,EAAC,8BAA8B;kBACxCC,OAAO,EAAC,iCAAiC;kBACzCC,KAAK,EAAC,gCAAgC;kBACtCC,SAAS,EAAC,iCAAiC;kBAC3CC,OAAO,EAAC,8BAA8B;kBAAAvE,QAAA,eAEtC7C,OAAA,CAAClB,IAAI,CAACuI,KAAK;oBAACtE,SAAS,EAAC,2JAA2J;oBAAAF,QAAA,gBAE/K7C,OAAA;sBAAK+C,SAAS,EAAC,iFAAiF;sBAAAF,QAAA,eAC9F7C,OAAA;wBAAK+C,SAAS,EAAC,6BAA6B;wBAAAF,QAAA,GACzC/B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+F,cAAc,gBACnB7G,OAAA;0BACE+C,SAAS,EAAC,6CAA6C;0BACvDsC,GAAG,EAAEvE,IAAI,CAAC+F,cAAe;0BACzBvB,GAAG,EAAC;wBAAE;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,gBAEFlE,OAAA;0BAAK+C,SAAS,EAAC,qEAAqE;0BAAAF,QAAA,eAClF7C,OAAA,CAACZ,QAAQ;4BAAC2D,SAAS,EAAC;0BAAoB;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CACN,eACDlE,OAAA;0BAAA6C,QAAA,gBACE7C,OAAA;4BAAG+C,SAAS,EAAC,0BAA0B;4BAAAF,QAAA,GACpC/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,SAAS,EAAC,GAAC,EAAChG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,QAAQ;0BAAA;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC,CAAC,eACJlE,OAAA;4BAAG+C,SAAS,EAAC,uBAAuB;4BAAAF,QAAA,EAAE/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG;0BAAK;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNlE,OAAA;sBAAK+C,SAAS,EAAC,MAAM;sBAAAF,QAAA,GAClBJ,cAAc,CAAC0B,GAAG,CAAEC,IAAI,iBACvBpE,OAAA,CAAClB,IAAI,CAAC0I,IAAI;wBAAA3E,QAAA,EACPA,CAAC;0BAAE4E;wBAAO,CAAC,kBACVzH,OAAA,CAACtB,IAAI;0BACH4E,EAAE,EAAEc,IAAI,CAAC9C,IAAK;0BACdyB,SAAS,EAAE7C,UAAU,CACnBuH,MAAM,GAAG,0CAA0C,GAAG,eAAe,EACrE,8EACF,CAAE;0BAAA5E,QAAA,gBAEF7C,OAAA,CAACoE,IAAI,CAACtC,IAAI;4BAACiB,SAAS,EAAC;0BAAS;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjClE,OAAA;4BAAA6C,QAAA,EAAOuB,IAAI,CAACvC;0BAAI;4BAAAkC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBACP,GAZaE,IAAI,CAACvC,IAAI;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAad,CACZ,CAAC,eACFlE,OAAA;wBAAK+C,SAAS,EAAC,oCAAoC;wBAAAF,QAAA,eACjD7C,OAAA,CAAClB,IAAI,CAAC0I,IAAI;0BAAA3E,QAAA,EACPA,CAAC;4BAAE4E;0BAAO,CAAC,kBACVzH,OAAA;4BACE+F,OAAO,EAAE/E,MAAO;4BAChB+B,SAAS,EAAE7C,UAAU,CACnBuH,MAAM,GAAG,wBAAwB,GAAG,cAAc,EAClD,qFACF,CAAE;4BAAA5E,QAAA,gBAEF7C,OAAA,CAACJ,yBAAyB;8BAACmD,SAAS,EAAC;4BAAS;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACjDlE,OAAA;8BAAA6C,QAAA,EAAM;4BAAQ;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBACT;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,gBAEPlE,OAAA;gBAAK+C,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1C7C,OAAA,CAACtB,IAAI;kBAAC4E,EAAE,EAAC,QAAQ;kBAAAT,QAAA,eACf7C,OAAA,CAACpB,MAAM,CAACkH,MAAM;oBACZtC,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEY,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCmC,QAAQ,EAAE;sBAAE/C,KAAK,EAAE;oBAAK,CAAE;oBAC1BX,SAAS,EAAE,4EACTtC,UAAU,GACN,yHAAyH,GACzH,mIAAmI,EACtI;oBAAAoC,QAAA,EACJ;kBAED;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPlE,OAAA,CAACtB,IAAI;kBAAC4E,EAAE,EAAC,WAAW;kBAAAT,QAAA,eAClB7C,OAAA,CAACpB,MAAM,CAACkH,MAAM;oBACZtC,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEY,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCmC,QAAQ,EAAE;sBAAE/C,KAAK,EAAE;oBAAK,CAAE;oBAC1BX,SAAS,EAAC,gOAAgO;oBAAAF,QAAA,EAC3O;kBAED;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,eAGDlE,OAAA;gBAAK+C,SAAS,EAAC,WAAW;gBAAAF,QAAA,eACxB7C,OAAA,CAACnB,UAAU,CAAC+H,MAAM;kBAAC7D,SAAS,EAAE,+FAC5BtC,UAAU,GACN,oEAAoE,GACpE,qGAAqG,+EAC3B;kBAAAoC,QAAA,gBAC9E7C,OAAA;oBAAM+C,SAAS,EAAC,SAAS;oBAAAF,QAAA,EAAC;kBAAc;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/ClE,OAAA,CAACpB,MAAM,CAAC2E,GAAG;oBACToB,OAAO,EAAE;sBAAElB,MAAM,EAAEJ,IAAI,GAAG,GAAG,GAAG;oBAAE,CAAE;oBACpCM,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAE7BQ,IAAI,gBACHrD,OAAA,CAACf,SAAS;sBAAC8D,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE1DlE,OAAA,CAAChB,SAAS;sBAAC+D,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA,CAACnB,UAAU,CAAC6I,KAAK;UAAC3E,SAAS,EAAC,WAAW;UAAAF,QAAA,eACrC7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;YACTmB,OAAO,EAAE;cAAEI,OAAO,EAAE,CAAC;cAAE6C,MAAM,EAAE,CAAC;cAAErD,CAAC,EAAE,CAAC;YAAG,CAAE;YAC3CK,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAE6C,MAAM,EAAE,MAAM;cAAErD,CAAC,EAAE;YAAE,CAAE;YAC9CS,IAAI,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE6C,MAAM,EAAE,CAAC;cAAErD,CAAC,EAAE,CAAC;YAAG,CAAE;YACxCX,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEgE,IAAI,EAAE;YAAY,CAAE;YACjD7E,SAAS,EAAC,kEAAkE;YAAAF,QAAA,eAE5E7C,OAAA;cAAK+C,SAAS,EAAC,0BAA0B;cAAAF,QAAA,gBAEvC7C,OAAA,CAACpB,MAAM,CAAC2E,GAAG;gBACTmB,OAAO,EAAE;kBAAEI,OAAO,EAAE,CAAC;kBAAE0B,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChC7B,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAE0B,CAAC,EAAE;gBAAE,CAAE;gBAC9B7C,UAAU,EAAE;kBAAEkE,KAAK,EAAE;gBAAI,CAAE;gBAC3B9E,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBAEzB7C,OAAA;kBAAK+C,SAAS,EAAC,sEAAsE;kBAAAF,QAAA,eACnF7C,OAAA,CAACb,mBAAmB;oBAAC4D,SAAS,EAAC;kBAAuB;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNlE,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACX0B,WAAW,EAAC,wBAAwB;kBACpCC,KAAK,EAAE7E,WAAY;kBACnB8E,QAAQ,EAAGvE,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACwE,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGzE,CAAC,IAAKA,CAAC,CAAC0E,GAAG,KAAK,OAAO,IAAI3E,YAAY,CAACC,CAAC,CAAE;kBACvD6B,SAAS,EAAC;gBAAgP;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGblE,OAAA;gBAAK+C,SAAS,EAAC,WAAW;gBAAAF,QAAA,EACvBjB,UAAU,CAACuC,GAAG,CAAC,CAACC,IAAI,EAAEgB,KAAK,kBAC1BpF,OAAA,CAACpB,MAAM,CAAC2E,GAAG;kBAETmB,OAAO,EAAE;oBAAEI,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChC7B,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAE0B,CAAC,EAAE;kBAAE,CAAE;kBAC9B7C,UAAU,EAAE;oBAAEkE,KAAK,EAAE,GAAG,IAAIzC,KAAK,GAAG,CAAC;kBAAE,CAAE;kBAAAvC,QAAA,eAEzC7C,OAAA,CAACnB,UAAU,CAAC+H,MAAM;oBAChB9D,EAAE,EAAEpE,IAAK;oBACT4E,EAAE,EAAEc,IAAI,CAAC9C,IAAK;oBACdyB,SAAS,EAAE7C,UAAU,CACnBwC,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GACf,iFAAiF,GACjF,oEAAoE,EACxE,qFACF,CAAE;oBAAAuB,QAAA,gBAEF7C,OAAA,CAACoE,IAAI,CAACtC,IAAI;sBAACiB,SAAS,EAAE7C,UAAU,CAC9BwC,QAAQ,CAAC0B,IAAI,CAAC9C,IAAI,CAAC,GAAG,YAAY,GAAG,iDAAiD,EACtF,SACF;oBAAE;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLlE,OAAA;sBAAM+C,SAAS,EAAC,uBAAuB;sBAAAF,QAAA,EAAEuB,IAAI,CAACvC;oBAAI;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC,GApBfE,IAAI,CAACvC,IAAI;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBJ,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL,CAACnD,eAAe,iBACff,OAAA,CAACpB,MAAM,CAAC2E,GAAG;gBACTmB,OAAO,EAAE;kBAAEI,OAAO,EAAE,CAAC;kBAAER,CAAC,EAAE;gBAAG,CAAE;gBAC/BK,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAER,CAAC,EAAE;gBAAE,CAAE;gBAC9BX,UAAU,EAAE;kBAAEkE,KAAK,EAAE;gBAAI,CAAE;gBAC3B9E,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,gBAE/B7C,OAAA,CAACtB,IAAI;kBAAC4E,EAAE,EAAC,QAAQ;kBAACP,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eAClC7C,OAAA,CAACnB,UAAU,CAAC+H,MAAM;oBAChB9D,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,wJAAwJ;oBAAAF,QAAA,EACnK;kBAED;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACPlE,OAAA,CAACtB,IAAI;kBAAC4E,EAAE,EAAC,WAAW;kBAACP,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eACrC7C,OAAA,CAACnB,UAAU,CAAC+H,MAAM;oBAChB9D,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,8MAA8M;oBAAAF,QAAA,EACzN;kBAED;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA,eACnB;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAGblE,OAAA;MAAK+C,SAAS,EAAC;IAAM;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eAC5B,CAAC;AAEP,CAAC;AAAC1D,EAAA,CA3uBID,gBAAgB;EAAA,QAGH5B,WAAW,EACcmB,OAAO;AAAA;AAAAgI,EAAA,GAJ7CvH,gBAAgB;AA6uBtB,eAAeA,gBAAgB;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}