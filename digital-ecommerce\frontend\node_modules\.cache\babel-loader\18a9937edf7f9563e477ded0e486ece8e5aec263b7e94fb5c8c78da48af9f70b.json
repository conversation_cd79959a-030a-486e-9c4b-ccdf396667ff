{"ast": null, "code": "const appearAnimationStore = new Map();\nconst appearComplete = new Map();\nexport { appearAnimationStore, appearComplete };", "map": {"version": 3, "names": ["appearAnimationStore", "Map", "appearComplete"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/store.mjs"], "sourcesContent": ["const appearAnimationStore = new Map();\nconst appearComplete = new Map();\n\nexport { appearAnimationStore, appearComplete };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACtC,MAAMC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;AAEhC,SAASD,oBAAoB,EAAEE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}