import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ComputerDesktopIcon,
  CpuChipIcon,
  DevicePhoneMobileIcon,
  ShoppingCartIcon,
  HeartIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';
import { useCart } from '../components/ShoppingCart';

const PcGamingPage = () => {
  const { getPcGamingProducts } = useProducts();
  const { showSuccess } = useToast();
  const { addToCart } = useCart();
  
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubcategory, setSelectedSubcategory] = useState('all');
  const [selectedComponentType, setSelectedComponentType] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 5000]);
  const [sortBy, setSortBy] = useState('name');
  const [showFilters, setShowFilters] = useState(false);
  const [compareList, setCompareList] = useState([]);

  // Get all PC gaming products
  const allPcProducts = getPcGamingProducts();

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = allPcProducts.filter(product => {
      const matchesSearch = !searchQuery || 
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesSubcategory = selectedSubcategory === 'all' || 
        product.subcategory === selectedSubcategory;
      
      const matchesComponentType = selectedComponentType === 'all' || 
        product.componentType === selectedComponentType;
      
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      
      return matchesSearch && matchesSubcategory && matchesComponentType && matchesPrice;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [allPcProducts, searchQuery, selectedSubcategory, selectedComponentType, priceRange, sortBy]);

  const handleAddToCompare = (product) => {
    if (compareList.length >= 3) {
      showSuccess('Compare Limit', 'You can compare up to 3 products at a time.');
      return;
    }
    
    if (!compareList.find(p => p.id === product.id)) {
      setCompareList([...compareList, product]);
      showSuccess('Added to Compare', `${product.name} added to comparison.`);
    }
  };

  const removeFromCompare = (productId) => {
    setCompareList(compareList.filter(p => p.id !== productId));
  };

  const ProductCard = ({ product }) => (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
    >
      <div className="relative">
        <img
          src={product.images[0]}
          alt={product.name}
          className="w-full h-48 object-cover"
        />
        {product.badge && (
          <span className="absolute top-2 left-2 px-2 py-1 bg-light-orange-500 text-white text-xs font-medium rounded-full">
            {product.badge}
          </span>
        )}
        <div className="absolute top-2 right-2 flex space-x-1">
          <button
            onClick={() => handleAddToCompare(product)}
            className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors"
            title="Add to Compare"
          >
            <AdjustmentsHorizontalIcon className="w-4 h-4 text-gray-600" />
          </button>
          <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors">
            <HeartIcon className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      <div className="p-4">
        <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2">
          {product.name}
        </h3>
        
        <div className="flex items-center space-x-2 mb-2">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
            <span className="text-sm text-gray-600 ml-1">({product.reviews})</span>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.description}
        </p>

        <div className="flex items-center justify-between mb-4">
          <div>
            <span className="text-2xl font-bold text-light-orange-600">
              ${product.price.toLocaleString()}
            </span>
            {product.originalPrice && product.originalPrice > product.price && (
              <span className="text-sm text-gray-500 line-through ml-2">
                ${product.originalPrice.toLocaleString()}
              </span>
            )}
          </div>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            product.inStock 
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {product.inStock ? 'In Stock' : 'Out of Stock'}
          </span>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => {
              addToCart(product);
              showSuccess('Added to Cart', `${product.name} added to cart.`);
            }}
            disabled={!product.inStock}
            className={`flex-1 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 ${
              product.inStock
                ? 'bg-light-orange-500 text-white hover:bg-light-orange-600'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <ShoppingCartIcon className="w-4 h-4" />
            <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
          </button>
          <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <EyeIcon className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-light-orange-50 to-white">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-6xl font-bold mb-4"
            >
              PC Gaming Hub
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl md:text-2xl mb-8 opacity-90"
            >
              Build Your Ultimate Gaming Experience
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Link
                to="/pc-builder"
                className="bg-white text-light-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                PC Builder Tool
              </Link>
              <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-light-orange-600 transition-colors">
                Browse Components
              </button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex space-x-8">
              <button
                onClick={() => setSelectedSubcategory('all')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  selectedSubcategory === 'all'
                    ? 'bg-light-orange-100 text-light-orange-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <ComputerDesktopIcon className="w-5 h-5" />
                <span>All Products</span>
              </button>
              <button
                onClick={() => setSelectedSubcategory('pre-built-pc')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  selectedSubcategory === 'pre-built-pc'
                    ? 'bg-light-orange-100 text-light-orange-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <ComputerDesktopIcon className="w-5 h-5" />
                <span>Pre-Built PCs</span>
              </button>
              <button
                onClick={() => setSelectedSubcategory('pc-component')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  selectedSubcategory === 'pc-component'
                    ? 'bg-light-orange-100 text-light-orange-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <CpuChipIcon className="w-5 h-5" />
                <span>Components</span>
              </button>
              <button
                onClick={() => setSelectedSubcategory('pc-accessory')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  selectedSubcategory === 'pc-accessory'
                    ? 'bg-light-orange-100 text-light-orange-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <DevicePhoneMobileIcon className="w-5 h-5" />
                <span>Accessories</span>
              </button>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search PC gaming products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
              </div>
              
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FunnelIcon className="w-5 h-5" />
                <span>Filters</span>
              </button>

              <div className="flex border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-light-orange-100 text-light-orange-600' : 'text-gray-600'}`}
                >
                  <Squares2X2Icon className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-light-orange-100 text-light-orange-600' : 'text-gray-600'}`}
                >
                  <ListBulletIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {selectedSubcategory === 'all' ? 'All PC Gaming Products' : 
             selectedSubcategory === 'pre-built-pc' ? 'Pre-Built Gaming PCs' :
             selectedSubcategory === 'pc-component' ? 'PC Components' : 'Gaming Accessories'}
          </h2>
          <p className="text-gray-600">
            {filteredProducts.length} products found
          </p>
        </div>

        {/* Compare Bar */}
        {compareList.length > 0 && (
          <div className="bg-light-orange-50 border border-light-orange-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="font-medium text-gray-900">Compare Products:</span>
                <div className="flex space-x-2">
                  {compareList.map(product => (
                    <div key={product.id} className="flex items-center space-x-2 bg-white px-3 py-1 rounded-lg">
                      <span className="text-sm">{product.name}</span>
                      <button
                        onClick={() => removeFromCompare(product.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              <Link
                to={`/pc-gaming/compare?products=${compareList.map(p => p.id).join(',')}`}
                className="bg-light-orange-500 text-white px-4 py-2 rounded-lg hover:bg-light-orange-600 transition-colors"
              >
                Compare Now
              </Link>
            </div>
          </div>
        )}

        {/* Products Grid */}
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
            : 'grid-cols-1'
        }`}>
          <AnimatePresence>
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </AnimatePresence>
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <ComputerDesktopIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PcGamingPage;
