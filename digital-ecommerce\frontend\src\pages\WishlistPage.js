import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  HeartIcon,
  ShoppingBagIcon,
  TrashIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { useUser } from '../contexts/UserContext';
import { useCart } from '../components/ShoppingCart';
import { products } from '../data/products';
import Button from '../components/Button';
import ProductPreviewModal from '../components/ProductPreviewModal';
import toast, { Toaster } from 'react-hot-toast';

const WishlistPage = () => {
  const { user, removeFromWishlist, isInWishlist } = useUser();
  const { addToCart } = useCart();
  const [previewProduct, setPreviewProduct] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Get wishlist products
  const wishlistProducts = products.filter(product => 
    user?.wishlist?.includes(product.id)
  );

  const handleAddToCart = (product) => {
    addToCart(product);
    toast.success(`${product.name} added to cart!`);
  };

  const handleRemoveFromWishlist = (productId) => {
    removeFromWishlist(productId);
    toast.success('Removed from wishlist');
  };

  const handleProductPreview = (product) => {
    setPreviewProduct(product);
    setIsPreviewOpen(true);
  };

  const closePreview = () => {
    setIsPreviewOpen(false);
    setPreviewProduct(null);
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Sign in to view your wishlist</h1>
          <p className="text-gray-600 mb-8">
            Create an account or sign in to save your favorite products.
          </p>
          <div className="space-y-4">
            <Link to="/login">
              <Button fullWidth>Sign In</Button>
            </Link>
            <Link to="/register">
              <Button variant="outline" fullWidth>Create Account</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen theme-transition" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <Toaster position="top-right" />

      {/* Header */}
      <div className="border-b theme-transition"
           style={{
             backgroundColor: 'var(--bg-primary)',
             borderBottomColor: 'var(--border-primary)'
           }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-full theme-transition"
                 style={{ backgroundColor: 'var(--accent-primary)' }}>
              <HeartIconSolid className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold theme-transition"
                  style={{ color: 'var(--text-primary)' }}>
                My Wishlist
              </h1>
              <p className="theme-transition" style={{ color: 'var(--text-secondary)' }}>
                {wishlistProducts.length} {wishlistProducts.length === 1 ? 'item' : 'items'} saved
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {wishlistProducts.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Your wishlist is empty</h2>
              <p className="text-gray-600 mb-8">
                Start browsing and save your favorite products to your wishlist.
              </p>
              <Link to="/products">
                <Button>Start Shopping</Button>
              </Link>
            </div>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlistProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden">
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Wishlist Button */}
                  <button
                    onClick={() => handleRemoveFromWishlist(product.id)}
                    className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-red-50 transition-colors group"
                  >
                    <HeartIconSolid className="w-5 h-5 text-red-500 group-hover:text-red-600" />
                  </button>

                  {/* Badge */}
                  {product.badge && (
                    <div className="absolute top-3 left-3">
                      <span className="bg-light-orange-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
                        {product.badge}
                      </span>
                    </div>
                  )}

                  {/* Stock Status */}
                  {!product.inStock && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Out of Stock
                      </span>
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="mb-3">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {product.name}
                    </h3>
                    
                    {/* Rating */}
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.floor(product.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">
                        ({product.reviews})
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center space-x-2">
                      <span className="text-xl font-bold text-light-orange-600">
                        ${product.price}
                      </span>
                      {product.originalPrice && product.originalPrice > product.price && (
                        <span className="text-sm text-gray-500 line-through">
                          ${product.originalPrice}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.inStock}
                      fullWidth
                      icon={ShoppingBagIcon}
                      variant={product.type === 'digital' ? 'digital' : 'primary'}
                    >
                      {product.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </Button>
                    
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => handleProductPreview(product)}
                        variant="outline"
                        className="flex-1"
                      >
                        Quick Preview
                      </Button>
                      <Link to={`/products/${product.id}`} className="flex-1">
                        <Button variant="outline" fullWidth>
                          View Details
                        </Button>
                      </Link>
                      <Button
                        onClick={() => handleRemoveFromWishlist(product.id)}
                        variant="ghost"
                        icon={TrashIcon}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Continue Shopping */}
        {wishlistProducts.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center mt-12"
          >
            <Link to="/products">
              <Button variant="outline" size="large">
                Continue Shopping
              </Button>
            </Link>
          </motion.div>
        )}
      </div>

      {/* Product Preview Modal */}
      <ProductPreviewModal
        product={previewProduct}
        isOpen={isPreviewOpen}
        onClose={closePreview}
      />
    </div>
  );
};

export default WishlistPage;
