{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ModernNavigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst ModernNavigation = () => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigation = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'PC Gaming',\n    href: '/pc-gaming',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const userNavigation = [{\n    name: 'Your Profile',\n    href: '/account',\n    icon: UserIcon\n  }, {\n    name: 'Order History',\n    href: '/orders',\n    icon: ShoppingBagIcon\n  }, {\n    name: 'Wishlist',\n    href: '/wishlist',\n    icon: HeartIcon\n  }, {\n    name: 'Settings',\n    href: '/settings',\n    icon: Cog6ToothIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Disclosure, {\n      as: \"nav\",\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' : 'bg-white/10 backdrop-blur-sm'}`,\n      children: ({\n        open\n      }) => /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex h-20 items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"flex items-center space-x-3 group\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    rotate: 360,\n                    scale: 1.1\n                  },\n                  transition: {\n                    duration: 0.6,\n                    type: \"spring\",\n                    stiffness: 200\n                  },\n                  className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                    className: \"w-7 h-7 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-2xl font-bold transition-all duration-300 ${isScrolled ? 'text-gray-900' : 'text-gray-900 drop-shadow-lg'}`,\n                    children: \"ShopHub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs font-medium transition-all duration-300 ${isScrolled ? 'text-light-orange-600' : 'text-light-orange-600 drop-shadow-sm'}`,\n                    children: \"Premium Store\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:block\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-baseline space-x-2\",\n                children: navigation.map(item => /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    y: -2\n                  },\n                  transition: {\n                    duration: 0.2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: item.href,\n                    className: classNames(isActive(item.href) ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25' : isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-gray-900 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm shadow-sm border border-white/20', 'relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group'),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative z-10\",\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 27\n                    }, this), !isActive(item.href) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 25\n                  }, this)\n                }, item.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center flex-1 max-w-lg mx-8\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"relative w-full group\",\n                whileHover: {\n                  scale: 1.02\n                },\n                transition: {\n                  duration: 0.2\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: `h-5 w-5 transition-colors duration-300 ${isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search for products, brands, and more...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: `w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${isScrolled ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100' : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'} focus:outline-none shadow-lg hover:shadow-xl`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/wishlist\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1,\n                    y: -2\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `relative p-3 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'}`,\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Menu, {\n                as: \"div\",\n                className: \"relative ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Menu.Button, {\n                    className: `relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"sr-only\",\n                      children: \"Open user menu\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 27\n                    }, this), user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"h-8 w-8 rounded-full ring-2 ring-white/20\",\n                      src: user.profilePicture,\n                      alt: \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden md:block text-sm font-medium\",\n                      children: (user === null || user === void 0 ? void 0 : user.firstName) || 'Account'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Transition, {\n                  as: Fragment,\n                  enter: \"transition ease-out duration-100\",\n                  enterFrom: \"transform opacity-0 scale-95\",\n                  enterTo: \"transform opacity-100 scale-100\",\n                  leave: \"transition ease-in duration-75\",\n                  leaveFrom: \"transform opacity-100 scale-100\",\n                  leaveTo: \"transform opacity-0 scale-95\",\n                  children: /*#__PURE__*/_jsxDEV(Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"w-12 h-12 rounded-full ring-2 ring-white/30\",\n                          src: user.profilePicture,\n                          alt: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                            className: \"w-6 h-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 231,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 230,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"font-semibold text-white\",\n                            children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 235,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/80\",\n                            children: user === null || user === void 0 ? void 0 : user.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"py-2\",\n                      children: [userNavigation.map(item => /*#__PURE__*/_jsxDEV(Menu.Item, {\n                        children: ({\n                          active\n                        }) => /*#__PURE__*/_jsxDEV(Link, {\n                          to: item.href,\n                          className: classNames(active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700', 'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'),\n                          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                            className: \"w-5 h-5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 255,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: item.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 256,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 248,\n                          columnNumber: 35\n                        }, this)\n                      }, item.name, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-100 mt-2 pt-2\",\n                        children: /*#__PURE__*/_jsxDEV(Menu.Item, {\n                          children: ({\n                            active\n                          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: logout,\n                            className: classNames(active ? 'bg-red-50 text-red-600' : 'text-red-600', 'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'),\n                            children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                              className: \"w-5 h-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 271,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Sign out\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 272,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 264,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 262,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: `px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'}`,\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                  className: `relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'} focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: open ? 180 : 0\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: open ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Disclosure.Panel, {\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto',\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeInOut\"\n            },\n            className: \"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1 px-6 pb-6 pt-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.1\n                },\n                className: \"relative mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search for products...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: \"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: navigation.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.1 * (index + 2)\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: Link,\n                    to: item.href,\n                    className: classNames(isActive(item.href) ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg' : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600', 'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'),\n                    children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                      className: classNames(isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500', 'w-6 h-6')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-lg\",\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)\n                }, item.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.4\n                },\n                className: \"flex space-x-4 pt-6\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\",\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernNavigation, \"nqhB+dQNtp/nfK+k0r5VlnahLcE=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = ModernNavigation;\nexport default ModernNavigation;\nvar _c;\n$RefreshReg$(_c, \"ModernNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "Link", "useLocation", "motion", "Disclosure", "<PERSON><PERSON>", "Transition", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "Cog6ToothIcon", "ArrowRightOnRectangleIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "ModernNavigation", "_s", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "window", "href", "encodeURIComponent", "handleScroll", "scrollY", "addEventListener", "removeEventListener", "navigation", "name", "icon", "userNavigation", "isActive", "path", "pathname", "children", "as", "className", "open", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "y", "placeholder", "value", "onChange", "target", "onKeyDown", "key", "button", "whileTap", "<PERSON><PERSON>", "profilePicture", "src", "alt", "firstName", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Items", "lastName", "email", "<PERSON><PERSON>", "active", "onClick", "animate", "Panel", "initial", "opacity", "height", "exit", "ease", "x", "delay", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ModernNavigation.js"], "sourcesContent": ["import React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst ModernNavigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'PC Gaming', href: '/pc-gaming', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const userNavigation = [\n    { name: 'Your Profile', href: '/account', icon: UserIcon },\n    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },\n    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },\n    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <Disclosure as=\"nav\" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n        isScrolled \n          ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' \n          : 'bg-white/10 backdrop-blur-sm'\n      }`}>\n        {({ open }) => (\n          <>\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex h-20 items-center justify-between\">\n                {/* Logo */}\n                <div className=\"flex items-center\">\n                  <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n                    <motion.div\n                      whileHover={{ rotate: 360, scale: 1.1 }}\n                      transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                      className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                    >\n                      <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n                    </motion.div>\n                    <div className=\"flex flex-col\">\n                      <span className={`text-2xl font-bold transition-all duration-300 ${\n                        isScrolled ? 'text-gray-900' : 'text-gray-900 drop-shadow-lg'\n                      }`}>\n                        ShopHub\n                      </span>\n                      <span className={`text-xs font-medium transition-all duration-300 ${\n                        isScrolled ? 'text-light-orange-600' : 'text-light-orange-600 drop-shadow-sm'\n                      }`}>\n                        Premium Store\n                      </span>\n                    </div>\n                  </Link>\n                </div>\n\n                {/* Desktop Navigation */}\n                <div className=\"hidden lg:block\">\n                  <div className=\"flex items-baseline space-x-2\">\n                    {navigation.map((item) => (\n                      <motion.div\n                        key={item.name}\n                        whileHover={{ y: -2 }}\n                        transition={{ duration: 0.2 }}\n                      >\n                        <Link\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'\n                              : isScrolled\n                                ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                                : 'text-gray-900 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm shadow-sm border border-white/20',\n                            'relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group'\n                          )}\n                        >\n                          <span className=\"relative z-10\">{item.name}</span>\n                          {!isActive(item.href) && (\n                            <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                          )}\n                        </Link>\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Search Bar */}\n                <div className=\"hidden md:flex items-center flex-1 max-w-lg mx-8\">\n                  <motion.div \n                    className=\"relative w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${\n                        isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'\n                      }`} />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products, brands, and more...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${\n                        isScrolled\n                          ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'\n                          : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'\n                      } focus:outline-none shadow-lg hover:shadow-xl`}\n                    />\n                  </motion.div>\n                </div>\n\n                {/* Right side items */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Wishlist */}\n                  <Link to=\"/wishlist\">\n                    <motion.button\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`relative p-3 rounded-xl transition-all duration-300 group ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                          : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'\n                      }`}\n                    >\n                      <HeartIcon className=\"w-6 h-6\" />\n                    </motion.button>\n                  </Link>\n\n                  {/* Shopping Cart */}\n                  <div className=\"relative\">\n                    <ShoppingCart />\n                  </div>\n\n                  {/* User menu */}\n                  {isAuthenticated ? (\n                    <Menu as=\"div\" className=\"relative ml-3\">\n                      <div>\n                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                          isScrolled\n                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'\n                        }`}>\n                          <span className=\"sr-only\">Open user menu</span>\n                          {user?.profilePicture ? (\n                            <img\n                              className=\"h-8 w-8 rounded-full ring-2 ring-white/20\"\n                              src={user.profilePicture}\n                              alt=\"\"\n                            />\n                          ) : (\n                            <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                              <UserIcon className=\"w-5 h-5 text-white\" />\n                            </div>\n                          )}\n                          <span className=\"hidden md:block text-sm font-medium\">\n                            {user?.firstName || 'Account'}\n                          </span>\n                          <ChevronDownIcon className=\"w-4 h-4\" />\n                        </Menu.Button>\n                      </div>\n                      <Transition\n                        as={Fragment}\n                        enter=\"transition ease-out duration-100\"\n                        enterFrom=\"transform opacity-0 scale-95\"\n                        enterTo=\"transform opacity-100 scale-100\"\n                        leave=\"transition ease-in duration-75\"\n                        leaveFrom=\"transform opacity-100 scale-100\"\n                        leaveTo=\"transform opacity-0 scale-95\"\n                      >\n                        <Menu.Items className=\"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\">\n                          {/* User info header */}\n                          <div className=\"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                            <div className=\"flex items-center space-x-3\">\n                              {user?.profilePicture ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full ring-2 ring-white/30\"\n                                  src={user.profilePicture}\n                                  alt=\"\"\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                  <UserIcon className=\"w-6 h-6 text-white\" />\n                                </div>\n                              )}\n                              <div>\n                                <p className=\"font-semibold text-white\">\n                                  {user?.firstName} {user?.lastName}\n                                </p>\n                                <p className=\"text-sm text-white/80\">{user?.email}</p>\n                              </div>\n                            </div>\n                          </div>\n                          \n                          {/* Menu items */}\n                          <div className=\"py-2\">\n                            {userNavigation.map((item) => (\n                              <Menu.Item key={item.name}>\n                                {({ active }) => (\n                                  <Link\n                                    to={item.href}\n                                    className={classNames(\n                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',\n                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <item.icon className=\"w-5 h-5\" />\n                                    <span>{item.name}</span>\n                                  </Link>\n                                )}\n                              </Menu.Item>\n                            ))}\n                            <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                              <Menu.Item>\n                                {({ active }) => (\n                                  <button\n                                    onClick={logout}\n                                    className={classNames(\n                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',\n                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n                                    <span>Sign out</span>\n                                  </button>\n                                )}\n                              </Menu.Item>\n                            </div>\n                          </div>\n                        </Menu.Items>\n                      </Transition>\n                    </Menu>\n                  ) : (\n                    <div className=\"flex items-center space-x-3\">\n                      <Link to=\"/login\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${\n                            isScrolled\n                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'\n                              : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'\n                          }`}\n                        >\n                          Sign In\n                        </motion.button>\n                      </Link>\n                      <Link to=\"/register\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                        >\n                          Sign Up\n                        </motion.button>\n                      </Link>\n                    </div>\n                  )}\n\n                  {/* Mobile menu button */}\n                  <div className=\"lg:hidden\">\n                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'\n                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>\n                      <span className=\"sr-only\">Open main menu</span>\n                      <motion.div\n                        animate={{ rotate: open ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {open ? (\n                          <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        ) : (\n                          <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        )}\n                      </motion.div>\n                    </Disclosure.Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu */}\n            <Disclosure.Panel className=\"lg:hidden\">\n              <motion.div\n                initial={{ opacity: 0, height: 0, y: -20 }}\n                animate={{ opacity: 1, height: 'auto', y: 0 }}\n                exit={{ opacity: 0, height: 0, y: -20 }}\n                transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                className=\"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\"\n              >\n                <div className=\"space-y-1 px-6 pb-6 pt-6\">\n                  {/* Mobile Search */}\n                  <motion.div \n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 }}\n                    className=\"relative mb-6\"\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                    />\n                  </motion.div>\n\n                  {/* Mobile Navigation */}\n                  <div className=\"space-y-3\">\n                    {navigation.map((item, index) => (\n                      <motion.div\n                        key={item.name}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: 0.1 * (index + 2) }}\n                      >\n                        <Disclosure.Button\n                          as={Link}\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'\n                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',\n                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'\n                          )}\n                        >\n                          <item.icon className={classNames(\n                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',\n                            'w-6 h-6'\n                          )} />\n                          <span className=\"font-semibold text-lg\">{item.name}</span>\n                        </Disclosure.Button>\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  {/* Mobile Auth Buttons */}\n                  {!isAuthenticated && (\n                    <motion.div \n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 }}\n                      className=\"flex space-x-4 pt-6\"\n                    >\n                      <Link to=\"/login\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\"\n                        >\n                          Sign In\n                        </Disclosure.Button>\n                      </Link>\n                      <Link to=\"/register\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\"\n                        >\n                          Sign Up\n                        </Disclosure.Button>\n                      </Link>\n                    </motion.div>\n                  )}\n                </div>\n              </motion.div>\n            </Disclosure.Panel>\n          </>\n        )}\n      </Disclosure>\n\n      {/* Spacer */}\n      <div className=\"h-20\"></div>\n    </>\n  );\n};\n\nexport default ModernNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,IAAI,EAAEC,UAAU,QAAQ,mBAAmB;AAChE,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,yBAAyB,QACpB,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAvB,QAAA,IAAAwB,SAAA;AAElD,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMsC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIR,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MACtBC,MAAM,CAACR,QAAQ,CAACS,IAAI,GAAG,oBAAoBC,kBAAkB,CAACZ,WAAW,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE;IACrF;EACF,CAAC;EAED5C,SAAS,CAAC,MAAM;IACd,MAAMgD,YAAY,GAAGA,CAAA,KAAM;MACzBd,aAAa,CAACW,MAAM,CAACI,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDJ,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMH,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,MAAM;IAAEP,IAAI,EAAE,GAAG;IAAEQ,IAAI,EAAExC;EAAS,CAAC,EAC3C;IAAEuC,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEvC;EAAQ,CAAC,EACtD;IAAEsC,IAAI,EAAE,SAAS;IAAEP,IAAI,EAAE,mBAAmB;IAAEQ,IAAI,EAAEvC;EAAQ,CAAC,EAC7D;IAAEsC,IAAI,EAAE,WAAW;IAAEP,IAAI,EAAE,YAAY;IAAEQ,IAAI,EAAEvC;EAAQ,CAAC,EACxD;IAAEsC,IAAI,EAAE,OAAO;IAAEP,IAAI,EAAE,QAAQ;IAAEQ,IAAI,EAAErC;EAAsB,CAAC,EAC9D;IAAEoC,IAAI,EAAE,SAAS;IAAEP,IAAI,EAAE,UAAU;IAAEQ,IAAI,EAAEtC;EAAU,CAAC,CACvD;EAED,MAAMuC,cAAc,GAAG,CACrB;IAAEF,IAAI,EAAE,cAAc;IAAEP,IAAI,EAAE,UAAU;IAAEQ,IAAI,EAAE1C;EAAS,CAAC,EAC1D;IAAEyC,IAAI,EAAE,eAAe;IAAEP,IAAI,EAAE,SAAS;IAAEQ,IAAI,EAAE5C;EAAgB,CAAC,EACjE;IAAE2C,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEzC;EAAU,CAAC,EACxD;IAAEwC,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEnC;EAAc,CAAC,CAC7D;EAED,MAAMqC,QAAQ,GAAIC,IAAI,IAAKpB,QAAQ,CAACqB,QAAQ,KAAKD,IAAI;EAErD,oBACEjC,OAAA,CAAAC,SAAA;IAAAkC,QAAA,gBACEnC,OAAA,CAACnB,UAAU;MAACuD,EAAE,EAAC,KAAK;MAACC,SAAS,EAAE,+DAC9B5B,UAAU,GACN,iEAAiE,GACjE,8BAA8B,EACjC;MAAA0B,QAAA,EACAA,CAAC;QAAEG;MAAK,CAAC,kBACRtC,OAAA,CAAAC,SAAA;QAAAkC,QAAA,gBACEnC,OAAA;UAAKqC,SAAS,EAAC,wCAAwC;UAAAF,QAAA,eACrDnC,OAAA;YAAKqC,SAAS,EAAC,wCAAwC;YAAAF,QAAA,gBAErDnC,OAAA;cAAKqC,SAAS,EAAC,mBAAmB;cAAAF,QAAA,eAChCnC,OAAA,CAACtB,IAAI;gBAAC6D,EAAE,EAAC,GAAG;gBAACF,SAAS,EAAC,mCAAmC;gBAAAF,QAAA,gBACxDnC,OAAA,CAACpB,MAAM,CAAC4D,GAAG;kBACTC,UAAU,EAAE;oBAAEC,MAAM,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBACxCC,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE;kBAAI,CAAE;kBAC9DV,SAAS,EAAC,2MAA2M;kBAAAF,QAAA,gBAErNnC,OAAA,CAACd,eAAe;oBAACmD,SAAS,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDnD,OAAA;oBAAKqC,SAAS,EAAC;kBAA6E;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACbnD,OAAA;kBAAKqC,SAAS,EAAC,eAAe;kBAAAF,QAAA,gBAC5BnC,OAAA;oBAAMqC,SAAS,EAAE,kDACf5B,UAAU,GAAG,eAAe,GAAG,8BAA8B,EAC5D;oBAAA0B,QAAA,EAAC;kBAEJ;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPnD,OAAA;oBAAMqC,SAAS,EAAE,mDACf5B,UAAU,GAAG,uBAAuB,GAAG,sCAAsC,EAC5E;oBAAA0B,QAAA,EAAC;kBAEJ;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNnD,OAAA;cAAKqC,SAAS,EAAC,iBAAiB;cAAAF,QAAA,eAC9BnC,OAAA;gBAAKqC,SAAS,EAAC,+BAA+B;gBAAAF,QAAA,EAC3CP,UAAU,CAACwB,GAAG,CAAEC,IAAI,iBACnBrD,OAAA,CAACpB,MAAM,CAAC4D,GAAG;kBAETC,UAAU,EAAE;oBAAEa,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACtBV,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAV,QAAA,eAE9BnC,OAAA,CAACtB,IAAI;oBACH6D,EAAE,EAAEc,IAAI,CAAC/B,IAAK;oBACde,SAAS,EAAEnC,UAAU,CACnB8B,QAAQ,CAACqB,IAAI,CAAC/B,IAAI,CAAC,GACf,qEAAqE,GACrEb,UAAU,GACR,oEAAoE,GACpE,+GAA+G,EACrH,yFACF,CAAE;oBAAA0B,QAAA,gBAEFnC,OAAA;sBAAMqC,SAAS,EAAC,eAAe;sBAAAF,QAAA,EAAEkB,IAAI,CAACxB;oBAAI;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACjD,CAACnB,QAAQ,CAACqB,IAAI,CAAC/B,IAAI,CAAC,iBACnBtB,OAAA;sBAAKqC,SAAS,EAAC;oBAAyJ;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC/K;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC,GAnBFE,IAAI,CAACxB,IAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBJ,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnD,OAAA;cAAKqC,SAAS,EAAC,kDAAkD;cAAAF,QAAA,eAC/DnC,OAAA,CAACpB,MAAM,CAAC4D,GAAG;gBACTH,SAAS,EAAC,uBAAuB;gBACjCI,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAV,QAAA,gBAE9BnC,OAAA;kBAAKqC,SAAS,EAAC,sEAAsE;kBAAAF,QAAA,eACnFnC,OAAA,CAACb,mBAAmB;oBAACkD,SAAS,EAAE,0CAC9B5B,UAAU,GAAG,iDAAiD,GAAG,sCAAsC;kBACtG;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnD,OAAA;kBACE8C,IAAI,EAAC,MAAM;kBACXS,WAAW,EAAC,0CAA0C;kBACtDC,KAAK,EAAE7C,WAAY;kBACnB8C,QAAQ,EAAGvC,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGzC,CAAC,IAAKA,CAAC,CAAC0C,GAAG,KAAK,OAAO,IAAI3C,YAAY,CAACC,CAAC,CAAE;kBACvDmB,SAAS,EAAE,2EACT5B,UAAU,GACN,qJAAqJ,GACrJ,uJAAuJ;gBAC7G;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNnD,OAAA;cAAKqC,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBAE1CnC,OAAA,CAACtB,IAAI;gBAAC6D,EAAE,EAAC,WAAW;gBAAAJ,QAAA,eAClBnC,OAAA,CAACpB,MAAM,CAACiF,MAAM;kBACZpB,UAAU,EAAE;oBAAEE,KAAK,EAAE,GAAG;oBAAEW,CAAC,EAAE,CAAC;kBAAE,CAAE;kBAClCQ,QAAQ,EAAE;oBAAEnB,KAAK,EAAE;kBAAK,CAAE;kBAC1BN,SAAS,EAAE,6DACT5B,UAAU,GACN,oFAAoF,GACpF,qHAAqH,EACxH;kBAAA0B,QAAA,eAEHnC,OAAA,CAACX,SAAS;oBAACgD,SAAS,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAGPnD,OAAA;gBAAKqC,SAAS,EAAC,UAAU;gBAAAF,QAAA,eACvBnC,OAAA,CAACH,YAAY;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EAGLpC,eAAe,gBACdf,OAAA,CAAClB,IAAI;gBAACsD,EAAE,EAAC,KAAK;gBAACC,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBACtCnC,OAAA;kBAAAmC,QAAA,eACEnC,OAAA,CAAClB,IAAI,CAACiF,MAAM;oBAAC1B,SAAS,EAAE,+FACtB5B,UAAU,GACN,oFAAoF,GACpF,qHAAqH,EACxH;oBAAA0B,QAAA,gBACDnC,OAAA;sBAAMqC,SAAS,EAAC,SAAS;sBAAAF,QAAA,EAAC;oBAAc;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC9CrC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkD,cAAc,gBACnBhE,OAAA;sBACEqC,SAAS,EAAC,2CAA2C;sBACrD4B,GAAG,EAAEnD,IAAI,CAACkD,cAAe;sBACzBE,GAAG,EAAC;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,gBAEFnD,OAAA;sBAAKqC,SAAS,EAAC,mHAAmH;sBAAAF,QAAA,eAChInC,OAAA,CAACZ,QAAQ;wBAACiD,SAAS,EAAC;sBAAoB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACN,eACDnD,OAAA;sBAAMqC,SAAS,EAAC,qCAAqC;sBAAAF,QAAA,EAClD,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,SAAS,KAAI;oBAAS;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACPnD,OAAA,CAACN,eAAe;sBAAC2C,SAAS,EAAC;oBAAS;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACNnD,OAAA,CAACjB,UAAU;kBACTqD,EAAE,EAAE3D,QAAS;kBACb2F,KAAK,EAAC,kCAAkC;kBACxCC,SAAS,EAAC,8BAA8B;kBACxCC,OAAO,EAAC,iCAAiC;kBACzCC,KAAK,EAAC,gCAAgC;kBACtCC,SAAS,EAAC,iCAAiC;kBAC3CC,OAAO,EAAC,8BAA8B;kBAAAtC,QAAA,eAEtCnC,OAAA,CAAClB,IAAI,CAAC4F,KAAK;oBAACrC,SAAS,EAAC,2JAA2J;oBAAAF,QAAA,gBAE/KnC,OAAA;sBAAKqC,SAAS,EAAC,iFAAiF;sBAAAF,QAAA,eAC9FnC,OAAA;wBAAKqC,SAAS,EAAC,6BAA6B;wBAAAF,QAAA,GACzCrB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkD,cAAc,gBACnBhE,OAAA;0BACEqC,SAAS,EAAC,6CAA6C;0BACvD4B,GAAG,EAAEnD,IAAI,CAACkD,cAAe;0BACzBE,GAAG,EAAC;wBAAE;0BAAAlB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,gBAEFnD,OAAA;0BAAKqC,SAAS,EAAC,qEAAqE;0BAAAF,QAAA,eAClFnC,OAAA,CAACZ,QAAQ;4BAACiD,SAAS,EAAC;0BAAoB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CACN,eACDnD,OAAA;0BAAAmC,QAAA,gBACEnC,OAAA;4BAAGqC,SAAS,EAAC,0BAA0B;4BAAAF,QAAA,GACpCrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,SAAS,EAAC,GAAC,EAACrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,QAAQ;0BAAA;4BAAA3B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC,CAAC,eACJnD,OAAA;4BAAGqC,SAAS,EAAC,uBAAuB;4BAAAF,QAAA,EAAErB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D;0BAAK;4BAAA5B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNnD,OAAA;sBAAKqC,SAAS,EAAC,MAAM;sBAAAF,QAAA,GAClBJ,cAAc,CAACqB,GAAG,CAAEC,IAAI,iBACvBrD,OAAA,CAAClB,IAAI,CAAC+F,IAAI;wBAAA1C,QAAA,EACPA,CAAC;0BAAE2C;wBAAO,CAAC,kBACV9E,OAAA,CAACtB,IAAI;0BACH6D,EAAE,EAAEc,IAAI,CAAC/B,IAAK;0BACde,SAAS,EAAEnC,UAAU,CACnB4E,MAAM,GAAG,0CAA0C,GAAG,eAAe,EACrE,8EACF,CAAE;0BAAA3C,QAAA,gBAEFnC,OAAA,CAACqD,IAAI,CAACvB,IAAI;4BAACO,SAAS,EAAC;0BAAS;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjCnD,OAAA;4BAAAmC,QAAA,EAAOkB,IAAI,CAACxB;0BAAI;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBACP,GAZaE,IAAI,CAACxB,IAAI;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAad,CACZ,CAAC,eACFnD,OAAA;wBAAKqC,SAAS,EAAC,oCAAoC;wBAAAF,QAAA,eACjDnC,OAAA,CAAClB,IAAI,CAAC+F,IAAI;0BAAA1C,QAAA,EACPA,CAAC;4BAAE2C;0BAAO,CAAC,kBACV9E,OAAA;4BACE+E,OAAO,EAAE/D,MAAO;4BAChBqB,SAAS,EAAEnC,UAAU,CACnB4E,MAAM,GAAG,wBAAwB,GAAG,cAAc,EAClD,qFACF,CAAE;4BAAA3C,QAAA,gBAEFnC,OAAA,CAACJ,yBAAyB;8BAACyC,SAAS,EAAC;4BAAS;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACjDnD,OAAA;8BAAAmC,QAAA,EAAM;4BAAQ;8BAAAa,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBACT;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,gBAEPnD,OAAA;gBAAKqC,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1CnC,OAAA,CAACtB,IAAI;kBAAC6D,EAAE,EAAC,QAAQ;kBAAAJ,QAAA,eACfnC,OAAA,CAACpB,MAAM,CAACiF,MAAM;oBACZpB,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEW,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCQ,QAAQ,EAAE;sBAAEnB,KAAK,EAAE;oBAAK,CAAE;oBAC1BN,SAAS,EAAE,4EACT5B,UAAU,GACN,yHAAyH,GACzH,mIAAmI,EACtI;oBAAA0B,QAAA,EACJ;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPnD,OAAA,CAACtB,IAAI;kBAAC6D,EAAE,EAAC,WAAW;kBAAAJ,QAAA,eAClBnC,OAAA,CAACpB,MAAM,CAACiF,MAAM;oBACZpB,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEW,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCQ,QAAQ,EAAE;sBAAEnB,KAAK,EAAE;oBAAK,CAAE;oBAC1BN,SAAS,EAAC,gOAAgO;oBAAAF,QAAA,EAC3O;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,eAGDnD,OAAA;gBAAKqC,SAAS,EAAC,WAAW;gBAAAF,QAAA,eACxBnC,OAAA,CAACnB,UAAU,CAACkF,MAAM;kBAAC1B,SAAS,EAAE,+FAC5B5B,UAAU,GACN,oEAAoE,GACpE,qGAAqG,+EAC3B;kBAAA0B,QAAA,gBAC9EnC,OAAA;oBAAMqC,SAAS,EAAC,SAAS;oBAAAF,QAAA,EAAC;kBAAc;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CnD,OAAA,CAACpB,MAAM,CAAC4D,GAAG;oBACTwC,OAAO,EAAE;sBAAEtC,MAAM,EAAEJ,IAAI,GAAG,GAAG,GAAG;oBAAE,CAAE;oBACpCM,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAV,QAAA,EAE7BG,IAAI,gBACHtC,OAAA,CAACf,SAAS;sBAACoD,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE1DnD,OAAA,CAAChB,SAAS;sBAACqD,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA,CAACnB,UAAU,CAACoG,KAAK;UAAC5C,SAAS,EAAC,WAAW;UAAAF,QAAA,eACrCnC,OAAA,CAACpB,MAAM,CAAC4D,GAAG;YACT0C,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,CAAC;cAAE9B,CAAC,EAAE,CAAC;YAAG,CAAE;YAC3C0B,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,MAAM;cAAE9B,CAAC,EAAE;YAAE,CAAE;YAC9C+B,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,CAAC;cAAE9B,CAAC,EAAE,CAAC;YAAG,CAAE;YACxCV,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEyC,IAAI,EAAE;YAAY,CAAE;YACjDjD,SAAS,EAAC,kEAAkE;YAAAF,QAAA,eAE5EnC,OAAA;cAAKqC,SAAS,EAAC,0BAA0B;cAAAF,QAAA,gBAEvCnC,OAAA,CAACpB,MAAM,CAAC4D,GAAG;gBACT0C,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCP,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE;gBAAE,CAAE;gBAC9B3C,UAAU,EAAE;kBAAE4C,KAAK,EAAE;gBAAI,CAAE;gBAC3BnD,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBAEzBnC,OAAA;kBAAKqC,SAAS,EAAC,sEAAsE;kBAAAF,QAAA,eACnFnC,OAAA,CAACb,mBAAmB;oBAACkD,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNnD,OAAA;kBACE8C,IAAI,EAAC,MAAM;kBACXS,WAAW,EAAC,wBAAwB;kBACpCC,KAAK,EAAE7C,WAAY;kBACnB8C,QAAQ,EAAGvC,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGzC,CAAC,IAAKA,CAAC,CAAC0C,GAAG,KAAK,OAAO,IAAI3C,YAAY,CAACC,CAAC,CAAE;kBACvDmB,SAAS,EAAC;gBAAgP;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbnD,OAAA;gBAAKqC,SAAS,EAAC,WAAW;gBAAAF,QAAA,EACvBP,UAAU,CAACwB,GAAG,CAAC,CAACC,IAAI,EAAEoC,KAAK,kBAC1BzF,OAAA,CAACpB,MAAM,CAAC4D,GAAG;kBAET0C,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCP,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAE,CAAE;kBAC9B3C,UAAU,EAAE;oBAAE4C,KAAK,EAAE,GAAG,IAAIC,KAAK,GAAG,CAAC;kBAAE,CAAE;kBAAAtD,QAAA,eAEzCnC,OAAA,CAACnB,UAAU,CAACkF,MAAM;oBAChB3B,EAAE,EAAE1D,IAAK;oBACT6D,EAAE,EAAEc,IAAI,CAAC/B,IAAK;oBACde,SAAS,EAAEnC,UAAU,CACnB8B,QAAQ,CAACqB,IAAI,CAAC/B,IAAI,CAAC,GACf,iFAAiF,GACjF,oEAAoE,EACxE,qFACF,CAAE;oBAAAa,QAAA,gBAEFnC,OAAA,CAACqD,IAAI,CAACvB,IAAI;sBAACO,SAAS,EAAEnC,UAAU,CAC9B8B,QAAQ,CAACqB,IAAI,CAAC/B,IAAI,CAAC,GAAG,YAAY,GAAG,iDAAiD,EACtF,SACF;oBAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLnD,OAAA;sBAAMqC,SAAS,EAAC,uBAAuB;sBAAAF,QAAA,EAAEkB,IAAI,CAACxB;oBAAI;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC,GApBfE,IAAI,CAACxB,IAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBJ,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL,CAACpC,eAAe,iBACff,OAAA,CAACpB,MAAM,CAAC4D,GAAG;gBACT0C,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAE7B,CAAC,EAAE;gBAAG,CAAE;gBAC/B0B,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAE7B,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAE4C,KAAK,EAAE;gBAAI,CAAE;gBAC3BnD,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,gBAE/BnC,OAAA,CAACtB,IAAI;kBAAC6D,EAAE,EAAC,QAAQ;kBAACF,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eAClCnC,OAAA,CAACnB,UAAU,CAACkF,MAAM;oBAChB3B,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,wJAAwJ;oBAAAF,QAAA,EACnK;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACPnD,OAAA,CAACtB,IAAI;kBAAC6D,EAAE,EAAC,WAAW;kBAACF,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eACrCnC,OAAA,CAACnB,UAAU,CAACkF,MAAM;oBAChB3B,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,8MAA8M;oBAAAF,QAAA,EACzN;kBAED;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA,eACnB;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAGbnD,OAAA;MAAKqC,SAAS,EAAC;IAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eAC5B,CAAC;AAEP,CAAC;AAAC3C,EAAA,CAjZID,gBAAgB;EAAA,QAGH5B,WAAW,EACcmB,OAAO;AAAA;AAAA4F,EAAA,GAJ7CnF,gBAAgB;AAmZtB,eAAeA,gBAAgB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}