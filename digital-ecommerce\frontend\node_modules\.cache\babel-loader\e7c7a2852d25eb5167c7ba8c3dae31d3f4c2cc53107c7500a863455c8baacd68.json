{"ast": null, "code": "import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\nconst {\n  schedule: frame,\n  cancel: cancelFrame,\n  state: frameData,\n  steps: frameSteps\n} = /* @__PURE__ */createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\nexport { cancelFrame, frame, frameData, frameSteps };", "map": {"version": 3, "names": ["noop", "createRenderBatcher", "schedule", "frame", "cancel", "cancelFrame", "state", "frameData", "steps", "frameSteps", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/frameloop/frame.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,mBAAmB,QAAQ,eAAe;AAEnD,MAAM;EAAEC,QAAQ,EAAEC,KAAK;EAAEC,MAAM,EAAEC,WAAW;EAAEC,KAAK,EAAEC,SAAS;EAAEC,KAAK,EAAEC;AAAY,CAAC,GAAG,eAAgBR,mBAAmB,CAAC,OAAOS,qBAAqB,KAAK,WAAW,GAAGA,qBAAqB,GAAGV,IAAI,EAAE,IAAI,CAAC;AAE7M,SAASK,WAAW,EAAEF,KAAK,EAAEI,SAAS,EAAEE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}