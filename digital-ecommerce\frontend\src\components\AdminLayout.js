import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  ShoppingBagIcon,
  TagIcon,
  PhotoIcon,
  ChartBarIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  UserIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { useAdmin } from '../contexts/AdminContext';

const AdminLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { admin, adminLogout, hasPermission } = useAdmin();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    adminLogout();
    navigate('/admin/login');
  };

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: HomeIcon,
      permission: null
    },
    {
      name: 'Products',
      href: '/admin/products',
      icon: ShoppingBagIcon,
      permission: 'products'
    },
    {
      name: 'Categories',
      href: '/admin/categories',
      icon: TagIcon,
      permission: 'categories'
    },
    {
      name: 'Inventory',
      href: '/admin/inventory',
      icon: ClipboardDocumentListIcon,
      permission: 'inventory'
    },
    {
      name: 'Media',
      href: '/admin/media',
      icon: PhotoIcon,
      permission: 'media'
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: ChartBarIcon,
      permission: 'analytics'
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: CogIcon,
      permission: 'settings'
    }
  ];

  const filteredNavItems = navigationItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  const isActive = (path) => location.pathname === path;

  const Sidebar = ({ mobile = false }) => (
    <div className="flex flex-col h-full bg-white border-r border-gray-200 shadow-lg">
      {/* Logo */}
      <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-gradient-to-r from-light-orange-50 to-white">
        <Link to="/admin/dashboard" className="flex items-center space-x-3 group">
          <div className="w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow">
            <ShoppingBagIcon className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900 group-hover:text-light-orange-700 transition-colors">
            Admin Panel
          </span>
        </Link>
        {mobile && (
          <button
            onClick={() => setSidebarOpen(false)}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        {filteredNavItems.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            onClick={mobile ? () => setSidebarOpen(false) : undefined}
            className={`group flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              isActive(item.href)
                ? 'bg-light-orange-100 text-light-orange-800 shadow-sm border-l-4 border-light-orange-500'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
            }`}
          >
            <item.icon className={`w-5 h-5 transition-colors ${
              isActive(item.href) ? 'text-light-orange-600' : 'text-gray-500 group-hover:text-gray-700'
            }`} />
            <span className="font-medium">{item.name}</span>
          </Link>
        ))}
      </nav>

      {/* User Info & Logout */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3 p-3 rounded-lg bg-white shadow-sm border border-gray-100">
          <div className="w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-md">
            <UserIcon className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-gray-900 truncate">
              {admin?.firstName} {admin?.lastName}
            </p>
            <p className="text-xs text-gray-600 truncate capitalize">
              {admin?.role?.replace('_', ' ')}
            </p>
          </div>
        </div>
        <button
          onClick={handleLogout}
          className="w-full mt-3 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 border border-red-200 hover:border-red-300"
        >
          <ArrowRightOnRectangleIcon className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <Sidebar />
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <div className="absolute inset-0 bg-black opacity-50" />
            </motion.div>
            <motion.div
              initial={{ x: -256 }}
              animate={{ x: 0 }}
              exit={{ x: -256 }}
              transition={{ type: "spring", damping: 30, stiffness: 300 }}
              className="fixed inset-y-0 left-0 z-50 w-64 lg:hidden"
            >
              <Sidebar mobile />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="lg:pl-64">
        {/* Top Header */}
        <header className="sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 bg-white border-b border-gray-200 shadow-sm">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Bars3Icon className="w-6 h-6" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Admin Dashboard
              </h1>
              <p className="text-sm text-gray-600 hidden sm:block">
                Welcome back, {admin?.firstName}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-light-orange-50 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-700">Online</span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
