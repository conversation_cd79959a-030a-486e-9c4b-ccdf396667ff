{"ast": null, "code": "import { warning } from 'motion-utils';\nimport { hex } from '../../value/types/color/hex.mjs';\nimport { hsla } from '../../value/types/color/hsla.mjs';\nimport { hslaToRgba } from '../../value/types/color/hsla-to-rgba.mjs';\nimport { rgba } from '../../value/types/color/rgba.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber } from './number.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n  const fromExpo = from * from;\n  const expo = v * (to * to - fromExpo) + fromExpo;\n  return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = v => colorTypes.find(type => type.test(v));\nfunction asRGBA(color) {\n  const type = getColorType(color);\n  warning(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n  if (!Boolean(type)) return false;\n  let model = type.parse(color);\n  if (type === hsla) {\n    // TODO Remove this cast - needed since Motion's stricter typing\n    model = hslaToRgba(model);\n  }\n  return model;\n}\nconst mixColor = (from, to) => {\n  const fromRGBA = asRGBA(from);\n  const toRGBA = asRGBA(to);\n  if (!fromRGBA || !toRGBA) {\n    return mixImmediate(from, to);\n  }\n  const blended = {\n    ...fromRGBA\n  };\n  return v => {\n    blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n    blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n    blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n    blended.alpha = mixNumber(fromRGBA.alpha, toRGBA.alpha, v);\n    return rgba.transform(blended);\n  };\n};\nexport { mixColor, mixLinearColor };", "map": {"version": 3, "names": ["warning", "hex", "hsla", "hslaToRgba", "rgba", "mixImmediate", "mixNumber", "mixLinearColor", "from", "to", "v", "fromExpo", "expo", "Math", "sqrt", "colorTypes", "getColorType", "find", "type", "test", "asRGBA", "color", "Boolean", "model", "parse", "mixColor", "fromRGBA", "toRGBA", "blended", "red", "green", "blue", "alpha", "transform"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/mix/color.mjs"], "sourcesContent": ["import { warning } from 'motion-utils';\nimport { hex } from '../../value/types/color/hex.mjs';\nimport { hsla } from '../../value/types/color/hsla.mjs';\nimport { hslaToRgba } from '../../value/types/color/hsla-to-rgba.mjs';\nimport { rgba } from '../../value/types/color/rgba.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber } from './number.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const expo = v * (to * to - fromExpo) + fromExpo;\n    return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    warning(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n    if (!Boolean(type))\n        return false;\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    if (!fromRGBA || !toRGBA) {\n        return mixImmediate(from, to);\n    }\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mixNumber(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,GAAG,QAAQ,iCAAiC;AACrD,SAASC,IAAI,QAAQ,kCAAkC;AACvD,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,IAAI,QAAQ,kCAAkC;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,cAAc;;AAExC;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,CAAC,KAAK;EACpC,MAAMC,QAAQ,GAAGH,IAAI,GAAGA,IAAI;EAC5B,MAAMI,IAAI,GAAGF,CAAC,IAAID,EAAE,GAAGA,EAAE,GAAGE,QAAQ,CAAC,GAAGA,QAAQ;EAChD,OAAOC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC;AACzC,CAAC;AACD,MAAMG,UAAU,GAAG,CAACd,GAAG,EAAEG,IAAI,EAAEF,IAAI,CAAC;AACpC,MAAMc,YAAY,GAAIN,CAAC,IAAKK,UAAU,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAACT,CAAC,CAAC,CAAC;AACnE,SAASU,MAAMA,CAACC,KAAK,EAAE;EACnB,MAAMH,IAAI,GAAGF,YAAY,CAACK,KAAK,CAAC;EAChCrB,OAAO,CAACsB,OAAO,CAACJ,IAAI,CAAC,EAAE,IAAIG,KAAK,sEAAsE,CAAC;EACvG,IAAI,CAACC,OAAO,CAACJ,IAAI,CAAC,EACd,OAAO,KAAK;EAChB,IAAIK,KAAK,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;EAC7B,IAAIH,IAAI,KAAKhB,IAAI,EAAE;IACf;IACAqB,KAAK,GAAGpB,UAAU,CAACoB,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AAChB;AACA,MAAME,QAAQ,GAAGA,CAACjB,IAAI,EAAEC,EAAE,KAAK;EAC3B,MAAMiB,QAAQ,GAAGN,MAAM,CAACZ,IAAI,CAAC;EAC7B,MAAMmB,MAAM,GAAGP,MAAM,CAACX,EAAE,CAAC;EACzB,IAAI,CAACiB,QAAQ,IAAI,CAACC,MAAM,EAAE;IACtB,OAAOtB,YAAY,CAACG,IAAI,EAAEC,EAAE,CAAC;EACjC;EACA,MAAMmB,OAAO,GAAG;IAAE,GAAGF;EAAS,CAAC;EAC/B,OAAQhB,CAAC,IAAK;IACVkB,OAAO,CAACC,GAAG,GAAGtB,cAAc,CAACmB,QAAQ,CAACG,GAAG,EAAEF,MAAM,CAACE,GAAG,EAAEnB,CAAC,CAAC;IACzDkB,OAAO,CAACE,KAAK,GAAGvB,cAAc,CAACmB,QAAQ,CAACI,KAAK,EAAEH,MAAM,CAACG,KAAK,EAAEpB,CAAC,CAAC;IAC/DkB,OAAO,CAACG,IAAI,GAAGxB,cAAc,CAACmB,QAAQ,CAACK,IAAI,EAAEJ,MAAM,CAACI,IAAI,EAAErB,CAAC,CAAC;IAC5DkB,OAAO,CAACI,KAAK,GAAG1B,SAAS,CAACoB,QAAQ,CAACM,KAAK,EAAEL,MAAM,CAACK,KAAK,EAAEtB,CAAC,CAAC;IAC1D,OAAON,IAAI,CAAC6B,SAAS,CAACL,OAAO,CAAC;EAClC,CAAC;AACL,CAAC;AAED,SAASH,QAAQ,EAAElB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}