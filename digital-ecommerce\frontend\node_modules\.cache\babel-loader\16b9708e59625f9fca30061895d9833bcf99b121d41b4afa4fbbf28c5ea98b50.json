{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\ProductContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { products as initialProducts, categories as initialCategories } from '../data/products';\nimport { pcGamingProducts, pcGamingCategories, pcGamingBundles, compatibilityMatrix } from '../data/pcGamingProducts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductContext = /*#__PURE__*/createContext();\nexport const useProducts = () => {\n  _s();\n  const context = useContext(ProductContext);\n  if (!context) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n};\n_s(useProducts, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ProductProvider = ({\n  children\n}) => {\n  _s2();\n  const [products, setProducts] = useState([...initialProducts, ...pcGamingProducts]);\n  const [categories, setCategories] = useState(initialCategories);\n  const [pcCategories, setPcCategories] = useState(pcGamingCategories);\n  const [bundles, setBundles] = useState(pcGamingBundles);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load products from localStorage on mount\n  useEffect(() => {\n    const savedProducts = localStorage.getItem('products');\n    const savedCategories = localStorage.getItem('categories');\n    if (savedProducts) {\n      try {\n        setProducts(JSON.parse(savedProducts));\n      } catch (error) {\n        console.error('Error loading products from localStorage:', error);\n      }\n    }\n    if (savedCategories) {\n      try {\n        setCategories(JSON.parse(savedCategories));\n      } catch (error) {\n        console.error('Error loading categories from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save products to localStorage whenever products change\n  useEffect(() => {\n    localStorage.setItem('products', JSON.stringify(products));\n  }, [products]);\n\n  // Save categories to localStorage whenever categories change\n  useEffect(() => {\n    localStorage.setItem('categories', JSON.stringify(categories));\n  }, [categories]);\n  const addProduct = async productData => {\n    setIsLoading(true);\n    try {\n      var _processedImages$;\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Generate unique ID\n      const newId = `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n\n      // Process images (in a real app, you'd upload to a server)\n      const processedImages = productData.images.map((image, index) => ({\n        id: `img-${newId}-${index}`,\n        url: image.url,\n        // In production, this would be the uploaded URL\n        alt: productData.name,\n        isPrimary: index === 0\n      }));\n\n      // Create new product object\n      const newProduct = {\n        id: newId,\n        name: productData.name,\n        description: productData.description,\n        shortDescription: productData.shortDescription,\n        price: parseFloat(productData.price),\n        discountPrice: productData.discountPrice ? parseFloat(productData.discountPrice) : null,\n        currency: productData.currency,\n        category: productData.category,\n        subcategory: productData.subcategory,\n        type: productData.type,\n        stockCount: productData.type === 'physical' ? parseInt(productData.stockCount) : null,\n        sku: productData.sku,\n        tags: productData.tags,\n        keywords: productData.keywords,\n        isActive: productData.isActive,\n        isFeatured: productData.isFeatured,\n        specifications: productData.specifications,\n        images: processedImages,\n        image: (_processedImages$ = processedImages[0]) === null || _processedImages$ === void 0 ? void 0 : _processedImages$.url,\n        // Main image for backward compatibility\n        inStock: productData.type === 'digital' ? true : parseInt(productData.stockCount) > 0,\n        rating: 0,\n        reviews: 0,\n        sold: 0,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Add to products list\n      setProducts(prevProducts => [newProduct, ...prevProducts]);\n      setIsLoading(false);\n      return {\n        success: true,\n        product: newProduct\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding product:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const updateProduct = async (productId, updates) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setProducts(prevProducts => prevProducts.map(product => product.id === productId ? {\n        ...product,\n        ...updates,\n        updatedAt: new Date().toISOString()\n      } : product));\n      setIsLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating product:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const deleteProduct = async productId => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setProducts(prevProducts => prevProducts.filter(product => product.id !== productId));\n      setIsLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting product:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const addCategory = async categoryData => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      const newId = `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      const newCategory = {\n        id: newId,\n        name: categoryData.name,\n        description: categoryData.description,\n        icon: categoryData.icon,\n        subcategories: categoryData.subcategories || [],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      setCategories(prevCategories => [...prevCategories, newCategory]);\n      setIsLoading(false);\n      return {\n        success: true,\n        category: newCategory\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding category:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const updateCategory = async (categoryId, updates) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setCategories(prevCategories => prevCategories.map(category => category.id === categoryId ? {\n        ...category,\n        ...updates,\n        updatedAt: new Date().toISOString()\n      } : category));\n      setIsLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating category:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const deleteCategory = async categoryId => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Check if any products use this category\n      const productsUsingCategory = products.filter(product => product.category === categoryId);\n      if (productsUsingCategory.length > 0) {\n        throw new Error(`Cannot delete category. ${productsUsingCategory.length} products are using this category.`);\n      }\n      setCategories(prevCategories => prevCategories.filter(category => category.id !== categoryId));\n      setIsLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting category:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const getProductById = productId => {\n    return products.find(product => product.id === productId);\n  };\n  const getCategoryById = categoryId => {\n    return categories.find(category => category.id === categoryId);\n  };\n  const getProductsByCategory = categoryId => {\n    return products.filter(product => product.category === categoryId);\n  };\n  const searchProducts = query => {\n    const lowercaseQuery = query.toLowerCase();\n    return products.filter(product => product.name.toLowerCase().includes(lowercaseQuery) || product.description.toLowerCase().includes(lowercaseQuery) || product.tags && product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) || product.keywords && product.keywords.toLowerCase().includes(lowercaseQuery));\n  };\n\n  // PC Gaming specific functions\n  const getPcGamingProducts = (subcategory = null, componentType = null) => {\n    return products.filter(product => {\n      const isPcGaming = product.category === 'pc-gaming';\n      const matchesSubcategory = !subcategory || product.subcategory === subcategory;\n      const matchesComponentType = !componentType || product.componentType === componentType;\n      return isPcGaming && matchesSubcategory && matchesComponentType;\n    });\n  };\n  const checkCompatibility = components => {\n    // Basic compatibility checking logic\n    const cpu = components.find(c => c.componentType === 'cpu');\n    const motherboard = components.find(c => c.componentType === 'motherboard');\n    const issues = [];\n    if (cpu && motherboard) {\n      var _cpu$compatibility, _motherboard$compatib;\n      const cpuSocket = (_cpu$compatibility = cpu.compatibility) === null || _cpu$compatibility === void 0 ? void 0 : _cpu$compatibility.socket;\n      const mbSocket = (_motherboard$compatib = motherboard.compatibility) === null || _motherboard$compatib === void 0 ? void 0 : _motherboard$compatib.socket;\n      if (cpuSocket && mbSocket && cpuSocket !== mbSocket) {\n        issues.push(`CPU socket ${cpuSocket} is not compatible with motherboard socket ${mbSocket}`);\n      }\n    }\n    return {\n      compatible: issues.length === 0,\n      issues\n    };\n  };\n  const calculateBundlePrice = productIds => {\n    const bundleProducts = products.filter(p => productIds.includes(p.id));\n    const totalPrice = bundleProducts.reduce((sum, product) => sum + product.price, 0);\n    const bundleDiscount = totalPrice * 0.05; // 5% bundle discount\n\n    return {\n      originalPrice: totalPrice,\n      bundlePrice: totalPrice - bundleDiscount,\n      savings: bundleDiscount,\n      products: bundleProducts\n    };\n  };\n  const getRecommendedComponents = baseComponent => {\n    // Simple recommendation logic based on component type and price range\n    const priceRange = {\n      budget: baseComponent.price < 200,\n      midRange: baseComponent.price >= 200 && baseComponent.price < 500,\n      highEnd: baseComponent.price >= 500\n    };\n    return products.filter(product => {\n      if (product.category !== 'pc-gaming' || product.subcategory !== 'pc-component') {\n        return false;\n      }\n\n      // Don't recommend the same component type\n      if (product.componentType === baseComponent.componentType) {\n        return false;\n      }\n\n      // Match price tier\n      if (priceRange.budget && product.price > 300) return false;\n      if (priceRange.midRange && (product.price < 150 || product.price > 800)) return false;\n      if (priceRange.highEnd && product.price < 400) return false;\n      return true;\n    }).slice(0, 6); // Return top 6 recommendations\n  };\n  const value = {\n    products,\n    categories,\n    pcCategories,\n    bundles,\n    isLoading,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getProductById,\n    getCategoryById,\n    getProductsByCategory,\n    searchProducts,\n    // PC Gaming specific functions\n    getPcGamingProducts,\n    checkCompatibility,\n    calculateBundlePrice,\n    getRecommendedComponents,\n    compatibilityMatrix\n  };\n  return /*#__PURE__*/_jsxDEV(ProductContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s2(ProductProvider, \"Zo5skCAFb3QR+4YQKwzFU2Vdp8E=\");\n_c = ProductProvider;\nexport default ProductContext;\nvar _c;\n$RefreshReg$(_c, \"ProductProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "products", "initialProducts", "categories", "initialCategories", "pcGamingProducts", "pcGamingCategories", "pcGamingBundles", "compatibilityMatrix", "jsxDEV", "_jsxDEV", "ProductContext", "useProducts", "_s", "context", "Error", "ProductProvider", "children", "_s2", "setProducts", "setCategories", "pcCategories", "setPcCategories", "bundles", "setBundles", "isLoading", "setIsLoading", "savedProducts", "localStorage", "getItem", "savedCategories", "JSON", "parse", "error", "console", "setItem", "stringify", "addProduct", "productData", "_processedImages$", "Promise", "resolve", "setTimeout", "newId", "Date", "now", "Math", "random", "toString", "substr", "processedImages", "images", "map", "image", "index", "id", "url", "alt", "name", "isPrimary", "newProduct", "description", "shortDescription", "price", "parseFloat", "discountPrice", "currency", "category", "subcategory", "type", "stockCount", "parseInt", "sku", "tags", "keywords", "isActive", "isFeatured", "specifications", "inStock", "rating", "reviews", "sold", "createdAt", "toISOString", "updatedAt", "prevProducts", "success", "product", "message", "updateProduct", "productId", "updates", "deleteProduct", "filter", "addCategory", "categoryData", "newCategory", "icon", "subcategories", "prevCategories", "updateCategory", "categoryId", "deleteCategory", "productsUsingCategory", "length", "getProductById", "find", "getCategoryById", "getProductsByCategory", "searchProducts", "query", "lowercase<PERSON><PERSON>y", "toLowerCase", "includes", "some", "tag", "getPcGamingProducts", "componentType", "isPcGaming", "matchesSubcategory", "matchesComponentType", "checkCompatibility", "components", "cpu", "c", "motherboard", "issues", "_cpu$compatibility", "_motherboard$compatib", "cpuSocket", "compatibility", "socket", "mbSocket", "push", "compatible", "calculateBundlePrice", "productIds", "bundleProducts", "p", "totalPrice", "reduce", "sum", "bundleDiscount", "originalPrice", "bundlePrice", "savings", "getRecommendedComponents", "baseComponent", "priceRange", "budget", "midRange", "highEnd", "slice", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/ProductContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { products as initialProducts, categories as initialCategories } from '../data/products';\nimport { pcGamingProducts, pcGamingCategories, pcGamingBundles, compatibilityMatrix } from '../data/pcGamingProducts';\n\nconst ProductContext = createContext();\n\nexport const useProducts = () => {\n  const context = useContext(ProductContext);\n  if (!context) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n};\n\nexport const ProductProvider = ({ children }) => {\n  const [products, setProducts] = useState([...initialProducts, ...pcGamingProducts]);\n  const [categories, setCategories] = useState(initialCategories);\n  const [pcCategories, setPcCategories] = useState(pcGamingCategories);\n  const [bundles, setBundles] = useState(pcGamingBundles);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load products from localStorage on mount\n  useEffect(() => {\n    const savedProducts = localStorage.getItem('products');\n    const savedCategories = localStorage.getItem('categories');\n    \n    if (savedProducts) {\n      try {\n        setProducts(JSON.parse(savedProducts));\n      } catch (error) {\n        console.error('Error loading products from localStorage:', error);\n      }\n    }\n    \n    if (savedCategories) {\n      try {\n        setCategories(JSON.parse(savedCategories));\n      } catch (error) {\n        console.error('Error loading categories from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save products to localStorage whenever products change\n  useEffect(() => {\n    localStorage.setItem('products', JSON.stringify(products));\n  }, [products]);\n\n  // Save categories to localStorage whenever categories change\n  useEffect(() => {\n    localStorage.setItem('categories', JSON.stringify(categories));\n  }, [categories]);\n\n  const addProduct = async (productData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Generate unique ID\n      const newId = `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      // Process images (in a real app, you'd upload to a server)\n      const processedImages = productData.images.map((image, index) => ({\n        id: `img-${newId}-${index}`,\n        url: image.url, // In production, this would be the uploaded URL\n        alt: productData.name,\n        isPrimary: index === 0\n      }));\n\n      // Create new product object\n      const newProduct = {\n        id: newId,\n        name: productData.name,\n        description: productData.description,\n        shortDescription: productData.shortDescription,\n        price: parseFloat(productData.price),\n        discountPrice: productData.discountPrice ? parseFloat(productData.discountPrice) : null,\n        currency: productData.currency,\n        category: productData.category,\n        subcategory: productData.subcategory,\n        type: productData.type,\n        stockCount: productData.type === 'physical' ? parseInt(productData.stockCount) : null,\n        sku: productData.sku,\n        tags: productData.tags,\n        keywords: productData.keywords,\n        isActive: productData.isActive,\n        isFeatured: productData.isFeatured,\n        specifications: productData.specifications,\n        images: processedImages,\n        image: processedImages[0]?.url, // Main image for backward compatibility\n        inStock: productData.type === 'digital' ? true : parseInt(productData.stockCount) > 0,\n        rating: 0,\n        reviews: 0,\n        sold: 0,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Add to products list\n      setProducts(prevProducts => [newProduct, ...prevProducts]);\n      \n      setIsLoading(false);\n      return { success: true, product: newProduct };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const updateProduct = async (productId, updates) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setProducts(prevProducts =>\n        prevProducts.map(product =>\n          product.id === productId\n            ? { ...product, ...updates, updatedAt: new Date().toISOString() }\n            : product\n        )\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const deleteProduct = async (productId) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setProducts(prevProducts =>\n        prevProducts.filter(product => product.id !== productId)\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const addCategory = async (categoryData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newId = `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      const newCategory = {\n        id: newId,\n        name: categoryData.name,\n        description: categoryData.description,\n        icon: categoryData.icon,\n        subcategories: categoryData.subcategories || [],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      setCategories(prevCategories => [...prevCategories, newCategory]);\n      \n      setIsLoading(false);\n      return { success: true, category: newCategory };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const updateCategory = async (categoryId, updates) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setCategories(prevCategories =>\n        prevCategories.map(category =>\n          category.id === categoryId\n            ? { ...category, ...updates, updatedAt: new Date().toISOString() }\n            : category\n        )\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const deleteCategory = async (categoryId) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      // Check if any products use this category\n      const productsUsingCategory = products.filter(product => product.category === categoryId);\n      if (productsUsingCategory.length > 0) {\n        throw new Error(`Cannot delete category. ${productsUsingCategory.length} products are using this category.`);\n      }\n      \n      setCategories(prevCategories =>\n        prevCategories.filter(category => category.id !== categoryId)\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const getProductById = (productId) => {\n    return products.find(product => product.id === productId);\n  };\n\n  const getCategoryById = (categoryId) => {\n    return categories.find(category => category.id === categoryId);\n  };\n\n  const getProductsByCategory = (categoryId) => {\n    return products.filter(product => product.category === categoryId);\n  };\n\n  const searchProducts = (query) => {\n    const lowercaseQuery = query.toLowerCase();\n    return products.filter(product =>\n      product.name.toLowerCase().includes(lowercaseQuery) ||\n      product.description.toLowerCase().includes(lowercaseQuery) ||\n      (product.tags && product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))) ||\n      (product.keywords && product.keywords.toLowerCase().includes(lowercaseQuery))\n    );\n  };\n\n  // PC Gaming specific functions\n  const getPcGamingProducts = (subcategory = null, componentType = null) => {\n    return products.filter(product => {\n      const isPcGaming = product.category === 'pc-gaming';\n      const matchesSubcategory = !subcategory || product.subcategory === subcategory;\n      const matchesComponentType = !componentType || product.componentType === componentType;\n\n      return isPcGaming && matchesSubcategory && matchesComponentType;\n    });\n  };\n\n  const checkCompatibility = (components) => {\n    // Basic compatibility checking logic\n    const cpu = components.find(c => c.componentType === 'cpu');\n    const motherboard = components.find(c => c.componentType === 'motherboard');\n\n    const issues = [];\n\n    if (cpu && motherboard) {\n      const cpuSocket = cpu.compatibility?.socket;\n      const mbSocket = motherboard.compatibility?.socket;\n      if (cpuSocket && mbSocket && cpuSocket !== mbSocket) {\n        issues.push(`CPU socket ${cpuSocket} is not compatible with motherboard socket ${mbSocket}`);\n      }\n    }\n\n    return {\n      compatible: issues.length === 0,\n      issues\n    };\n  };\n\n  const calculateBundlePrice = (productIds) => {\n    const bundleProducts = products.filter(p => productIds.includes(p.id));\n    const totalPrice = bundleProducts.reduce((sum, product) => sum + product.price, 0);\n    const bundleDiscount = totalPrice * 0.05; // 5% bundle discount\n\n    return {\n      originalPrice: totalPrice,\n      bundlePrice: totalPrice - bundleDiscount,\n      savings: bundleDiscount,\n      products: bundleProducts\n    };\n  };\n\n  const getRecommendedComponents = (baseComponent) => {\n    // Simple recommendation logic based on component type and price range\n    const priceRange = {\n      budget: baseComponent.price < 200,\n      midRange: baseComponent.price >= 200 && baseComponent.price < 500,\n      highEnd: baseComponent.price >= 500\n    };\n\n    return products.filter(product => {\n      if (product.category !== 'pc-gaming' || product.subcategory !== 'pc-component') {\n        return false;\n      }\n\n      // Don't recommend the same component type\n      if (product.componentType === baseComponent.componentType) {\n        return false;\n      }\n\n      // Match price tier\n      if (priceRange.budget && product.price > 300) return false;\n      if (priceRange.midRange && (product.price < 150 || product.price > 800)) return false;\n      if (priceRange.highEnd && product.price < 400) return false;\n\n      return true;\n    }).slice(0, 6); // Return top 6 recommendations\n  };\n\n  const value = {\n    products,\n    categories,\n    pcCategories,\n    bundles,\n    isLoading,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getProductById,\n    getCategoryById,\n    getProductsByCategory,\n    searchProducts,\n    // PC Gaming specific functions\n    getPcGamingProducts,\n    checkCompatibility,\n    calculateBundlePrice,\n    getRecommendedComponents,\n    compatibilityMatrix\n  };\n\n  return (\n    <ProductContext.Provider value={value}>\n      {children}\n    </ProductContext.Provider>\n  );\n};\n\nexport default ProductContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,QAAQ,IAAIC,eAAe,EAAEC,UAAU,IAAIC,iBAAiB,QAAQ,kBAAkB;AAC/F,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtH,MAAMC,cAAc,gBAAGd,aAAa,CAAC,CAAC;AAEtC,OAAO,MAAMe,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,OAAO,GAAGhB,UAAU,CAACa,cAAc,CAAC;EAC1C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,WAAW;AAQxB,OAAO,MAAMI,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC/C,MAAM,CAACjB,QAAQ,EAAEkB,WAAW,CAAC,GAAGpB,QAAQ,CAAC,CAAC,GAAGG,eAAe,EAAE,GAAGG,gBAAgB,CAAC,CAAC;EACnF,MAAM,CAACF,UAAU,EAAEiB,aAAa,CAAC,GAAGrB,QAAQ,CAACK,iBAAiB,CAAC;EAC/D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAACO,kBAAkB,CAAC;EACpE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAACQ,eAAe,CAAC;EACvD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2B,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACtD,MAAMC,eAAe,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAE1D,IAAIF,aAAa,EAAE;MACjB,IAAI;QACFR,WAAW,CAACY,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC,CAAC;MACxC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE;IACF;IAEA,IAAIH,eAAe,EAAE;MACnB,IAAI;QACFV,aAAa,CAACW,IAAI,CAACC,KAAK,CAACF,eAAe,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACrE;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,SAAS,CAAC,MAAM;IACd4B,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACnC,QAAQ,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACAD,SAAS,CAAC,MAAM;IACd4B,YAAY,CAACO,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACjC,UAAU,CAAC,CAAC;EAChE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMkC,UAAU,GAAG,MAAOC,WAAW,IAAK;IACxCZ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MAAA,IAAAa,iBAAA;MACF;MACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,KAAK,GAAG,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;MAEhF;MACA,MAAMC,eAAe,GAAGZ,WAAW,CAACa,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;QAChEC,EAAE,EAAE,OAAOZ,KAAK,IAAIW,KAAK,EAAE;QAC3BE,GAAG,EAAEH,KAAK,CAACG,GAAG;QAAE;QAChBC,GAAG,EAAEnB,WAAW,CAACoB,IAAI;QACrBC,SAAS,EAAEL,KAAK,KAAK;MACvB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMM,UAAU,GAAG;QACjBL,EAAE,EAAEZ,KAAK;QACTe,IAAI,EAAEpB,WAAW,CAACoB,IAAI;QACtBG,WAAW,EAAEvB,WAAW,CAACuB,WAAW;QACpCC,gBAAgB,EAAExB,WAAW,CAACwB,gBAAgB;QAC9CC,KAAK,EAAEC,UAAU,CAAC1B,WAAW,CAACyB,KAAK,CAAC;QACpCE,aAAa,EAAE3B,WAAW,CAAC2B,aAAa,GAAGD,UAAU,CAAC1B,WAAW,CAAC2B,aAAa,CAAC,GAAG,IAAI;QACvFC,QAAQ,EAAE5B,WAAW,CAAC4B,QAAQ;QAC9BC,QAAQ,EAAE7B,WAAW,CAAC6B,QAAQ;QAC9BC,WAAW,EAAE9B,WAAW,CAAC8B,WAAW;QACpCC,IAAI,EAAE/B,WAAW,CAAC+B,IAAI;QACtBC,UAAU,EAAEhC,WAAW,CAAC+B,IAAI,KAAK,UAAU,GAAGE,QAAQ,CAACjC,WAAW,CAACgC,UAAU,CAAC,GAAG,IAAI;QACrFE,GAAG,EAAElC,WAAW,CAACkC,GAAG;QACpBC,IAAI,EAAEnC,WAAW,CAACmC,IAAI;QACtBC,QAAQ,EAAEpC,WAAW,CAACoC,QAAQ;QAC9BC,QAAQ,EAAErC,WAAW,CAACqC,QAAQ;QAC9BC,UAAU,EAAEtC,WAAW,CAACsC,UAAU;QAClCC,cAAc,EAAEvC,WAAW,CAACuC,cAAc;QAC1C1B,MAAM,EAAED,eAAe;QACvBG,KAAK,GAAAd,iBAAA,GAAEW,eAAe,CAAC,CAAC,CAAC,cAAAX,iBAAA,uBAAlBA,iBAAA,CAAoBiB,GAAG;QAAE;QAChCsB,OAAO,EAAExC,WAAW,CAAC+B,IAAI,KAAK,SAAS,GAAG,IAAI,GAAGE,QAAQ,CAACjC,WAAW,CAACgC,UAAU,CAAC,GAAG,CAAC;QACrFS,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,IAAItC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC;MACpC,CAAC;;MAED;MACAhE,WAAW,CAACkE,YAAY,IAAI,CAACzB,UAAU,EAAE,GAAGyB,YAAY,CAAC,CAAC;MAE1D3D,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE3B;MAAW,CAAC;IAC/C,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,KAAK;IAClDjE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDtB,WAAW,CAACkE,YAAY,IACtBA,YAAY,CAACjC,GAAG,CAACmC,OAAO,IACtBA,OAAO,CAAChC,EAAE,KAAKmC,SAAS,GACpB;QAAE,GAAGH,OAAO;QAAE,GAAGI,OAAO;QAAEP,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC;MAAE,CAAC,GAC/DI,OACN,CACF,CAAC;MAED7D,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMI,aAAa,GAAG,MAAOF,SAAS,IAAK;IACzChE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDtB,WAAW,CAACkE,YAAY,IACtBA,YAAY,CAACQ,MAAM,CAACN,OAAO,IAAIA,OAAO,CAAChC,EAAE,KAAKmC,SAAS,CACzD,CAAC;MAEDhE,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMM,WAAW,GAAG,MAAOC,YAAY,IAAK;IAC1CrE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,MAAME,KAAK,GAAG,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAEjF,MAAM+C,WAAW,GAAG;QAClBzC,EAAE,EAAEZ,KAAK;QACTe,IAAI,EAAEqC,YAAY,CAACrC,IAAI;QACvBG,WAAW,EAAEkC,YAAY,CAAClC,WAAW;QACrCoC,IAAI,EAAEF,YAAY,CAACE,IAAI;QACvBC,aAAa,EAAEH,YAAY,CAACG,aAAa,IAAI,EAAE;QAC/ChB,SAAS,EAAE,IAAItC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC;MACpC,CAAC;MAED/D,aAAa,CAAC+E,cAAc,IAAI,CAAC,GAAGA,cAAc,EAAEH,WAAW,CAAC,CAAC;MAEjEtE,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE,IAAI;QAAEnB,QAAQ,EAAE6B;MAAY,CAAC;IACjD,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMY,cAAc,GAAG,MAAAA,CAAOC,UAAU,EAAEV,OAAO,KAAK;IACpDjE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDrB,aAAa,CAAC+E,cAAc,IAC1BA,cAAc,CAAC/C,GAAG,CAACe,QAAQ,IACzBA,QAAQ,CAACZ,EAAE,KAAK8C,UAAU,GACtB;QAAE,GAAGlC,QAAQ;QAAE,GAAGwB,OAAO;QAAEP,SAAS,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC;MAAE,CAAC,GAChEhB,QACN,CACF,CAAC;MAEDzC,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMc,cAAc,GAAG,MAAOD,UAAU,IAAK;IAC3C3E,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,MAAM8D,qBAAqB,GAAGtG,QAAQ,CAAC4F,MAAM,CAACN,OAAO,IAAIA,OAAO,CAACpB,QAAQ,KAAKkC,UAAU,CAAC;MACzF,IAAIE,qBAAqB,CAACC,MAAM,GAAG,CAAC,EAAE;QACpC,MAAM,IAAIzF,KAAK,CAAC,2BAA2BwF,qBAAqB,CAACC,MAAM,oCAAoC,CAAC;MAC9G;MAEApF,aAAa,CAAC+E,cAAc,IAC1BA,cAAc,CAACN,MAAM,CAAC1B,QAAQ,IAAIA,QAAQ,CAACZ,EAAE,KAAK8C,UAAU,CAC9D,CAAC;MAED3E,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE4D,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAErD,KAAK,EAAEA,KAAK,CAACuD;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMiB,cAAc,GAAIf,SAAS,IAAK;IACpC,OAAOzF,QAAQ,CAACyG,IAAI,CAACnB,OAAO,IAAIA,OAAO,CAAChC,EAAE,KAAKmC,SAAS,CAAC;EAC3D,CAAC;EAED,MAAMiB,eAAe,GAAIN,UAAU,IAAK;IACtC,OAAOlG,UAAU,CAACuG,IAAI,CAACvC,QAAQ,IAAIA,QAAQ,CAACZ,EAAE,KAAK8C,UAAU,CAAC;EAChE,CAAC;EAED,MAAMO,qBAAqB,GAAIP,UAAU,IAAK;IAC5C,OAAOpG,QAAQ,CAAC4F,MAAM,CAACN,OAAO,IAAIA,OAAO,CAACpB,QAAQ,KAAKkC,UAAU,CAAC;EACpE,CAAC;EAED,MAAMQ,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMC,cAAc,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IAC1C,OAAO/G,QAAQ,CAAC4F,MAAM,CAACN,OAAO,IAC5BA,OAAO,CAAC7B,IAAI,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,IACnDxB,OAAO,CAAC1B,WAAW,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,IACzDxB,OAAO,CAACd,IAAI,IAAIc,OAAO,CAACd,IAAI,CAACyC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,CAAE,IACrFxB,OAAO,CAACb,QAAQ,IAAIa,OAAO,CAACb,QAAQ,CAACsC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAC7E,CAAC;EACH,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAGA,CAAChD,WAAW,GAAG,IAAI,EAAEiD,aAAa,GAAG,IAAI,KAAK;IACxE,OAAOpH,QAAQ,CAAC4F,MAAM,CAACN,OAAO,IAAI;MAChC,MAAM+B,UAAU,GAAG/B,OAAO,CAACpB,QAAQ,KAAK,WAAW;MACnD,MAAMoD,kBAAkB,GAAG,CAACnD,WAAW,IAAImB,OAAO,CAACnB,WAAW,KAAKA,WAAW;MAC9E,MAAMoD,oBAAoB,GAAG,CAACH,aAAa,IAAI9B,OAAO,CAAC8B,aAAa,KAAKA,aAAa;MAEtF,OAAOC,UAAU,IAAIC,kBAAkB,IAAIC,oBAAoB;IACjE,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,UAAU,IAAK;IACzC;IACA,MAAMC,GAAG,GAAGD,UAAU,CAAChB,IAAI,CAACkB,CAAC,IAAIA,CAAC,CAACP,aAAa,KAAK,KAAK,CAAC;IAC3D,MAAMQ,WAAW,GAAGH,UAAU,CAAChB,IAAI,CAACkB,CAAC,IAAIA,CAAC,CAACP,aAAa,KAAK,aAAa,CAAC;IAE3E,MAAMS,MAAM,GAAG,EAAE;IAEjB,IAAIH,GAAG,IAAIE,WAAW,EAAE;MAAA,IAAAE,kBAAA,EAAAC,qBAAA;MACtB,MAAMC,SAAS,IAAAF,kBAAA,GAAGJ,GAAG,CAACO,aAAa,cAAAH,kBAAA,uBAAjBA,kBAAA,CAAmBI,MAAM;MAC3C,MAAMC,QAAQ,IAAAJ,qBAAA,GAAGH,WAAW,CAACK,aAAa,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2BG,MAAM;MAClD,IAAIF,SAAS,IAAIG,QAAQ,IAAIH,SAAS,KAAKG,QAAQ,EAAE;QACnDN,MAAM,CAACO,IAAI,CAAC,cAAcJ,SAAS,8CAA8CG,QAAQ,EAAE,CAAC;MAC9F;IACF;IAEA,OAAO;MACLE,UAAU,EAAER,MAAM,CAACtB,MAAM,KAAK,CAAC;MAC/BsB;IACF,CAAC;EACH,CAAC;EAED,MAAMS,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,MAAMC,cAAc,GAAGxI,QAAQ,CAAC4F,MAAM,CAAC6C,CAAC,IAAIF,UAAU,CAACvB,QAAQ,CAACyB,CAAC,CAACnF,EAAE,CAAC,CAAC;IACtE,MAAMoF,UAAU,GAAGF,cAAc,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEtD,OAAO,KAAKsD,GAAG,GAAGtD,OAAO,CAACxB,KAAK,EAAE,CAAC,CAAC;IAClF,MAAM+E,cAAc,GAAGH,UAAU,GAAG,IAAI,CAAC,CAAC;;IAE1C,OAAO;MACLI,aAAa,EAAEJ,UAAU;MACzBK,WAAW,EAAEL,UAAU,GAAGG,cAAc;MACxCG,OAAO,EAAEH,cAAc;MACvB7I,QAAQ,EAAEwI;IACZ,CAAC;EACH,CAAC;EAED,MAAMS,wBAAwB,GAAIC,aAAa,IAAK;IAClD;IACA,MAAMC,UAAU,GAAG;MACjBC,MAAM,EAAEF,aAAa,CAACpF,KAAK,GAAG,GAAG;MACjCuF,QAAQ,EAAEH,aAAa,CAACpF,KAAK,IAAI,GAAG,IAAIoF,aAAa,CAACpF,KAAK,GAAG,GAAG;MACjEwF,OAAO,EAAEJ,aAAa,CAACpF,KAAK,IAAI;IAClC,CAAC;IAED,OAAO9D,QAAQ,CAAC4F,MAAM,CAACN,OAAO,IAAI;MAChC,IAAIA,OAAO,CAACpB,QAAQ,KAAK,WAAW,IAAIoB,OAAO,CAACnB,WAAW,KAAK,cAAc,EAAE;QAC9E,OAAO,KAAK;MACd;;MAEA;MACA,IAAImB,OAAO,CAAC8B,aAAa,KAAK8B,aAAa,CAAC9B,aAAa,EAAE;QACzD,OAAO,KAAK;MACd;;MAEA;MACA,IAAI+B,UAAU,CAACC,MAAM,IAAI9D,OAAO,CAACxB,KAAK,GAAG,GAAG,EAAE,OAAO,KAAK;MAC1D,IAAIqF,UAAU,CAACE,QAAQ,KAAK/D,OAAO,CAACxB,KAAK,GAAG,GAAG,IAAIwB,OAAO,CAACxB,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK;MACrF,IAAIqF,UAAU,CAACG,OAAO,IAAIhE,OAAO,CAACxB,KAAK,GAAG,GAAG,EAAE,OAAO,KAAK;MAE3D,OAAO,IAAI;IACb,CAAC,CAAC,CAACyF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMC,KAAK,GAAG;IACZxJ,QAAQ;IACRE,UAAU;IACVkB,YAAY;IACZE,OAAO;IACPE,SAAS;IACTY,UAAU;IACVoD,aAAa;IACbG,aAAa;IACbE,WAAW;IACXM,cAAc;IACdE,cAAc;IACdG,cAAc;IACdE,eAAe;IACfC,qBAAqB;IACrBC,cAAc;IACd;IACAO,mBAAmB;IACnBK,kBAAkB;IAClBc,oBAAoB;IACpBW,wBAAwB;IACxB1I;EACF,CAAC;EAED,oBACEE,OAAA,CAACC,cAAc,CAAC+I,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAxI,QAAA,EACnCA;EAAQ;IAAA0I,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAAC5I,GAAA,CAzVWF,eAAe;AAAA+I,EAAA,GAAf/I,eAAe;AA2V5B,eAAeL,cAAc;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}