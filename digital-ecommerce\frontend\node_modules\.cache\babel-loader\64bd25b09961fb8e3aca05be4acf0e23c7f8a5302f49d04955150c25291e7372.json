{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport { ShoppingBagIcon, StarIcon, TruckIcon, ShieldCheckIcon, HeartIcon, SparklesIcon, ArrowDownTrayIcon as CloudDownloadIcon, ComputerDesktopIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport ProductPreviewModal from '../components/ProductPreviewModal';\nimport toast, { Toaster } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [heroRef, heroInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [featuresRef, featuresInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [productsRef, productsInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [previewProduct, setPreviewProduct] = useState(null);\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const {\n    addToCart\n  } = useCart();\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n  const handleAddToCart = product => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right'\n    });\n  };\n  const handleProductPreview = product => {\n    setPreviewProduct(product);\n    setIsPreviewOpen(true);\n  };\n  const closePreview = () => {\n    setIsPreviewOpen(false);\n    setPreviewProduct(null);\n  };\n  const testimonials = [{\n    id: 1,\n    name: 'Sarah Johnson',\n    role: 'Verified Customer',\n    content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n  }, {\n    id: 2,\n    name: 'Mike Chen',\n    role: 'Tech Enthusiast',\n    content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n  }, {\n    id: 3,\n    name: 'Emily Davis',\n    role: 'Regular Shopper',\n    content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n  }];\n  const features = [{\n    icon: TruckIcon,\n    title: 'Free Shipping',\n    description: 'Free delivery on orders over $50'\n  }, {\n    icon: ShieldCheckIcon,\n    title: 'Secure Payment',\n    description: '100% secure payment processing'\n  }, {\n    icon: HeartIcon,\n    title: '24/7 Support',\n    description: 'Round-the-clock customer service'\n  }, {\n    icon: SparklesIcon,\n    title: 'Premium Quality',\n    description: 'Only the finest products'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: heroRef,\n      initial: {\n        opacity: 0\n      },\n      animate: heroInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 1\n      },\n      className: \"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: -100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Discover Amazing\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-light-orange-100 mb-8 leading-relaxed\",\n              children: \"Shop the latest trends with unbeatable prices and premium quality. Your perfect shopping experience starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"Shop Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/digital-products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\",\n                  children: \"Digital Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: 100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\",\n                alt: \"Shopping Experience\",\n                className: \"rounded-2xl shadow-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: featuresRef,\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: featuresInView ? {\n        opacity: 1,\n        y: 0\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Us?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"We're committed to providing you with the best shopping experience possible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: featuresInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-10 h-10 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: productsRef,\n      initial: {\n        opacity: 0\n      },\n      animate: productsInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-gradient-to-br from-light-orange-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover our handpicked selection of premium products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: featuredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: productsInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images[0],\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: product.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  className: \"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-5 h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-light-orange-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-semibold\",\n                  children: [\"Save $\", (product.originalPrice - product.price).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  onClick: () => handleAddToCart(product),\n                  className: \"flex-1 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Add to Cart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleProductPreview(product),\n                  className: \"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                  title: \"Quick Preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-gray-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      initial: {\n        opacity: 0\n      },\n      whileInView: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.8\n      },\n      viewport: {\n        once: true\n      },\n      className: \"py-20 bg-gradient-to-br from-blue-50 to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Digital Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Instant access to software, games, and digital content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: digitalProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images[0],\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                    className: \"w-3 h-3 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this), \"Instant\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-blue-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  onClick: () => handleAddToCart(product),\n                  className: \"flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Get Instantly\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleProductPreview(product),\n                  className: \"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                  title: \"Quick Preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-gray-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/digital-products\",\n            children: /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n              children: [/*#__PURE__*/_jsxDEV(ComputerDesktopIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View All Digital Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Customers Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Don't just take our word for it - hear from our satisfied customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"p-8 rounded-2xl shadow-lg bg-gradient-to-br from-light-orange-50 to-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: testimonial.avatar,\n                alt: testimonial.name,\n                className: \"w-12 h-12 rounded-full mr-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: testimonial.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex mb-4\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-5 h-5 text-yellow-400\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: [\"\\\"\", testimonial.content, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this)]\n          }, testimonial.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductPreviewModal, {\n      product: previewProduct,\n      isOpen: isPreviewOpen,\n      onClose: closePreview\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"IsfKxRrPF3iFf+HXEITmZPAvVf0=\", false, function () {\n  return [useInView, useInView, useInView, useCart];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "useInView", "Link", "ShoppingBagIcon", "StarIcon", "TruckIcon", "ShieldCheckIcon", "HeartIcon", "SparklesIcon", "ArrowDownTrayIcon", "CloudDownloadIcon", "ComputerDesktopIcon", "StarIconSolid", "getFeaturedProducts", "getDigitalProducts", "useCart", "ProductPreviewModal", "toast", "Toaster", "jsxDEV", "_jsxDEV", "HomePage", "_s", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "featuresRef", "featuresInView", "productsRef", "productsInView", "previewProduct", "setPreviewProduct", "isPreviewOpen", "setIsPreviewOpen", "addToCart", "featuredProducts", "slice", "digitalProducts", "handleAddToCart", "product", "success", "name", "duration", "position", "handleProductPreview", "closePreview", "testimonials", "id", "role", "content", "rating", "avatar", "features", "icon", "title", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "section", "ref", "initial", "opacity", "animate", "transition", "div", "x", "delay", "to", "button", "whileHover", "scale", "whileTap", "src", "alt", "y", "map", "feature", "index", "images", "badge", "Array", "_", "i", "Math", "floor", "reviews", "price", "originalPrice", "toFixed", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "whileInView", "viewport", "once", "testimonial", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport {\n  ShoppingBagIcon,\n  StarIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  SparklesIcon,\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport ProductPreviewModal from '../components/ProductPreviewModal';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst HomePage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [productsRef, productsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [previewProduct, setPreviewProduct] = useState(null);\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const { addToCart } = useCart();\n\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n\n  const handleProductPreview = (product) => {\n    setPreviewProduct(product);\n    setIsPreviewOpen(true);\n  };\n\n  const closePreview = () => {\n    setIsPreviewOpen(false);\n    setPreviewProduct(null);\n  };\n\n  const testimonials = [\n    {\n      id: 1,\n      name: 'Sarah Johnson',\n      role: 'Verified Customer',\n      content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n    },\n    {\n      id: 2,\n      name: 'Mike Chen',\n      role: 'Tech Enthusiast',\n      content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n    },\n    {\n      id: 3,\n      name: 'Emily Davis',\n      role: 'Regular Shopper',\n      content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n    }\n  ];\n\n  const features = [\n    {\n      icon: TruckIcon,\n      title: 'Free Shipping',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Secure Payment',\n      description: '100% secure payment processing'\n    },\n    {\n      icon: HeartIcon,\n      title: '24/7 Support',\n      description: 'Round-the-clock customer service'\n    },\n    {\n      icon: SparklesIcon,\n      title: 'Premium Quality',\n      description: 'Only the finest products'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      <Toaster position=\"top-right\" />\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                Discover Amazing\n                <span className=\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n                  Products\n                </span>\n              </h1>\n              <p className=\"text-xl text-light-orange-100 mb-8 leading-relaxed\">\n                Shop the latest trends with unbeatable prices and premium quality. \n                Your perfect shopping experience starts here.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link to=\"/products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                  >\n                    Shop Now\n                  </motion.button>\n                </Link>\n                <Link to=\"/digital-products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\"\n                  >\n                    Digital Products\n                  </motion.button>\n                </Link>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\"\n                  alt=\"Shopping Experience\"\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section\n        ref={featuresRef}\n        initial={{ opacity: 0, y: 50 }}\n        animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We're committed to providing you with the best shopping experience possible\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <feature.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Products Section */}\n      <motion.section\n        ref={productsRef}\n        initial={{ opacity: 0 }}\n        animate={productsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of premium products\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                animate={productsInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className=\"rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 bg-white\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images[0]}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      {product.badge}\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n                    >\n                      <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-light-orange-600\">\n                        ${product.price}\n                      </span>\n                      <span className=\"text-lg text-gray-500 line-through\">\n                        ${product.originalPrice}\n                      </span>\n                    </div>\n                    <span className=\"text-sm text-green-600 font-semibold\">\n                      Save ${(product.originalPrice - product.price).toFixed(2)}\n                    </span>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <motion.button\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                      onClick={() => handleAddToCart(product)}\n                      className=\"flex-1 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                    >\n                      <ShoppingBagIcon className=\"w-5 h-5\" />\n                      <span>Add to Cart</span>\n                    </motion.button>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleProductPreview(product)}\n                      className=\"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n                      title=\"Quick Preview\"\n                    >\n                      <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                      </svg>\n                    </motion.button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Digital Products Section */}\n      <motion.section\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Digital Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Instant access to software, games, and digital content\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {digitalProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10 }}\n                className=\"rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 bg-white\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images[0]}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      Digital\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n                      <CloudDownloadIcon className=\"w-3 h-3 mr-1\" />\n                      Instant\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-blue-600\">\n                        ${product.price}\n                      </span>\n                      {product.originalPrice && product.originalPrice > product.price && (\n                        <span className=\"text-lg text-gray-500 line-through\">\n                          ${product.originalPrice}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <motion.button\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                      onClick={() => handleAddToCart(product)}\n                      className=\"flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                    >\n                      <CloudDownloadIcon className=\"w-5 h-5\" />\n                      <span>Get Instantly</span>\n                    </motion.button>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleProductPreview(product)}\n                      className=\"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n                      title=\"Quick Preview\"\n                    >\n                      <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                      </svg>\n                    </motion.button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link to=\"/digital-products\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\"\n              >\n                <ComputerDesktopIcon className=\"w-6 h-6\" />\n                <span>View All Digital Products</span>\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Customers Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it - hear from our satisfied customers\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"p-8 rounded-2xl shadow-lg bg-gradient-to-br from-light-orange-50 to-white\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <img\n                    src={testimonial.avatar}\n                    alt={testimonial.name}\n                    className=\"w-12 h-12 rounded-full mr-4\"\n                  />\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <StarIconSolid key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-700 italic\">\"{testimonial.content}\"</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Product Preview Modal */}\n      <ProductPreviewModal\n        product={previewProduct}\n        isOpen={isPreviewOpen}\n        onClose={closePreview}\n      />\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,eAAe,EACfC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,YAAY,EACZC,iBAAiB,IAAIC,iBAAiB,EACtCC,mBAAmB,QACd,6BAA6B;AACpC,SAASP,QAAQ,IAAIQ,aAAa,QAAQ,2BAA2B;AACrE,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC1E,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG7B,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM;IAAEoC;EAAU,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAE/B,MAAMqB,gBAAgB,GAAGvB,mBAAmB,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGxB,kBAAkB,CAAC,CAAC,CAACuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExD,MAAME,eAAe,GAAIC,OAAO,IAAK;IACnCL,SAAS,CAACK,OAAO,CAAC;IAClBvB,KAAK,CAACwB,OAAO,CAAC,GAAGD,OAAO,CAACE,IAAI,iBAAiB,EAAE;MAC9CC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,oBAAoB,GAAIL,OAAO,IAAK;IACxCR,iBAAiB,CAACQ,OAAO,CAAC;IAC1BN,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBZ,gBAAgB,CAAC,KAAK,CAAC;IACvBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMe,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,eAAe;IACrBO,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,4FAA4F;IACrGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,0FAA0F;IACnGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLN,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,gGAAgG;IACzGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEjD,SAAS;IACfkD,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhD,eAAe;IACrBiD,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/C,SAAS;IACfgD,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE9C,YAAY;IAClB+C,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEpC,OAAA;IAAKqC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BtC,OAAA,CAACF,OAAO;MAAC0B,QAAQ,EAAC;IAAW;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhC1C,OAAA,CAACpB,MAAM,CAAC+D,OAAO;MACbC,GAAG,EAAEzC,OAAQ;MACb0C,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE3C,UAAU,GAAG;QAAE0C,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEzB,QAAQ,EAAE;MAAE,CAAE;MAC5Bc,SAAS,EAAC,2GAA2G;MAAAC,QAAA,gBAGrHtC,OAAA;QAAKqC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtC,OAAA;UAAKqC,SAAS,EAAC;QAAqF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3G1C,OAAA;UAAKqC,SAAS,EAAC;QAAuF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7G1C,OAAA;UAAKqC,SAAS,EAAC;QAAwF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EtC,OAAA;UAAKqC,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClEtC,OAAA,CAACpB,MAAM,CAACqE,GAAG;YACTJ,OAAO,EAAE;cAAEK,CAAC,EAAE,CAAC,GAAG;cAAEJ,OAAO,EAAE;YAAE,CAAE;YACjCC,OAAO,EAAE3C,UAAU,GAAG;cAAE8C,CAAC,EAAE,CAAC;cAAEJ,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAE1CtC,OAAA;cAAIqC,SAAS,EAAC,8DAA8D;cAAAC,QAAA,GAAC,kBAE3E,eAAAtC,OAAA;gBAAMqC,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAErG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL1C,OAAA;cAAGqC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAGlE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1C,OAAA;cAAKqC,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CtC,OAAA,CAAClB,IAAI;gBAACsE,EAAE,EAAC,WAAW;gBAAAd,QAAA,eAClBtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BlB,SAAS,EAAC,mIAAmI;kBAAAC,QAAA,EAC9I;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACP1C,OAAA,CAAClB,IAAI;gBAACsE,EAAE,EAAC,mBAAmB;gBAAAd,QAAA,eAC1BtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BlB,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EACjK;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEb1C,OAAA,CAACpB,MAAM,CAACqE,GAAG;YACTJ,OAAO,EAAE;cAAEK,CAAC,EAAE,GAAG;cAAEJ,OAAO,EAAE;YAAE,CAAE;YAChCC,OAAO,EAAE3C,UAAU,GAAG;cAAE8C,CAAC,EAAE,CAAC;cAAEJ,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpBtC,OAAA;cAAKqC,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BtC,OAAA;gBACEyD,GAAG,EAAC,oEAAoE;gBACxEC,GAAG,EAAC,qBAAqB;gBACzBrB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC;YAA+G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjB1C,OAAA,CAACpB,MAAM,CAAC+D,OAAO;MACbC,GAAG,EAAErC,WAAY;MACjBsC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEa,CAAC,EAAE;MAAG,CAAE;MAC/BZ,OAAO,EAAEvC,cAAc,GAAG;QAAEsC,OAAO,EAAE,CAAC;QAAEa,CAAC,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MACpDX,UAAU,EAAE;QAAEzB,QAAQ,EAAE;MAAI,CAAE;MAC9Bc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAE1BtC,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtC,OAAA;YAAIqC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAGqC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEL,QAAQ,CAAC2B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B9D,OAAA,CAACpB,MAAM,CAACqE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BZ,OAAO,EAAEvC,cAAc,GAAG;cAAEsC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDX,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BtC,OAAA;cAAKqC,SAAS,EAAC,0LAA0L;cAAAC,QAAA,eACvMtC,OAAA,CAAC6D,OAAO,CAAC3B,IAAI;gBAACG,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACN1C,OAAA;cAAIqC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEuB,OAAO,CAAC1B;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7E1C,OAAA;cAAGqC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEuB,OAAO,CAACzB;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAVjDoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjB1C,OAAA,CAACpB,MAAM,CAAC+D,OAAO;MACbC,GAAG,EAAEnC,WAAY;MACjBoC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAErC,cAAc,GAAG;QAAEoC,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC9CE,UAAU,EAAE;QAAEzB,QAAQ,EAAE;MAAI,CAAE;MAC9Bc,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eAEjEtC,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtC,OAAA;YAAIqC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAGqC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEtB,gBAAgB,CAAC4C,GAAG,CAAC,CAACxC,OAAO,EAAE0C,KAAK,kBACnC9D,OAAA,CAACpB,MAAM,CAACqE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BZ,OAAO,EAAErC,cAAc,GAAG;cAAEoC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDX,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDR,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBtB,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBAE9GtC,OAAA;cAAKqC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBtC,OAAA;gBACEyD,GAAG,EAAErC,OAAO,CAAC2C,MAAM,CAAC,CAAC,CAAE;gBACvBL,GAAG,EAAEtC,OAAO,CAACE,IAAK;gBAClBe,SAAS,EAAC;cAAkF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACF1C,OAAA;gBAAKqC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCtC,OAAA;kBAAMqC,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAC1FlB,OAAO,CAAC4C;gBAAK;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAI,CAAE;kBACzBlB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAE7DtC,OAAA,CAACb,SAAS;oBAACkD,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAKqC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBtC,OAAA;gBAAIqC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAElB,OAAO,CAACE;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5E1C,OAAA;gBAAKqC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtC,OAAA;kBAAKqC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG2B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACjD,OAAO,CAACW,MAAM,CAAC,gBAC5B/B,OAAA,CAACR,aAAa;oBAAS6C,SAAS,EAAC;kBAAyB,GAAtC8B,CAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7D1C,OAAA,CAAChB,QAAQ;oBAASqD,SAAS,EAAC;kBAAuB,GAApC8B,CAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1C,OAAA;kBAAMqC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzClB,OAAO,CAACW,MAAM,EAAC,IAAE,EAACX,OAAO,CAACkD,OAAO,EAAC,WACrC;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN1C,OAAA;gBAAKqC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDtC,OAAA;kBAAKqC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CtC,OAAA;oBAAMqC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,GACxD,EAAClB,OAAO,CAACmD,KAAK;kBAAA;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACP1C,OAAA;oBAAMqC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAClB,OAAO,CAACoD,aAAa;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN1C,OAAA;kBAAMqC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GAAC,QAC/C,EAAC,CAAClB,OAAO,CAACoD,aAAa,GAAGpD,OAAO,CAACmD,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN1C,OAAA;gBAAKqC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BmB,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAACC,OAAO,CAAE;kBACxCiB,SAAS,EAAC,yOAAyO;kBAAAC,QAAA,gBAEnPtC,OAAA,CAACjB,eAAe;oBAACsD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC1C,OAAA;oBAAAsC,QAAA,EAAM;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eAChB1C,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BmB,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAACL,OAAO,CAAE;kBAC7CiB,SAAS,EAAC,0EAA0E;kBACpFF,KAAK,EAAC,eAAe;kBAAAG,QAAA,eAErBtC,OAAA;oBAAKqC,SAAS,EAAC,uBAAuB;oBAACsC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAvC,QAAA,gBAC1FtC,OAAA;sBAAM8E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1G1C,OAAA;sBAAM8E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAyH;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GApFDtB,OAAO,CAACQ,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqFL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjB1C,OAAA,CAACpB,MAAM,CAAC+D,OAAO;MACbE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBoC,WAAW,EAAE;QAAEpC,OAAO,EAAE;MAAE,CAAE;MAC5BE,UAAU,EAAE;QAAEzB,QAAQ,EAAE;MAAI,CAAE;MAC9B4D,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAE;MACzB/C,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAE7DtC,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtC,OAAA;YAAIqC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAGqC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDpB,eAAe,CAAC0C,GAAG,CAAC,CAACxC,OAAO,EAAE0C,KAAK,kBAClC9D,OAAA,CAACpB,MAAM,CAACqE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEpC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDqB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBtB,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBAE9GtC,OAAA;cAAKqC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBtC,OAAA;gBACEyD,GAAG,EAAErC,OAAO,CAAC2C,MAAM,CAAC,CAAC,CAAE;gBACvBL,GAAG,EAAEtC,OAAO,CAACE,IAAK;gBAClBe,SAAS,EAAC;cAAkF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACF1C,OAAA;gBAAKqC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCtC,OAAA;kBAAMqC,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAEtF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCtC,OAAA;kBAAMqC,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,gBACjGtC,OAAA,CAACV,iBAAiB;oBAAC+C,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAKqC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBtC,OAAA;gBAAIqC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAElB,OAAO,CAACE;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5E1C,OAAA;gBAAKqC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtC,OAAA;kBAAKqC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG2B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACjD,OAAO,CAACW,MAAM,CAAC,gBAC5B/B,OAAA,CAACR,aAAa;oBAAS6C,SAAS,EAAC;kBAAyB,GAAtC8B,CAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7D1C,OAAA,CAAChB,QAAQ;oBAASqD,SAAS,EAAC;kBAAuB,GAApC8B,CAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1C,OAAA;kBAAMqC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzClB,OAAO,CAACW,MAAM,EAAC,IAAE,EAACX,OAAO,CAACkD,OAAO,EAAC,WACrC;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN1C,OAAA;gBAAKqC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDtC,OAAA;kBAAKqC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CtC,OAAA;oBAAMqC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,GAAC,GAChD,EAAClB,OAAO,CAACmD,KAAK;kBAAA;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,EACNtB,OAAO,CAACoD,aAAa,IAAIpD,OAAO,CAACoD,aAAa,GAAGpD,OAAO,CAACmD,KAAK,iBAC7DvE,OAAA;oBAAMqC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAClB,OAAO,CAACoD,aAAa;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1C,OAAA;gBAAKqC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BmB,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAACC,OAAO,CAAE;kBACxCiB,SAAS,EAAC,yMAAyM;kBAAAC,QAAA,gBAEnNtC,OAAA,CAACV,iBAAiB;oBAAC+C,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzC1C,OAAA;oBAAAsC,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eAChB1C,OAAA,CAACpB,MAAM,CAACyE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BmB,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAACL,OAAO,CAAE;kBAC7CiB,SAAS,EAAC,0EAA0E;kBACpFF,KAAK,EAAC,eAAe;kBAAAG,QAAA,eAErBtC,OAAA;oBAAKqC,SAAS,EAAC,uBAAuB;oBAACsC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAvC,QAAA,gBAC1FtC,OAAA;sBAAM8E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1G1C,OAAA;sBAAM8E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAyH;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAjFDtB,OAAO,CAACQ,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkFL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCtC,OAAA,CAAClB,IAAI;YAACsE,EAAE,EAAC,mBAAmB;YAAAd,QAAA,eAC1BtC,OAAA,CAACpB,MAAM,CAACyE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BlB,SAAS,EAAC,gMAAgM;cAAAC,QAAA,gBAE1MtC,OAAA,CAACT,mBAAmB;gBAAC8C,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3C1C,OAAA;gBAAAsC,QAAA,EAAM;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjB1C,OAAA;MAASqC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCtC,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDtC,OAAA;UAAKqC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtC,OAAA;YAAIqC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAGqC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDX,YAAY,CAACiC,GAAG,CAAC,CAACyB,WAAW,EAAEvB,KAAK,kBACnC9D,OAAA,CAACpB,MAAM,CAACqE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEpC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEzB,QAAQ,EAAE,GAAG;cAAE4B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBAErFtC,OAAA;cAAKqC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCtC,OAAA;gBACEyD,GAAG,EAAE4B,WAAW,CAACrD,MAAO;gBACxB0B,GAAG,EAAE2B,WAAW,CAAC/D,IAAK;gBACtBe,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACF1C,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAIqC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAE+C,WAAW,CAAC/D;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnE1C,OAAA;kBAAGqC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE+C,WAAW,CAACxD;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,GAAG2B,KAAK,CAACoB,WAAW,CAACtD,MAAM,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,kBACvCnE,OAAA,CAACR,aAAa;gBAAS6C,SAAS,EAAC;cAAyB,GAAtC8B,CAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuC,CAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAGqC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,IAAC,EAAC+C,WAAW,CAACvD,OAAO,EAAC,IAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAxB1D2C,WAAW,CAACzD,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV1C,OAAA,CAACJ,mBAAmB;MAClBwB,OAAO,EAAET,cAAe;MACxB2E,MAAM,EAAEzE,aAAc;MACtB0E,OAAO,EAAE7D;IAAa;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxC,EAAA,CAjeID,QAAQ;EAAA,QACkBpB,SAAS,EACDA,SAAS,EACTA,SAAS,EAGzBc,OAAO;AAAA;AAAA6F,EAAA,GANzBvF,QAAQ;AAmed,eAAeA,QAAQ;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}