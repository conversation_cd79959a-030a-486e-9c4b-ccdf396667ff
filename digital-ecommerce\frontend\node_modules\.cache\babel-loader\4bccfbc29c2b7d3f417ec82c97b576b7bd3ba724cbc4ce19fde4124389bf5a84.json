{"ast": null, "code": "import { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\nfunction isObjectKey(key, object) {\n  return key in object;\n}\nclass ObjectVisualElement extends VisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"object\";\n  }\n  readValueFromInstance(instance, key) {\n    if (isObjectKey(key, instance)) {\n      const value = instance[key];\n      if (typeof value === \"string\" || typeof value === \"number\") {\n        return value;\n      }\n    }\n    return undefined;\n  }\n  getBaseTargetFromProps() {\n    return undefined;\n  }\n  removeValueFromRenderState(key, renderState) {\n    delete renderState.output[key];\n  }\n  measureInstanceViewportBox() {\n    return createBox();\n  }\n  build(renderState, latestValues) {\n    Object.assign(renderState.output, latestValues);\n  }\n  renderInstance(instance, {\n    output\n  }) {\n    Object.assign(instance, output);\n  }\n  sortInstanceNodePosition() {\n    return 0;\n  }\n}\nexport { ObjectVisualElement };", "map": {"version": 3, "names": ["createBox", "VisualElement", "isObjectKey", "key", "object", "ObjectVisualElement", "constructor", "arguments", "type", "readValueFromInstance", "instance", "value", "undefined", "getBaseTargetFromProps", "removeValueFromRenderState", "renderState", "output", "measureInstanceViewportBox", "build", "latestValues", "Object", "assign", "renderInstance", "sortInstanceNodePosition"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs"], "sourcesContent": ["import { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\n\nfunction isObjectKey(key, object) {\n    return key in object;\n}\nclass ObjectVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"object\";\n    }\n    readValueFromInstance(instance, key) {\n        if (isObjectKey(key, instance)) {\n            const value = instance[key];\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    removeValueFromRenderState(key, renderState) {\n        delete renderState.output[key];\n    }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    build(renderState, latestValues) {\n        Object.assign(renderState.output, latestValues);\n    }\n    renderInstance(instance, { output }) {\n        Object.assign(instance, output);\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\n\nexport { ObjectVisualElement };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sCAAsC;AAChE,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,SAASC,WAAWA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC9B,OAAOD,GAAG,IAAIC,MAAM;AACxB;AACA,MAAMC,mBAAmB,SAASJ,aAAa,CAAC;EAC5CK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,QAAQ;EACxB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEP,GAAG,EAAE;IACjC,IAAID,WAAW,CAACC,GAAG,EAAEO,QAAQ,CAAC,EAAE;MAC5B,MAAMC,KAAK,GAAGD,QAAQ,CAACP,GAAG,CAAC;MAC3B,IAAI,OAAOQ,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACxD,OAAOA,KAAK;MAChB;IACJ;IACA,OAAOC,SAAS;EACpB;EACAC,sBAAsBA,CAAA,EAAG;IACrB,OAAOD,SAAS;EACpB;EACAE,0BAA0BA,CAACX,GAAG,EAAEY,WAAW,EAAE;IACzC,OAAOA,WAAW,CAACC,MAAM,CAACb,GAAG,CAAC;EAClC;EACAc,0BAA0BA,CAAA,EAAG;IACzB,OAAOjB,SAAS,CAAC,CAAC;EACtB;EACAkB,KAAKA,CAACH,WAAW,EAAEI,YAAY,EAAE;IAC7BC,MAAM,CAACC,MAAM,CAACN,WAAW,CAACC,MAAM,EAAEG,YAAY,CAAC;EACnD;EACAG,cAAcA,CAACZ,QAAQ,EAAE;IAAEM;EAAO,CAAC,EAAE;IACjCI,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAEM,MAAM,CAAC;EACnC;EACAO,wBAAwBA,CAAA,EAAG;IACvB,OAAO,CAAC;EACZ;AACJ;AAEA,SAASlB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}