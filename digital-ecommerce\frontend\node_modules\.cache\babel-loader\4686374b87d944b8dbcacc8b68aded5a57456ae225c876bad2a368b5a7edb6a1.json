{"ast": null, "code": "import { cubicBezierAsString } from './cubic-bezier.mjs';\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: /*@__PURE__*/cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: /*@__PURE__*/cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: /*@__PURE__*/cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: /*@__PURE__*/cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nexport { supportedWaapiEasing };", "map": {"version": 3, "names": ["cubicBezierAsString", "supportedWaapiEasing", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs"], "sourcesContent": ["import { cubicBezierAsString } from './cubic-bezier.mjs';\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\n\nexport { supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,oBAAoB;AAExD,MAAMC,oBAAoB,GAAG;EACzBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAE,aAAcP,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC7DQ,OAAO,EAAE,aAAcR,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAC9DS,MAAM,EAAE,aAAcT,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACpEU,OAAO,EAAE,aAAcV,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACvE,CAAC;AAED,SAASC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}