import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  QuestionMarkCircleIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  UserIcon,
  CheckBadgeIcon,
  PlusIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useToast } from '../contexts/ToastContext';

const ProductQA = ({ productId, productName }) => {
  const { showSuccess } = useToast();
  const [showAskQuestion, setShowAskQuestion] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  // Mock Q&A data
  const qaData = [
    {
      id: 1,
      question: 'Is this compatible with my AMD Ryzen 5 5600X processor?',
      answer: 'Yes, this motherboard is fully compatible with the AMD Ryzen 5 5600X. It supports all Ryzen 5000 series processors out of the box.',
      askedBy: 'TechBuilder2023',
      answeredBy: 'PCGaming Support',
      askDate: '2024-01-15',
      answerDate: '2024-01-15',
      helpful: 18,
      notHelpful: 2,
      verified: true
    },
    {
      id: 2,
      question: 'What is the maximum RAM capacity supported?',
      answer: 'This motherboard supports up to 128GB of DDR4 memory across 4 DIMM slots, with speeds up to DDR4-3200 (JEDEC) and higher with overclocking.',
      askedBy: 'GamerMike',
      answeredBy: 'PCGaming Support',
      askDate: '2024-01-12',
      answerDate: '2024-01-12',
      helpful: 24,
      notHelpful: 1,
      verified: true
    },
    {
      id: 3,
      question: 'Does it come with built-in WiFi?',
      answer: 'Yes, this model includes WiFi 6 (802.11ax) and Bluetooth 5.0 connectivity built-in.',
      askedBy: 'WirelessUser',
      answeredBy: 'PCGaming Support',
      askDate: '2024-01-10',
      answerDate: '2024-01-10',
      helpful: 15,
      notHelpful: 0,
      verified: true
    },
    {
      id: 4,
      question: 'Can I use this for cryptocurrency mining?',
      answer: 'While technically possible, this motherboard is optimized for gaming and general computing. For dedicated mining, consider our mining-specific motherboards.',
      askedBy: 'CryptoMiner',
      answeredBy: 'Community Expert',
      askDate: '2024-01-08',
      answerDate: '2024-01-09',
      helpful: 8,
      notHelpful: 3,
      verified: false
    }
  ];

  const [questions, setQuestions] = useState(qaData);

  const filteredQuestions = questions.filter(qa =>
    qa.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    qa.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedQuestions = [...filteredQuestions].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.askDate) - new Date(b.askDate);
      case 'helpful':
        return b.helpful - a.helpful;
      case 'newest':
      default:
        return new Date(b.askDate) - new Date(a.askDate);
    }
  });

  const handleAskQuestion = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const newQuestion = {
      id: questions.length + 1,
      question: formData.get('question'),
      answer: null,
      askedBy: 'You',
      answeredBy: null,
      askDate: new Date().toISOString().split('T')[0],
      answerDate: null,
      helpful: 0,
      notHelpful: 0,
      verified: false
    };

    setQuestions([newQuestion, ...questions]);
    setShowAskQuestion(false);
    showSuccess('Question Submitted', 'Your question has been submitted and will be answered soon!');
  };

  const handleHelpful = (questionId, helpful) => {
    setQuestions(questions.map(q => 
      q.id === questionId 
        ? { ...q, helpful: helpful ? q.helpful + 1 : q.helpful, notHelpful: helpful ? q.notHelpful : q.notHelpful + 1 }
        : q
    ));
    showSuccess('Feedback Recorded', 'Thank you for your feedback!');
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <QuestionMarkCircleIcon className="w-8 h-8 text-light-orange-500" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Questions & Answers</h2>
            <p className="text-sm text-gray-600">Get answers from the community and our experts</p>
          </div>
        </div>
        <button
          onClick={() => setShowAskQuestion(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors text-sm font-medium"
        >
          <PlusIcon className="w-4 h-4" />
          <span>Ask Question</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search questions and answers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
          />
        </div>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
        >
          <option value="newest">Newest First</option>
          <option value="oldest">Oldest First</option>
          <option value="helpful">Most Helpful</option>
        </select>
      </div>

      {/* Questions List */}
      <div className="space-y-6">
        {sortedQuestions.length === 0 ? (
          <div className="text-center py-8">
            <QuestionMarkCircleIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
            <p className="text-gray-600">Be the first to ask a question about this product!</p>
          </div>
        ) : (
          sortedQuestions.map((qa) => (
            <motion.div
              key={qa.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="border border-gray-200 rounded-lg p-6"
            >
              {/* Question */}
              <div className="mb-4">
                <div className="flex items-start space-x-3 mb-3">
                  <div className="w-8 h-8 bg-light-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <QuestionMarkCircleIcon className="w-5 h-5 text-light-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">{qa.question}</h3>
                    <div className="flex items-center space-x-3 text-sm text-gray-600">
                      <span>Asked by {qa.askedBy}</span>
                      <span>•</span>
                      <span>{new Date(qa.askDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Answer */}
              {qa.answer ? (
                <div className="ml-11 bg-gray-50 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <UserIcon className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-700 mb-3">{qa.answer}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <span>Answered by {qa.answeredBy}</span>
                            {qa.verified && (
                              <CheckBadgeIcon className="w-4 h-4 text-green-500" title="Verified Answer" />
                            )}
                          </div>
                          <span>•</span>
                          <span>{new Date(qa.answerDate).toLocaleDateString()}</span>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <button
                            onClick={() => handleHelpful(qa.id, true)}
                            className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900"
                          >
                            <HandThumbUpIcon className="w-4 h-4" />
                            <span>Helpful ({qa.helpful})</span>
                          </button>
                          <button
                            onClick={() => handleHelpful(qa.id, false)}
                            className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900"
                          >
                            <HandThumbDownIcon className="w-4 h-4" />
                            <span>({qa.notHelpful})</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="ml-11 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-yellow-800 text-sm">
                    This question is awaiting an answer from our support team or community experts.
                  </p>
                </div>
              )}
            </motion.div>
          ))
        )}
      </div>

      {/* Ask Question Modal */}
      <AnimatePresence>
        {showAskQuestion && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
            onClick={() => setShowAskQuestion(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-md bg-white rounded-xl shadow-xl overflow-hidden"
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Ask a Question about {productName}
                </h3>
                
                <form onSubmit={handleAskQuestion} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Question
                    </label>
                    <textarea
                      name="question"
                      rows={4}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                      placeholder="What would you like to know about this product?"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Please be specific and include relevant details to help others provide better answers.
                    </p>
                  </div>
                  
                  <div className="flex space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAskQuestion(false)}
                      className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="flex-1 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600"
                    >
                      Ask Question
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProductQA;
