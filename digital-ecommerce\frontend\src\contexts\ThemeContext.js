import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

// Gaming Store Theme Context - 2025
// Provides comprehensive theme management with gaming aesthetics and accessibility

const ThemeContext = createContext();

// Theme configuration
const THEME_CONFIG = {
  STORAGE_KEY: 'gaming-store-theme',
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark',
    SYSTEM: 'system'
  },
  TRANSITION_DURATION: 300, // ms
  KEYBOARD_SHORTCUT: 'KeyT' // Ctrl+Shift+T
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(THEME_CONFIG.THEMES.SYSTEM);
  const [systemTheme, setSystemTheme] = useState(THEME_CONFIG.THEMES.LIGHT);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isSystemTheme, setIsSystemTheme] = useState(true);

  // Detect system theme preference
  const detectSystemTheme = useCallback(() => {
    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.LIGHT;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    return mediaQuery.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;
  }, []);

  // Get effective theme (resolves 'system' to actual theme)
  const getEffectiveTheme = useCallback((themeValue) => {
    if (themeValue === THEME_CONFIG.THEMES.SYSTEM) {
      return systemTheme;
    }
    return themeValue;
  }, [systemTheme]);

  // Apply theme to document
  const applyTheme = useCallback((themeValue, withTransition = true) => {
    if (typeof document === 'undefined') return;

    const effectiveTheme = getEffectiveTheme(themeValue);
    const root = document.documentElement;

    // Add transition class for smooth theme switching
    if (withTransition) {
      setIsTransitioning(true);
      root.classList.add('theme-transition');
      
      // Remove transition class after animation
      setTimeout(() => {
        root.classList.remove('theme-transition');
        setIsTransitioning(false);
      }, THEME_CONFIG.TRANSITION_DURATION);
    }

    // Apply theme attribute
    root.setAttribute('data-theme', effectiveTheme);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      const themeColors = {
        [THEME_CONFIG.THEMES.LIGHT]: '#ffffff',
        [THEME_CONFIG.THEMES.DARK]: '#0a0a0a'
      };
      metaThemeColor.setAttribute('content', themeColors[effectiveTheme]);
    }

    // Gaming-specific enhancements
    if (effectiveTheme === THEME_CONFIG.THEMES.DARK) {
      // Enable gaming mode optimizations
      root.style.setProperty('--gaming-mode', '1');
      
      // Enhance RGB effects for dark mode
      root.classList.add('gaming-dark-mode');
    } else {
      root.style.setProperty('--gaming-mode', '0');
      root.classList.remove('gaming-dark-mode');
    }

    // Announce theme change to screen readers
    announceThemeChange(effectiveTheme);
  }, [getEffectiveTheme]);

  // Announce theme change for accessibility
  const announceThemeChange = useCallback((effectiveTheme) => {
    if (typeof document === 'undefined') return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = `Theme changed to ${effectiveTheme} mode`;
    
    document.body.appendChild(announcement);
    
    // Remove announcement after screen readers have processed it
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  // Save theme preference to localStorage
  const saveThemePreference = useCallback((themeValue) => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(THEME_CONFIG.STORAGE_KEY, themeValue);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  }, []);

  // Load theme preference from localStorage
  const loadThemePreference = useCallback(() => {
    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.SYSTEM;
    
    try {
      const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);
      if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {
        return saved;
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error);
    }
    
    return THEME_CONFIG.THEMES.SYSTEM;
  }, []);

  // Toggle theme function
  const toggleTheme = useCallback(() => {
    const currentEffective = getEffectiveTheme(theme);
    let newTheme;

    if (theme === THEME_CONFIG.THEMES.SYSTEM) {
      // If on system, switch to opposite of current system theme
      newTheme = currentEffective === THEME_CONFIG.THEMES.DARK 
        ? THEME_CONFIG.THEMES.LIGHT 
        : THEME_CONFIG.THEMES.DARK;
    } else {
      // If on manual theme, switch to opposite
      newTheme = theme === THEME_CONFIG.THEMES.DARK 
        ? THEME_CONFIG.THEMES.LIGHT 
        : THEME_CONFIG.THEMES.DARK;
    }

    setTheme(newTheme);
    setIsSystemTheme(false);
    saveThemePreference(newTheme);
    applyTheme(newTheme);

    // Gaming feedback - trigger RGB pulse effect
    triggerGamingFeedback();
  }, [theme, getEffectiveTheme, saveThemePreference, applyTheme]);

  // Set specific theme
  const setSpecificTheme = useCallback((newTheme) => {
    if (!Object.values(THEME_CONFIG.THEMES).includes(newTheme)) return;

    setTheme(newTheme);
    setIsSystemTheme(newTheme === THEME_CONFIG.THEMES.SYSTEM);
    saveThemePreference(newTheme);
    applyTheme(newTheme);
  }, [saveThemePreference, applyTheme]);

  // Gaming feedback effect
  const triggerGamingFeedback = useCallback(() => {
    if (typeof document === 'undefined') return;

    // Add temporary RGB pulse class to body
    document.body.classList.add('theme-switch-pulse');
    
    setTimeout(() => {
      document.body.classList.remove('theme-switch-pulse');
    }, 600);
  }, []);

  // Initialize theme on mount
  useEffect(() => {
    const initialSystemTheme = detectSystemTheme();
    setSystemTheme(initialSystemTheme);

    const savedTheme = loadThemePreference();
    setTheme(savedTheme);
    setIsSystemTheme(savedTheme === THEME_CONFIG.THEMES.SYSTEM);
    
    // Apply theme without transition on initial load
    applyTheme(savedTheme, false);
  }, [detectSystemTheme, loadThemePreference, applyTheme]);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e) => {
      const newSystemTheme = e.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;
      setSystemTheme(newSystemTheme);
      
      // If currently using system theme, apply the change
      if (theme === THEME_CONFIG.THEMES.SYSTEM) {
        applyTheme(THEME_CONFIG.THEMES.SYSTEM);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme, applyTheme]);

  // Keyboard shortcut support
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleKeyDown = (event) => {
      // Ctrl+Shift+T to toggle theme
      if (event.ctrlKey && event.shiftKey && event.code === THEME_CONFIG.KEYBOARD_SHORTCUT) {
        event.preventDefault();
        toggleTheme();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleTheme]);

  // Gaming-specific theme utilities
  const isGamingMode = getEffectiveTheme(theme) === THEME_CONFIG.THEMES.DARK;
  const shouldUseRGBEffects = isGamingMode;

  const contextValue = {
    // Current theme state
    theme,
    systemTheme,
    effectiveTheme: getEffectiveTheme(theme),
    isSystemTheme,
    isTransitioning,
    
    // Theme actions
    toggleTheme,
    setTheme: setSpecificTheme,
    
    // Gaming-specific
    isGamingMode,
    shouldUseRGBEffects,
    
    // Utilities
    themes: THEME_CONFIG.THEMES,
    
    // Accessibility
    announceThemeChange: (message) => announceThemeChange(message)
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

// Gaming-specific theme utilities
export const useGamingTheme = () => {
  const theme = useTheme();
  
  return {
    ...theme,
    // Gaming-specific helpers
    getRGBGlow: (intensity = 'normal') => {
      if (!theme.shouldUseRGBEffects) return '';
      
      const glowMap = {
        subtle: 'var(--rgb-glow)',
        normal: 'var(--rgb-glow-strong)',
        intense: '0 0 50px rgba(255, 179, 102, 0.8)'
      };
      
      return glowMap[intensity] || glowMap.normal;
    },
    
    getGamingAccent: (type = 'primary') => {
      const accentMap = {
        primary: 'var(--accent-primary)',
        secondary: 'var(--accent-secondary)',
        tertiary: 'var(--accent-tertiary)',
        neon: 'var(--accent-secondary)',
        cyber: 'var(--accent-tertiary)'
      };
      
      return accentMap[type] || accentMap.primary;
    }
  };
};

// Theme detection utility for SSR
export const getInitialTheme = () => {
  if (typeof window === 'undefined') return THEME_CONFIG.THEMES.DARK; // Default for SSR
  
  try {
    const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);
    if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {
      return saved;
    }
  } catch (error) {
    console.warn('Failed to load theme preference:', error);
  }
  
  // Fallback to system preference or dark mode for gaming aesthetic
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  return systemPrefersDark ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;
};

export default ThemeContext;
