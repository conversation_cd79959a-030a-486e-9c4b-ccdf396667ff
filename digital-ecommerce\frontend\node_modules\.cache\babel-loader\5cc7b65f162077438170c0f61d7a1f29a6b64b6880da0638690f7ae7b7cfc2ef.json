{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\AddProductModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PhotoIcon, PlusIcon, TrashIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';\nimport { categories } from '../data/products';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddProductModal = ({\n  isOpen,\n  onClose,\n  onSubmit\n}) => {\n  _s();\n  var _steps;\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    shortDescription: '',\n    price: '',\n    discountPrice: '',\n    currency: 'USD',\n    category: '',\n    subcategory: '',\n    type: 'physical',\n    stockCount: '',\n    sku: '',\n    tags: [],\n    keywords: '',\n    isActive: true,\n    isFeatured: false,\n    specifications: {},\n    images: []\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const [newTag, setNewTag] = useState('');\n\n  // Auto-generate SKU when product name changes\n  useEffect(() => {\n    if (formData.name && !formData.sku) {\n      const sku = formData.name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 8) + '-' + Date.now().toString().slice(-4);\n      setFormData(prev => ({\n        ...prev,\n        sku\n      }));\n    }\n  }, [formData.name]);\n  const steps = [{\n    id: 1,\n    name: 'Basic Info',\n    description: 'Product name, description, and category'\n  }, {\n    id: 2,\n    name: 'Pricing',\n    description: 'Price, discounts, and currency'\n  }, {\n    id: 3,\n    name: 'Images',\n    description: 'Product photos and media'\n  }, {\n    id: 4,\n    name: 'Details',\n    description: 'Stock, SKU, and specifications'\n  }, {\n    id: 5,\n    name: 'Settings',\n    description: 'Tags, keywords, and publication'\n  }];\n  const validateStep = step => {\n    const newErrors = {};\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Product name is required';\n        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';\n        if (!formData.description.trim()) newErrors.description = 'Description is required';\n        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';\n        if (!formData.category) newErrors.category = 'Category is required';\n        break;\n      case 2:\n        if (!formData.price) newErrors.price = 'Price is required';\n        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';\n        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {\n          newErrors.discountPrice = 'Discount price must be a positive number';\n        }\n        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {\n          newErrors.discountPrice = 'Discount price must be less than regular price';\n        }\n        break;\n      case 3:\n        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';\n        break;\n      case 4:\n        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {\n          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';\n        }\n        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';\n        break;\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleNext = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    }\n  };\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleImageUpload = files => {\n    const newImages = Array.from(files).map(file => ({\n      id: Date.now() + Math.random(),\n      file,\n      url: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size\n    }));\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...newImages]\n    }));\n  };\n  const handleDrag = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageUpload(e.dataTransfer.files);\n    }\n  };\n  const removeImage = imageId => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(img => img.id !== imageId)\n    }));\n  };\n  const moveImage = (fromIndex, toIndex) => {\n    const newImages = [...formData.images];\n    const [removed] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, removed);\n    setFormData(prev => ({\n      ...prev,\n      images: newImages\n    }));\n  };\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n  const removeTag = tagToRemove => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) return;\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n      // Reset form only if submission was successful\n      setFormData({\n        name: '',\n        description: '',\n        shortDescription: '',\n        price: '',\n        discountPrice: '',\n        currency: 'USD',\n        category: '',\n        subcategory: '',\n        type: 'physical',\n        stockCount: '',\n        sku: '',\n        tags: [],\n        keywords: '',\n        isActive: true,\n        isFeatured: false,\n        specifications: {},\n        images: []\n      });\n      setCurrentStep(1);\n      setErrors({});\n    } catch (error) {\n      // Error handling is now done in the parent component\n      console.error('Error creating product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const selectedCategory = categories.find(cat => cat.id === formData.category);\n  const renderStepContent = () => {\n    var _selectedCategory$sub, _categories$find;\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.name ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Enter product name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Short Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.shortDescription,\n              onChange: e => handleInputChange('shortDescription', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n              placeholder: \"Brief product description\",\n              maxLength: 150\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [formData.shortDescription.length, \"/150 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Description *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description,\n              onChange: e => handleInputChange('description', e.target.value),\n              rows: 4,\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.description ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Detailed product description\",\n              maxLength: 2000\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 38\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [formData.description.length, \"/2000 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.category,\n                onChange: e => handleInputChange('category', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.category ? 'border-red-500' : 'border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Subcategory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.subcategory,\n                onChange: e => handleInputChange('subcategory', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                disabled: !(selectedCategory !== null && selectedCategory !== void 0 && selectedCategory.subcategories),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a subcategory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), selectedCategory === null || selectedCategory === void 0 ? void 0 : (_selectedCategory$sub = selectedCategory.subcategories) === null || _selectedCategory$sub === void 0 ? void 0 : _selectedCategory$sub.map(sub => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: sub,\n                  children: sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                }, sub, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  value: \"physical\",\n                  checked: formData.type === 'physical',\n                  onChange: e => handleInputChange('type', e.target.value),\n                  className: \"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), \"Physical Product\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  value: \"digital\",\n                  checked: formData.type === 'digital',\n                  onChange: e => handleInputChange('type', e.target.value),\n                  className: \"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), \"Digital Product\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Price * (\", formData.currency, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                min: \"0\",\n                value: formData.price,\n                onChange: e => handleInputChange('price', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.price ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"0.00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), errors.price && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.currency,\n                onChange: e => handleInputChange('currency', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"USD\",\n                  children: \"USD ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"EUR\",\n                  children: \"EUR (\\u20AC)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"GBP\",\n                  children: \"GBP (\\xA3)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"CAD\",\n                  children: \"CAD (C$)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: [\"Discount Price (\", formData.currency, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              min: \"0\",\n              value: formData.discountPrice,\n              onChange: e => handleInputChange('discountPrice', e.target.value),\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.discountPrice ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"0.00 (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), errors.discountPrice && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.discountPrice\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 40\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: \"Leave empty if no discount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), formData.price && formData.discountPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-800\",\n                children: \"Discount Amount:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-green-800\",\n                children: [formData.currency, \" \", (parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-800\",\n                children: \"Discount Percentage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-green-800\",\n                children: [((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price) * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Images *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive ? 'border-light-orange-500 bg-light-orange-50' : errors.images ? 'border-red-500 bg-red-50' : 'border-gray-300 hover:border-light-orange-400'}`,\n              onDragEnter: handleDrag,\n              onDragLeave: handleDrag,\n              onDragOver: handleDrag,\n              onDrop: handleDrop,\n              children: [/*#__PURE__*/_jsxDEV(ArrowUpTrayIcon, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"file-upload\",\n                  className: \"cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-sm font-medium text-gray-900\",\n                    children: \"Drop images here or click to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-1 block text-xs text-gray-500\",\n                    children: \"PNG, JPG, GIF up to 10MB each\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"file-upload\",\n                  name: \"file-upload\",\n                  type: \"file\",\n                  className: \"sr-only\",\n                  multiple: true,\n                  accept: \"image/*\",\n                  onChange: e => handleImageUpload(e.target.files)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), errors.images && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.images\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), formData.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-700 mb-3\",\n              children: [\"Uploaded Images (\", formData.images.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n              children: formData.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: image.url,\n                  alt: image.name,\n                  className: \"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => removeImage(image.id),\n                    className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 23\n                }, this), index === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\",\n                  children: \"Main\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 25\n                }, this)]\n              }, image.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-xs text-gray-500\",\n              children: \"The first image will be used as the main product image. Drag to reorder.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"SKU (Stock Keeping Unit) *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.sku,\n                onChange: e => handleInputChange('sku', e.target.value.toUpperCase()),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.sku ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"AUTO-GENERATED\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), errors.sku && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.sku\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 32\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"Unique identifier for this product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this), formData.type === 'physical' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Stock Quantity *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"0\",\n                value: formData.stockCount,\n                onChange: e => handleInputChange('stockCount', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.stockCount ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), errors.stockCount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.stockCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Specifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Specification name (e.g., Weight)\",\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Value (e.g., 1.5 kg)\",\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\",\n                children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add Specification\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Tags\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2 mb-3\",\n              children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\",\n                children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeTag(tag),\n                  className: \"ml-2 text-light-orange-600 hover:text-light-orange-800\",\n                  children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newTag,\n                onChange: e => setNewTag(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && (e.preventDefault(), addTag()),\n                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                placeholder: \"Add a tag\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addTag,\n                className: \"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\",\n                children: \"Add\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Keywords (for search)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.keywords,\n              onChange: e => handleInputChange('keywords', e.target.value),\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n              placeholder: \"Enter keywords separated by commas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: \"Help customers find this product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"isActive\",\n                checked: formData.isActive,\n                onChange: e => handleInputChange('isActive', e.target.checked),\n                className: \"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"isActive\",\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Publish product (make it visible to customers)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"isFeatured\",\n                checked: formData.isFeatured,\n                onChange: e => handleInputChange('isFeatured', e.target.checked),\n                className: \"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"isFeatured\",\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Feature this product (show in featured sections)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-800 mb-2\",\n              children: \"Product Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-700 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 20\n                }, this), \" \", formData.name || 'Not set']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Price:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 20\n                }, this), \" \", formData.currency, \" \", formData.price || '0.00']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Category:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 20\n                }, this), \" \", ((_categories$find = categories.find(c => c.id === formData.category)) === null || _categories$find === void 0 ? void 0 : _categories$find.name) || 'Not set']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 20\n                }, this), \" \", formData.type]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Images:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 20\n                }, this), \" \", formData.images.length, \" uploaded\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 20\n                }, this), \" \", formData.isActive ? 'Active' : 'Draft']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step content for step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      exit: {\n        opacity: 0\n      },\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0.9,\n          opacity: 0\n        },\n        onClick: e => e.stopPropagation(),\n        className: \"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Add New Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Step \", currentStep, \" of \", steps.length, \": \", (_steps = steps[currentStep - 1]) === null || _steps === void 0 ? void 0 : _steps.description]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep > step.id ? 'bg-green-500 text-white' : currentStep === step.id ? 'bg-light-orange-500 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: currentStep > step.id ? '✓' : step.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `ml-2 text-sm font-medium ${currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'}`,\n                children: step.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-0.5 mx-4 ${currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 max-h-96 overflow-y-auto\",\n          children: renderStepContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handlePrev,\n            disabled: currentStep === 1,\n            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this), currentStep < steps.length ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleNext,\n              className: \"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\",\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              disabled: isSubmitting,\n              className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: isSubmitting ? 'Creating...' : 'Create Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 674,\n    columnNumber: 5\n  }, this);\n};\n_s(AddProductModal, \"Zzz70ppU0GFoeufFOVQRLwdtcEE=\");\n_c = AddProductModal;\nexport default AddProductModal;\nvar _c;\n$RefreshReg$(_c, \"AddProductModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PhotoIcon", "PlusIcon", "TrashIcon", "ArrowUpTrayIcon", "categories", "jsxDEV", "_jsxDEV", "AddProductModal", "isOpen", "onClose", "onSubmit", "_s", "_steps", "currentStep", "setCurrentStep", "formData", "setFormData", "name", "description", "shortDescription", "price", "discountPrice", "currency", "category", "subcategory", "type", "stockCount", "sku", "tags", "keywords", "isActive", "isFeatured", "specifications", "images", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "dragActive", "setDragActive", "newTag", "setNewTag", "toUpperCase", "replace", "substring", "Date", "now", "toString", "slice", "prev", "steps", "id", "validateStep", "step", "newErrors", "trim", "length", "isNaN", "parseFloat", "parseInt", "Object", "keys", "handleNext", "Math", "min", "handlePrev", "max", "handleInputChange", "field", "value", "handleImageUpload", "files", "newImages", "Array", "from", "map", "file", "random", "url", "URL", "createObjectURL", "size", "handleDrag", "e", "preventDefault", "stopPropagation", "handleDrop", "dataTransfer", "removeImage", "imageId", "filter", "img", "moveImage", "fromIndex", "toIndex", "removed", "splice", "addTag", "includes", "removeTag", "tagToRemove", "tag", "handleSubmit", "error", "console", "selectedCate<PERSON><PERSON>", "find", "cat", "renderStepContent", "_selectedCategory$sub", "_categories$find", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "rows", "disabled", "subcategories", "sub", "l", "checked", "toFixed", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "htmlFor", "multiple", "accept", "image", "index", "src", "alt", "onClick", "onKeyPress", "key", "c", "div", "initial", "opacity", "animate", "exit", "scale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AddProductModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  XMarkIcon,\n  PhotoIcon,\n  PlusIcon,\n  TrashIcon,\n  ArrowUpTrayIcon\n} from '@heroicons/react/24/outline';\nimport { categories } from '../data/products';\n\nconst AddProductModal = ({ isOpen, onClose, onSubmit }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    shortDescription: '',\n    price: '',\n    discountPrice: '',\n    currency: 'USD',\n    category: '',\n    subcategory: '',\n    type: 'physical',\n    stockCount: '',\n    sku: '',\n    tags: [],\n    keywords: '',\n    isActive: true,\n    isFeatured: false,\n    specifications: {},\n    images: []\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const [newTag, setNewTag] = useState('');\n\n  // Auto-generate SKU when product name changes\n  useEffect(() => {\n    if (formData.name && !formData.sku) {\n      const sku = formData.name\n        .toUpperCase()\n        .replace(/[^A-Z0-9]/g, '')\n        .substring(0, 8) + '-' + Date.now().toString().slice(-4);\n      setFormData(prev => ({ ...prev, sku }));\n    }\n  }, [formData.name]);\n\n  const steps = [\n    { id: 1, name: 'Basic Info', description: 'Product name, description, and category' },\n    { id: 2, name: 'Pricing', description: 'Price, discounts, and currency' },\n    { id: 3, name: 'Images', description: 'Product photos and media' },\n    { id: 4, name: 'Details', description: 'Stock, SKU, and specifications' },\n    { id: 5, name: 'Settings', description: 'Tags, keywords, and publication' }\n  ];\n\n  const validateStep = (step) => {\n    const newErrors = {};\n\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Product name is required';\n        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';\n        if (!formData.description.trim()) newErrors.description = 'Description is required';\n        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';\n        if (!formData.category) newErrors.category = 'Category is required';\n        break;\n      case 2:\n        if (!formData.price) newErrors.price = 'Price is required';\n        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';\n        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {\n          newErrors.discountPrice = 'Discount price must be a positive number';\n        }\n        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {\n          newErrors.discountPrice = 'Discount price must be less than regular price';\n        }\n        break;\n      case 3:\n        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';\n        break;\n      case 4:\n        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {\n          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';\n        }\n        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';\n        break;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    }\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const handleImageUpload = (files) => {\n    const newImages = Array.from(files).map(file => ({\n      id: Date.now() + Math.random(),\n      file,\n      url: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size\n    }));\n\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...newImages]\n    }));\n  };\n\n  const handleDrag = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageUpload(e.dataTransfer.files);\n    }\n  };\n\n  const removeImage = (imageId) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(img => img.id !== imageId)\n    }));\n  };\n\n  const moveImage = (fromIndex, toIndex) => {\n    const newImages = [...formData.images];\n    const [removed] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, removed);\n    setFormData(prev => ({ ...prev, images: newImages }));\n  };\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n\n  const removeTag = (tagToRemove) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) return;\n\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n      // Reset form only if submission was successful\n      setFormData({\n        name: '',\n        description: '',\n        shortDescription: '',\n        price: '',\n        discountPrice: '',\n        currency: 'USD',\n        category: '',\n        subcategory: '',\n        type: 'physical',\n        stockCount: '',\n        sku: '',\n        tags: [],\n        keywords: '',\n        isActive: true,\n        isFeatured: false,\n        specifications: {},\n        images: []\n      });\n      setCurrentStep(1);\n      setErrors({});\n    } catch (error) {\n      // Error handling is now done in the parent component\n      console.error('Error creating product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const selectedCategory = categories.find(cat => cat.id === formData.category);\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.name ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter product name\"\n              />\n              {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Short Description\n              </label>\n              <input\n                type=\"text\"\n                value={formData.shortDescription}\n                onChange={(e) => handleInputChange('shortDescription', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Brief product description\"\n                maxLength={150}\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.shortDescription.length}/150 characters</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.description ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Detailed product description\"\n                maxLength={2000}\n              />\n              {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.description.length}/2000 characters</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => handleInputChange('category', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.category ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">Select a category</option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"mt-1 text-sm text-red-600\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subcategory\n                </label>\n                <select\n                  value={formData.subcategory}\n                  onChange={(e) => handleInputChange('subcategory', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  disabled={!selectedCategory?.subcategories}\n                >\n                  <option value=\"\">Select a subcategory</option>\n                  {selectedCategory?.subcategories?.map(sub => (\n                    <option key={sub} value={sub}>\n                      {sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Type\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"physical\"\n                    checked={formData.type === 'physical'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Physical Product\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"digital\"\n                    checked={formData.type === 'digital'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Digital Product\n                </label>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Price * ({formData.currency})\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  value={formData.price}\n                  onChange={(e) => handleInputChange('price', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.price ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"0.00\"\n                />\n                {errors.price && <p className=\"mt-1 text-sm text-red-600\">{errors.price}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Currency\n                </label>\n                <select\n                  value={formData.currency}\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                >\n                  <option value=\"USD\">USD ($)</option>\n                  <option value=\"EUR\">EUR (€)</option>\n                  <option value=\"GBP\">GBP (£)</option>\n                  <option value=\"CAD\">CAD (C$)</option>\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Discount Price ({formData.currency})\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.discountPrice}\n                onChange={(e) => handleInputChange('discountPrice', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.discountPrice ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00 (optional)\"\n              />\n              {errors.discountPrice && <p className=\"mt-1 text-sm text-red-600\">{errors.discountPrice}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">Leave empty if no discount</p>\n            </div>\n\n            {formData.price && formData.discountPrice && (\n              <div className=\"p-4 bg-green-50 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Amount:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {formData.currency} {(parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between mt-1\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Percentage:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {(((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price)) * 100).toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Images *\n              </label>\n              <div\n                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n                  dragActive\n                    ? 'border-light-orange-500 bg-light-orange-50'\n                    : errors.images\n                      ? 'border-red-500 bg-red-50'\n                      : 'border-gray-300 hover:border-light-orange-400'\n                }`}\n                onDragEnter={handleDrag}\n                onDragLeave={handleDrag}\n                onDragOver={handleDrag}\n                onDrop={handleDrop}\n              >\n                <ArrowUpTrayIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <div className=\"mt-4\">\n                  <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n                    <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                      Drop images here or click to upload\n                    </span>\n                    <span className=\"mt-1 block text-xs text-gray-500\">\n                      PNG, JPG, GIF up to 10MB each\n                    </span>\n                  </label>\n                  <input\n                    id=\"file-upload\"\n                    name=\"file-upload\"\n                    type=\"file\"\n                    className=\"sr-only\"\n                    multiple\n                    accept=\"image/*\"\n                    onChange={(e) => handleImageUpload(e.target.files)}\n                  />\n                </div>\n              </div>\n              {errors.images && <p className=\"mt-1 text-sm text-red-600\">{errors.images}</p>}\n            </div>\n\n            {formData.images.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-700 mb-3\">\n                  Uploaded Images ({formData.images.length})\n                </h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                  {formData.images.map((image, index) => (\n                    <div key={image.id} className=\"relative group\">\n                      <img\n                        src={image.url}\n                        alt={image.name}\n                        className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                      />\n                      <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\">\n                        <button\n                          onClick={() => removeImage(image.id)}\n                          className=\"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                      {index === 0 && (\n                        <div className=\"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\">\n                          Main\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n                <p className=\"mt-2 text-xs text-gray-500\">\n                  The first image will be used as the main product image. Drag to reorder.\n                </p>\n              </div>\n            )}\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  SKU (Stock Keeping Unit) *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.sku}\n                  onChange={(e) => handleInputChange('sku', e.target.value.toUpperCase())}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.sku ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"AUTO-GENERATED\"\n                />\n                {errors.sku && <p className=\"mt-1 text-sm text-red-600\">{errors.sku}</p>}\n                <p className=\"mt-1 text-xs text-gray-500\">Unique identifier for this product</p>\n              </div>\n\n              {formData.type === 'physical' && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Stock Quantity *\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    value={formData.stockCount}\n                    onChange={(e) => handleInputChange('stockCount', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                      errors.stockCount ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder=\"0\"\n                  />\n                  {errors.stockCount && <p className=\"mt-1 text-sm text-red-600\">{errors.stockCount}</p>}\n                </div>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Specifications\n              </label>\n              <div className=\"space-y-3\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Specification name (e.g., Weight)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Value (e.g., 1.5 kg)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                </div>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\"\n                >\n                  <PlusIcon className=\"w-4 h-4\" />\n                  <span>Add Specification</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Tags\n              </label>\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {formData.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\"\n                  >\n                    {tag}\n                    <button\n                      onClick={() => removeTag(tag)}\n                      className=\"ml-2 text-light-orange-600 hover:text-light-orange-800\"\n                    >\n                      <XMarkIcon className=\"w-3 h-3\" />\n                    </button>\n                  </span>\n                ))}\n              </div>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  placeholder=\"Add a tag\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addTag}\n                  className=\"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\"\n                >\n                  Add\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Keywords (for search)\n              </label>\n              <textarea\n                value={formData.keywords}\n                onChange={(e) => handleInputChange('keywords', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Enter keywords separated by commas\"\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">Help customers find this product</p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isActive\"\n                  checked={formData.isActive}\n                  onChange={(e) => handleInputChange('isActive', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isActive\" className=\"text-sm font-medium text-gray-700\">\n                  Publish product (make it visible to customers)\n                </label>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isFeatured\"\n                  checked={formData.isFeatured}\n                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isFeatured\" className=\"text-sm font-medium text-gray-700\">\n                  Feature this product (show in featured sections)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"p-4 bg-blue-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-800 mb-2\">Product Summary</h4>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p><strong>Name:</strong> {formData.name || 'Not set'}</p>\n                <p><strong>Price:</strong> {formData.currency} {formData.price || '0.00'}</p>\n                <p><strong>Category:</strong> {categories.find(c => c.id === formData.category)?.name || 'Not set'}</p>\n                <p><strong>Type:</strong> {formData.type}</p>\n                <p><strong>Images:</strong> {formData.images.length} uploaded</p>\n                <p><strong>Status:</strong> {formData.isActive ? 'Active' : 'Draft'}</p>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step content for step {currentStep}</div>;\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          onClick={(e) => e.stopPropagation()}\n          className=\"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\"\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">Add New Product</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Progress Steps */}\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                    currentStep > step.id\n                      ? 'bg-green-500 text-white'\n                      : currentStep === step.id\n                        ? 'bg-light-orange-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {currentStep > step.id ? '✓' : step.id}\n                  </div>\n                  <span className={`ml-2 text-sm font-medium ${\n                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'\n                  }`}>\n                    {step.name}\n                  </span>\n                  {index < steps.length - 1 && (\n                    <div className={`w-12 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 max-h-96 overflow-y-auto\">\n            {renderStepContent()}\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\">\n            <button\n              onClick={handlePrev}\n              disabled={currentStep === 1}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              Previous\n            </button>\n\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              \n              {currentStep < steps.length ? (\n                <button\n                  onClick={handleNext}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\"\n                >\n                  Next\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? 'Creating...' : 'Create Product'}\n                </button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default AddProductModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,eAAe,QACV,6BAA6B;AACpC,SAASC,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,MAAA;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,CAAC,CAAC;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACY,GAAG,EAAE;MAClC,MAAMA,GAAG,GAAGZ,QAAQ,CAACE,IAAI,CACtByB,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1DhC,WAAW,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB;MAAI,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACZ,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEnB,MAAMiC,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAElC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAE;EAA0C,CAAC,EACrF;IAAEiC,EAAE,EAAE,CAAC;IAAElC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAiC,CAAC,EACzE;IAAEiC,EAAE,EAAE,CAAC;IAAElC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAClE;IAAEiC,EAAE,EAAE,CAAC;IAAElC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAiC,CAAC,EACzE;IAAEiC,EAAE,EAAE,CAAC;IAAElC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAAkC,CAAC,CAC5E;EAED,MAAMkC,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,QAAQD,IAAI;MACV,KAAK,CAAC;QACJ,IAAI,CAACtC,QAAQ,CAACE,IAAI,CAACsC,IAAI,CAAC,CAAC,EAAED,SAAS,CAACrC,IAAI,GAAG,0BAA0B;QACtE,IAAIF,QAAQ,CAACE,IAAI,CAACuC,MAAM,GAAG,GAAG,EAAEF,SAAS,CAACrC,IAAI,GAAG,+CAA+C;QAChG,IAAI,CAACF,QAAQ,CAACG,WAAW,CAACqC,IAAI,CAAC,CAAC,EAAED,SAAS,CAACpC,WAAW,GAAG,yBAAyB;QACnF,IAAIH,QAAQ,CAACG,WAAW,CAACsC,MAAM,GAAG,IAAI,EAAEF,SAAS,CAACpC,WAAW,GAAG,+CAA+C;QAC/G,IAAI,CAACH,QAAQ,CAACQ,QAAQ,EAAE+B,SAAS,CAAC/B,QAAQ,GAAG,sBAAsB;QACnE;MACF,KAAK,CAAC;QACJ,IAAI,CAACR,QAAQ,CAACK,KAAK,EAAEkC,SAAS,CAAClC,KAAK,GAAG,mBAAmB;QAC1D,IAAIqC,KAAK,CAAC1C,QAAQ,CAACK,KAAK,CAAC,IAAIsC,UAAU,CAAC3C,QAAQ,CAACK,KAAK,CAAC,IAAI,CAAC,EAAEkC,SAAS,CAAClC,KAAK,GAAG,iCAAiC;QACjH,IAAIL,QAAQ,CAACM,aAAa,KAAKoC,KAAK,CAAC1C,QAAQ,CAACM,aAAa,CAAC,IAAIqC,UAAU,CAAC3C,QAAQ,CAACM,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;UACxGiC,SAAS,CAACjC,aAAa,GAAG,0CAA0C;QACtE;QACA,IAAIN,QAAQ,CAACM,aAAa,IAAIqC,UAAU,CAAC3C,QAAQ,CAACM,aAAa,CAAC,IAAIqC,UAAU,CAAC3C,QAAQ,CAACK,KAAK,CAAC,EAAE;UAC9FkC,SAAS,CAACjC,aAAa,GAAG,gDAAgD;QAC5E;QACA;MACF,KAAK,CAAC;QACJ,IAAIN,QAAQ,CAACkB,MAAM,CAACuB,MAAM,KAAK,CAAC,EAAEF,SAAS,CAACrB,MAAM,GAAG,wCAAwC;QAC7F;MACF,KAAK,CAAC;QACJ,IAAIlB,QAAQ,CAACU,IAAI,KAAK,UAAU,KAAK,CAACV,QAAQ,CAACW,UAAU,IAAI+B,KAAK,CAAC1C,QAAQ,CAACW,UAAU,CAAC,IAAIiC,QAAQ,CAAC5C,QAAQ,CAACW,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;UAC7H4B,SAAS,CAAC5B,UAAU,GAAG,iEAAiE;QAC1F;QACA,IAAI,CAACX,QAAQ,CAACY,GAAG,CAAC4B,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC3B,GAAG,GAAG,iBAAiB;QAC3D;IACJ;IAEAQ,SAAS,CAACmB,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIV,YAAY,CAACvC,WAAW,CAAC,EAAE;MAC7BC,cAAc,CAACmC,IAAI,IAAIc,IAAI,CAACC,GAAG,CAACf,IAAI,GAAG,CAAC,EAAEC,KAAK,CAACM,MAAM,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMS,UAAU,GAAGA,CAAA,KAAM;IACvBnD,cAAc,CAACmC,IAAI,IAAIc,IAAI,CAACG,GAAG,CAACjB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CrD,WAAW,CAACiC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD,IAAInC,MAAM,CAACkC,KAAK,CAAC,EAAE;MACjBjC,SAAS,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAACC,IAAI,KAAK;MAC/CzB,EAAE,EAAEN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGiB,IAAI,CAACc,MAAM,CAAC,CAAC;MAC9BD,IAAI;MACJE,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC9B3D,IAAI,EAAE2D,IAAI,CAAC3D,IAAI;MACfgE,IAAI,EAAEL,IAAI,CAACK;IACb,CAAC,CAAC,CAAC;IAEHjE,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhB,MAAM,EAAE,CAAC,GAAGgB,IAAI,CAAChB,MAAM,EAAE,GAAGuC,SAAS;IACvC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMU,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAAC1D,IAAI,KAAK,WAAW,IAAI0D,CAAC,CAAC1D,IAAI,KAAK,UAAU,EAAE;MACnDc,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,CAAC,CAAC1D,IAAI,KAAK,WAAW,EAAE;MACjCc,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM+C,UAAU,GAAIH,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB9C,aAAa,CAAC,KAAK,CAAC;IAEpB,IAAI4C,CAAC,CAACI,YAAY,CAAChB,KAAK,IAAIY,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC,EAAE;MACnDD,iBAAiB,CAACa,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC;IACzC;EACF,CAAC;EAED,MAAMiB,WAAW,GAAIC,OAAO,IAAK;IAC/BzE,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhB,MAAM,EAAEgB,IAAI,CAAChB,MAAM,CAACyD,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxC,EAAE,KAAKsC,OAAO;IACtD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,SAAS,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IACxC,MAAMtB,SAAS,GAAG,CAAC,GAAGzD,QAAQ,CAACkB,MAAM,CAAC;IACtC,MAAM,CAAC8D,OAAO,CAAC,GAAGvB,SAAS,CAACwB,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;IAChDrB,SAAS,CAACwB,MAAM,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;IACrC/E,WAAW,CAACiC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhB,MAAM,EAAEuC;IAAU,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAMyB,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIzD,MAAM,CAACe,IAAI,CAAC,CAAC,IAAI,CAACxC,QAAQ,CAACa,IAAI,CAACsE,QAAQ,CAAC1D,MAAM,CAACe,IAAI,CAAC,CAAC,CAAC,EAAE;MAC3DvC,WAAW,CAACiC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPrB,IAAI,EAAE,CAAC,GAAGqB,IAAI,CAACrB,IAAI,EAAEY,MAAM,CAACe,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC;MACHd,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAM0D,SAAS,GAAIC,WAAW,IAAK;IACjCpF,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPrB,IAAI,EAAEqB,IAAI,CAACrB,IAAI,CAAC8D,MAAM,CAACW,GAAG,IAAIA,GAAG,KAAKD,WAAW;IACnD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAClD,YAAY,CAACvC,WAAW,CAAC,EAAE;IAEhCwB,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM3B,QAAQ,CAACK,QAAQ,CAAC;MACxB;MACAC,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,EAAE;QACpBC,KAAK,EAAE,EAAE;QACTC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,EAAE;QACdC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,CAAC,CAAC;QAClBC,MAAM,EAAE;MACV,CAAC,CAAC;MACFnB,cAAc,CAAC,CAAC,CAAC;MACjBqB,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRlE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoE,gBAAgB,GAAGrG,UAAU,CAACsG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACxD,EAAE,KAAKpC,QAAQ,CAACQ,QAAQ,CAAC;EAE7E,MAAMqF,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,gBAAA;IAC9B,QAAQjG,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEP,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACEmB,IAAI,EAAC,MAAM;cACX4C,KAAK,EAAEtD,QAAQ,CAACE,IAAK;cACrBoG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAC3D0C,SAAS,EAAE,6GACT7E,MAAM,CAACjB,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,EACjD;cACHsG,WAAW,EAAC;YAAoB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACDlF,MAAM,CAACjB,IAAI,iBAAIX,OAAA;cAAGyG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACjB;YAAI;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACEmB,IAAI,EAAC,MAAM;cACX4C,KAAK,EAAEtD,QAAQ,CAACI,gBAAiB;cACjCkG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,kBAAkB,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cACvE0C,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC,2BAA2B;cACvCC,SAAS,EAAE;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAEjG,QAAQ,CAACI,gBAAgB,CAACqC,MAAM,EAAC,iBAAe;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACE+D,KAAK,EAAEtD,QAAQ,CAACG,WAAY;cAC5BmG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,aAAa,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAClEoD,IAAI,EAAE,CAAE;cACRV,SAAS,EAAE,6GACT7E,MAAM,CAAChB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;cACHqG,WAAW,EAAC,8BAA8B;cAC1CC,SAAS,EAAE;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACDlF,MAAM,CAAChB,WAAW,iBAAIZ,OAAA;cAAGyG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAAChB;YAAW;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxF9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAEjG,QAAQ,CAACG,WAAW,CAACsC,MAAM,EAAC,kBAAgB;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eAEN9G,OAAA;YAAKyG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1G,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACE+D,KAAK,EAAEtD,QAAQ,CAACQ,QAAS;gBACzB8F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC/D0C,SAAS,EAAE,6GACT7E,MAAM,CAACX,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;gBAAAyF,QAAA,gBAEH1G,OAAA;kBAAQ+D,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1ChH,UAAU,CAACuE,GAAG,CAACpD,QAAQ,iBACtBjB,OAAA;kBAA0B+D,KAAK,EAAE9C,QAAQ,CAAC4B,EAAG;kBAAA6D,QAAA,EAC1CzF,QAAQ,CAACN;gBAAI,GADHM,QAAQ,CAAC4B,EAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRlF,MAAM,CAACX,QAAQ,iBAAIjB,OAAA;gBAAGyG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACX;cAAQ;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAEN9G,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACE+D,KAAK,EAAEtD,QAAQ,CAACS,WAAY;gBAC5B6F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,aAAa,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAClE0C,SAAS,EAAC,2HAA2H;gBACrIW,QAAQ,EAAE,EAACjB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkB,aAAa,CAAC;gBAAAX,QAAA,gBAE3C1G,OAAA;kBAAQ+D,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CX,gBAAgB,aAAhBA,gBAAgB,wBAAAI,qBAAA,GAAhBJ,gBAAgB,CAAEkB,aAAa,cAAAd,qBAAA,uBAA/BA,qBAAA,CAAiClC,GAAG,CAACiD,GAAG,iBACvCtH,OAAA;kBAAkB+D,KAAK,EAAEuD,GAAI;kBAAAZ,QAAA,EAC1BY,GAAG,CAACjF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEkF,CAAC,IAAIA,CAAC,CAACnF,WAAW,CAAC,CAAC;gBAAC,GADlDkF,GAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cAAKyG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B1G,OAAA;gBAAOyG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClC1G,OAAA;kBACEmB,IAAI,EAAC,OAAO;kBACZ4C,KAAK,EAAC,UAAU;kBAChByD,OAAO,EAAE/G,QAAQ,CAACU,IAAI,KAAK,UAAW;kBACtC4F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;kBAC3D0C,SAAS,EAAC;gBAAwD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,oBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBAAOyG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClC1G,OAAA;kBACEmB,IAAI,EAAC,OAAO;kBACZ4C,KAAK,EAAC,SAAS;kBACfyD,OAAO,EAAE/G,QAAQ,CAACU,IAAI,KAAK,SAAU;kBACrC4F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;kBAC3D0C,SAAS,EAAC;gBAAwD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,mBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE9G,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1G,OAAA;YAAKyG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1G,OAAA;cAAKyG,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WACrD,EAACjG,QAAQ,CAACO,QAAQ,EAAC,GAC9B;cAAA;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACb4B,IAAI,EAAC,MAAM;gBACXW,GAAG,EAAC,GAAG;gBACPK,KAAK,EAAEtD,QAAQ,CAACK,KAAM;gBACtBiG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,OAAO,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC5D0C,SAAS,EAAE,6GACT7E,MAAM,CAACd,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;gBACHmG,WAAW,EAAC;cAAM;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACDlF,MAAM,CAACd,KAAK,iBAAId,OAAA;gBAAGyG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACd;cAAK;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eAEN9G,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACE+D,KAAK,EAAEtD,QAAQ,CAACO,QAAS;gBACzB+F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC/D0C,SAAS,EAAC,2HAA2H;gBAAAC,QAAA,gBAErI1G,OAAA;kBAAQ+D,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC9G,OAAA;kBAAQ+D,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC9G,OAAA;kBAAQ+D,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC9G,OAAA;kBAAQ+D,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,kBAC9C,EAACjG,QAAQ,CAACO,QAAQ,EAAC,GACrC;YAAA;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACEmB,IAAI,EAAC,QAAQ;cACb4B,IAAI,EAAC,MAAM;cACXW,GAAG,EAAC,GAAG;cACPK,KAAK,EAAEtD,QAAQ,CAACM,aAAc;cAC9BgG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,eAAe,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cACpE0C,SAAS,EAAE,6GACT7E,MAAM,CAACb,aAAa,GAAG,gBAAgB,GAAG,iBAAiB,EAC1D;cACHkG,WAAW,EAAC;YAAiB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACDlF,MAAM,CAACb,aAAa,iBAAIf,OAAA;cAAGyG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACb;YAAa;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5F9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAELrG,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACM,aAAa,iBACvCf,OAAA;YAAKyG,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC1G,OAAA;cAAKyG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1G,OAAA;gBAAMyG,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5E9G,OAAA;gBAAMyG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC/CjG,QAAQ,CAACO,QAAQ,EAAC,GAAC,EAAC,CAACoC,UAAU,CAAC3C,QAAQ,CAACK,KAAK,CAAC,GAAGsC,UAAU,CAAC3C,QAAQ,CAACM,aAAa,CAAC,EAAE0G,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN9G,OAAA;cAAKyG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD1G,OAAA;gBAAMyG,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChF9G,OAAA;gBAAMyG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC/C,CAAE,CAACtD,UAAU,CAAC3C,QAAQ,CAACK,KAAK,CAAC,GAAGsC,UAAU,CAAC3C,QAAQ,CAACM,aAAa,CAAC,IAAIqC,UAAU,CAAC3C,QAAQ,CAACK,KAAK,CAAC,GAAI,GAAG,EAAE2G,OAAO,CAAC,CAAC,CAAC,EAAC,GACvH;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE9G,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACEyG,SAAS,EAAE,uEACTzE,UAAU,GACN,4CAA4C,GAC5CJ,MAAM,CAACD,MAAM,GACX,0BAA0B,GAC1B,+CAA+C,EACpD;cACH+F,WAAW,EAAE9C,UAAW;cACxB+C,WAAW,EAAE/C,UAAW;cACxBgD,UAAU,EAAEhD,UAAW;cACvBiD,MAAM,EAAE7C,UAAW;cAAA0B,QAAA,gBAEnB1G,OAAA,CAACH,eAAe;gBAAC4G,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D9G,OAAA;gBAAKyG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1G,OAAA;kBAAO8H,OAAO,EAAC,aAAa;kBAACrB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBACrD1G,OAAA;oBAAMyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP9G,OAAA;oBAAMyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACR9G,OAAA;kBACE6C,EAAE,EAAC,aAAa;kBAChBlC,IAAI,EAAC,aAAa;kBAClBQ,IAAI,EAAC,MAAM;kBACXsF,SAAS,EAAC,SAAS;kBACnBsB,QAAQ;kBACRC,MAAM,EAAC,SAAS;kBAChBjB,QAAQ,EAAGlC,CAAC,IAAKb,iBAAiB,CAACa,CAAC,CAACmC,MAAM,CAAC/C,KAAK;gBAAE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlF,MAAM,CAACD,MAAM,iBAAI3B,OAAA;cAAGyG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACD;YAAM;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,EAELrG,QAAQ,CAACkB,MAAM,CAACuB,MAAM,GAAG,CAAC,iBACzBlD,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAIyG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,GAAC,mBACpC,EAACjG,QAAQ,CAACkB,MAAM,CAACuB,MAAM,EAAC,GAC3C;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9G,OAAA;cAAKyG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDjG,QAAQ,CAACkB,MAAM,CAAC0C,GAAG,CAAC,CAAC4D,KAAK,EAAEC,KAAK,kBAChClI,OAAA;gBAAoByG,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC5C1G,OAAA;kBACEmI,GAAG,EAAEF,KAAK,CAACzD,GAAI;kBACf4D,GAAG,EAAEH,KAAK,CAACtH,IAAK;kBAChB8F,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACF9G,OAAA;kBAAKyG,SAAS,EAAC,0IAA0I;kBAAAC,QAAA,eACvJ1G,OAAA;oBACEqI,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC+C,KAAK,CAACpF,EAAE,CAAE;oBACrC4D,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,eAEnE1G,OAAA,CAACJ,SAAS;sBAAC6G,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLoB,KAAK,KAAK,CAAC,iBACVlI,OAAA;kBAAKyG,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,EAAC;gBAEzF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA,GAlBOmB,KAAK,CAACpF,EAAE;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE9G,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1G,OAAA;YAAKyG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1G,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACX4C,KAAK,EAAEtD,QAAQ,CAACY,GAAI;gBACpB0F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,KAAK,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAC3B,WAAW,CAAC,CAAC,CAAE;gBACxEqE,SAAS,EAAE,6GACT7E,MAAM,CAACP,GAAG,GAAG,gBAAgB,GAAG,iBAAiB,EAChD;gBACH4F,WAAW,EAAC;cAAgB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDlF,MAAM,CAACP,GAAG,iBAAIrB,OAAA;gBAAGyG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACP;cAAG;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE9G,OAAA;gBAAGyG,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,EAELrG,QAAQ,CAACU,IAAI,KAAK,UAAU,iBAC3BnB,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAOyG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9G,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACbuC,GAAG,EAAC,GAAG;gBACPK,KAAK,EAAEtD,QAAQ,CAACW,UAAW;gBAC3B2F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,YAAY,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBACjE0C,SAAS,EAAE,6GACT7E,MAAM,CAACR,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,EACvD;gBACH6F,WAAW,EAAC;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EACDlF,MAAM,CAACR,UAAU,iBAAIpB,OAAA;gBAAGyG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACR;cAAU;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cAAKyG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1G,OAAA;gBAAKyG,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD1G,OAAA;kBACEmB,IAAI,EAAC,MAAM;kBACX8F,WAAW,EAAC,mCAAmC;kBAC/CR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC,eACF9G,OAAA;kBACEmB,IAAI,EAAC,MAAM;kBACX8F,WAAW,EAAC,sBAAsB;kBAClCR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9G,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACbsF,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBAEjG1G,OAAA,CAACL,QAAQ;kBAAC8G,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC9G,OAAA;kBAAA0G,QAAA,EAAM;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE9G,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cAAKyG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EACvCjG,QAAQ,CAACa,IAAI,CAAC+C,GAAG,CAAC,CAAC0B,GAAG,EAAEmC,KAAK,kBAC5BlI,OAAA;gBAEEyG,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE5GX,GAAG,eACJ/F,OAAA;kBACEqI,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAACE,GAAG,CAAE;kBAC9BU,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,eAElE1G,OAAA,CAACP,SAAS;oBAACgH,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA,GATJoB,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9G,OAAA;cAAKyG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B1G,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACX4C,KAAK,EAAE7B,MAAO;gBACd6E,QAAQ,EAAGlC,CAAC,IAAK1C,SAAS,CAAC0C,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC3CuE,UAAU,EAAGzD,CAAC,IAAKA,CAAC,CAAC0D,GAAG,KAAK,OAAO,KAAK1D,CAAC,CAACC,cAAc,CAAC,CAAC,EAAEa,MAAM,CAAC,CAAC,CAAE;gBACvEc,SAAS,EAAC,2HAA2H;gBACrIQ,WAAW,EAAC;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACF9G,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACbkH,OAAO,EAAE1C,MAAO;gBAChBc,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,EAC1F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAOyG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA;cACE+D,KAAK,EAAEtD,QAAQ,CAACc,QAAS;cACzBwF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAC/DoD,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAoC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eAEN9G,OAAA;YAAKyG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1G,OAAA;cAAKyG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1G,OAAA;gBACEmB,IAAI,EAAC,UAAU;gBACf0B,EAAE,EAAC,UAAU;gBACb2E,OAAO,EAAE/G,QAAQ,CAACe,QAAS;gBAC3BuF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACQ,OAAO,CAAE;gBACjEf,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACF9G,OAAA;gBAAO8H,OAAO,EAAC,UAAU;gBAACrB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAExE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9G,OAAA;cAAKyG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1G,OAAA;gBACEmB,IAAI,EAAC,UAAU;gBACf0B,EAAE,EAAC,YAAY;gBACf2E,OAAO,EAAE/G,QAAQ,CAACgB,UAAW;gBAC7BsF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,YAAY,EAAEgB,CAAC,CAACmC,MAAM,CAACQ,OAAO,CAAE;gBACnEf,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACF9G,OAAA;gBAAO8H,OAAO,EAAC,YAAY;gBAACrB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9G,OAAA;YAAKyG,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC1G,OAAA;cAAIyG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E9G,OAAA;cAAKyG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C1G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrG,QAAQ,CAACE,IAAI,IAAI,SAAS;cAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D9G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrG,QAAQ,CAACO,QAAQ,EAAC,GAAC,EAACP,QAAQ,CAACK,KAAK,IAAI,MAAM;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7E9G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAN,gBAAA,GAAA1G,UAAU,CAACsG,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAAC3F,EAAE,KAAKpC,QAAQ,CAACQ,QAAQ,CAAC,cAAAuF,gBAAA,uBAAhDA,gBAAA,CAAkD7F,IAAI,KAAI,SAAS;cAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvG9G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrG,QAAQ,CAACU,IAAI;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7C9G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrG,QAAQ,CAACkB,MAAM,CAACuB,MAAM,EAAC,WAAS;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjE9G,OAAA;gBAAA0G,QAAA,gBAAG1G,OAAA;kBAAA0G,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrG,QAAQ,CAACe,QAAQ,GAAG,QAAQ,GAAG,OAAO;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAO9G,OAAA;UAAA0G,QAAA,GAAK,wBAAsB,EAACnG,WAAW;QAAA;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACzD;EACF,CAAC;EAED,IAAI,CAAC5G,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA,CAACR,eAAe;IAAAkH,QAAA,eACd1G,OAAA,CAACT,MAAM,CAACkJ,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,IAAI,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACrBlC,SAAS,EAAC,gFAAgF;MAC1F4B,OAAO,EAAElI,OAAQ;MAAAuG,QAAA,eAEjB1G,OAAA,CAACT,MAAM,CAACkJ,GAAG;QACTC,OAAO,EAAE;UAAEI,KAAK,EAAE,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACpCC,OAAO,EAAE;UAAEE,KAAK,EAAE,CAAC;UAAEH,OAAO,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACjCN,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAACE,eAAe,CAAC,CAAE;QACpC0B,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBAGvF1G,OAAA;UAAKyG,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7E1G,OAAA;YAAA0G,QAAA,gBACE1G,OAAA;cAAIyG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE9G,OAAA;cAAGyG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,OACnC,EAACnG,WAAW,EAAC,MAAI,EAACqC,KAAK,CAACM,MAAM,EAAC,IAAE,GAAA5C,MAAA,GAACsC,KAAK,CAACrC,WAAW,GAAG,CAAC,CAAC,cAAAD,MAAA,uBAAtBA,MAAA,CAAwBM,WAAW;YAAA;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9G,OAAA;YACEqI,OAAO,EAAElI,OAAQ;YACjBsG,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eAE9E1G,OAAA,CAACP,SAAS;cAACgH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9G,OAAA;UAAKyG,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD1G,OAAA;YAAKyG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/C9D,KAAK,CAACyB,GAAG,CAAC,CAACtB,IAAI,EAAEmF,KAAK,kBACrBlI,OAAA;cAAmByG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9C1G,OAAA;gBAAKyG,SAAS,EAAE,6EACdlG,WAAW,GAAGwC,IAAI,CAACF,EAAE,GACjB,yBAAyB,GACzBtC,WAAW,KAAKwC,IAAI,CAACF,EAAE,GACrB,gCAAgC,GAChC,2BAA2B,EAChC;gBAAA6D,QAAA,EACAnG,WAAW,GAAGwC,IAAI,CAACF,EAAE,GAAG,GAAG,GAAGE,IAAI,CAACF;cAAE;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN9G,OAAA;gBAAMyG,SAAS,EAAE,4BACflG,WAAW,IAAIwC,IAAI,CAACF,EAAE,GAAG,eAAe,GAAG,eAAe,EACzD;gBAAA6D,QAAA,EACA3D,IAAI,CAACpC;cAAI;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACNoB,KAAK,GAAGtF,KAAK,CAACM,MAAM,GAAG,CAAC,iBACvBlD,OAAA;gBAAKyG,SAAS,EAAE,mBACdlG,WAAW,GAAGwC,IAAI,CAACF,EAAE,GAAG,cAAc,GAAG,aAAa;cACrD;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACN;YAAA,GAnBO/D,IAAI,CAACF,EAAE;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9G,OAAA;UAAKyG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC1CJ,iBAAiB,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGN9G,OAAA;UAAKyG,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxF1G,OAAA;YACEqI,OAAO,EAAE1E,UAAW;YACpByD,QAAQ,EAAE7G,WAAW,KAAK,CAAE;YAC5BkG,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET9G,OAAA;YAAKyG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B1G,OAAA;cACEqI,OAAO,EAAElI,OAAQ;cACjBsG,SAAS,EAAC,yGAAyG;cAAAC,QAAA,EACpH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERvG,WAAW,GAAGqC,KAAK,CAACM,MAAM,gBACzBlD,OAAA;cACEqI,OAAO,EAAE7E,UAAW;cACpBiD,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAC9G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET9G,OAAA;cACEqI,OAAO,EAAErC,YAAa;cACtBoB,QAAQ,EAAEtF,YAAa;cACvB2E,SAAS,EAAC,qIAAqI;cAAAC,QAAA,EAE9I5E,YAAY,GAAG,aAAa,GAAG;YAAgB;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAACzG,EAAA,CA/vBIJ,eAAe;AAAA8I,EAAA,GAAf9I,eAAe;AAiwBrB,eAAeA,eAAe;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}