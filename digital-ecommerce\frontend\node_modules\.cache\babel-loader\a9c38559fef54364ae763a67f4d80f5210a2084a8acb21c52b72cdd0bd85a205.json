{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ModernNavigation from './components/ModernNavigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport { AdminProvider } from './contexts/AdminContext';\nimport { ProductProvider } from './contexts/ProductContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport PcGamingPage from './pages/PcGamingPage';\nimport PcGamingComparePage from './pages/PcGamingComparePage';\nimport PcGamingProductDetailPage from './pages/PcGamingProductDetailPage';\nimport PcBuilder from './components/PcBuilder';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport AccountPage from './pages/AccountPage';\nimport WishlistPage from './pages/WishlistPage';\nimport AdminLoginPage from './pages/AdminLoginPage';\nimport AdminDashboardPage from './pages/AdminDashboardPage';\nimport AdminProductsPage from './pages/AdminProductsPage';\nimport AdminCategoriesPage from './pages/AdminCategoriesPage';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport AdminProtectedRoute from './components/AdminProtectedRoute';\nimport { HelpPage, ReturnsPage, ShippingPage, TrackOrderPage, PrivacyPage, TermsPage, CookiesPage, OrdersPage } from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(ProductProvider, {\n      children: /*#__PURE__*/_jsxDEV(AdminProvider, {\n        children: /*#__PURE__*/_jsxDEV(UserProvider, {\n          children: /*#__PURE__*/_jsxDEV(CartProvider, {\n            children: /*#__PURE__*/_jsxDEV(ToastProvider, {\n              children: /*#__PURE__*/_jsxDEV(Router, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-screen theme-transition\",\n                  style: {\n                    backgroundColor: 'var(--bg-primary)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ModernNavigation, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/_jsxDEV(Routes, {\n                      children: [/*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 68,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 62,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 61,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/products\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 78,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 72,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 71,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/digital-products\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(DigitalProductsPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 88,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 82,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 81,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/pc-gaming\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(PcGamingPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 98,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 92,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 91,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/pc-builder\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(PcBuilder, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 108,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 102,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 101,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/pc-gaming/compare\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(PcGamingComparePage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 118,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 112,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 111,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/pc-gaming/product/:id\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(PcGamingProductDetailPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 128,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 122,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 121,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/about\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 138,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 132,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 131,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/contact\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 148,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 142,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/checkout\",\n                        element: /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            y: 20\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          exit: {\n                            opacity: 0,\n                            y: -20\n                          },\n                          transition: {\n                            duration: 0.3\n                          },\n                          children: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 158,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 152,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 151,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/help\",\n                        element: /*#__PURE__*/_jsxDEV(HelpPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 161,\n                          columnNumber: 42\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 161,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/returns\",\n                        element: /*#__PURE__*/_jsxDEV(ReturnsPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 162,\n                          columnNumber: 45\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 162,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/shipping\",\n                        element: /*#__PURE__*/_jsxDEV(ShippingPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 163,\n                          columnNumber: 46\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/track\",\n                        element: /*#__PURE__*/_jsxDEV(TrackOrderPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 164,\n                          columnNumber: 43\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 164,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/orders\",\n                        element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 165,\n                          columnNumber: 44\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/privacy\",\n                        element: /*#__PURE__*/_jsxDEV(PrivacyPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 45\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 166,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/terms\",\n                        element: /*#__PURE__*/_jsxDEV(TermsPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 43\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 167,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/cookies\",\n                        element: /*#__PURE__*/_jsxDEV(CookiesPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 168,\n                          columnNumber: 45\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/login\",\n                        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                          requireAuth: false,\n                          children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 171,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 170,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/register\",\n                        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                          requireAuth: false,\n                          children: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 176,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 175,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/reset-password\",\n                        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                          requireAuth: false,\n                          children: /*#__PURE__*/_jsxDEV(ResetPasswordPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 181,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 180,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/account\",\n                        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                          children: /*#__PURE__*/_jsxDEV(AccountPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 186,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/wishlist\",\n                        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                          children: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 191,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 190,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 189,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/admin/login\",\n                        element: /*#__PURE__*/_jsxDEV(AdminLoginPage, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 197,\n                          columnNumber: 49\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/admin/dashboard\",\n                        element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                          children: /*#__PURE__*/_jsxDEV(AdminDashboardPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 200,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 199,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/admin/products\",\n                        element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                          requiredPermission: \"products\",\n                          children: /*#__PURE__*/_jsxDEV(AdminProductsPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 205,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/admin/categories\",\n                        element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                          requiredPermission: \"categories\",\n                          children: /*#__PURE__*/_jsxDEV(AdminCategoriesPage, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 210,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 209,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 13\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 60,\n                      columnNumber: 11\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 9\n                  }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n                    className: \"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"col-span-1 md:col-span-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                className: \"w-6 h-6 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  strokeWidth: 2,\n                                  d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 225,\n                                  columnNumber: 23\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 224,\n                                columnNumber: 21\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 223,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-2xl font-bold\",\n                              children: \"ShopHub\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 228,\n                              columnNumber: 19\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 222,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-gray-300 mb-4 max-w-md\",\n                            children: \"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 230,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [/*#__PURE__*/_jsxDEV(MultiLanguageSupport, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 235,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(EmailNotifications, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 236,\n                              columnNumber: 19\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 234,\n                            columnNumber: 17\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 221,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Quick Links\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 242,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"space-y-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Home\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 244,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 244,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/products\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Products\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 245,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 245,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/digital-products\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Digital Products\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 246,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 246,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/about\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"About Us\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 247,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 247,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/contact\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Contact\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 248,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 248,\n                              columnNumber: 19\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 243,\n                            columnNumber: 17\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 241,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Customer Service\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 254,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                            className: \"space-y-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/help\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Help Center\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 256,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 256,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/returns\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Returns\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 257,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 257,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/shipping\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Shipping Info\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 258,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 258,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                              children: /*#__PURE__*/_jsxDEV(Link, {\n                                to: \"/track\",\n                                className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                                children: \"Track Order\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 259,\n                                columnNumber: 23\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 259,\n                              columnNumber: 19\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 255,\n                            columnNumber: 17\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 15\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-400 text-sm\",\n                          children: \"\\xA9 2024 ShopHub. All rights reserved.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 265,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex space-x-6 mt-4 md:mt-0\",\n                          children: [/*#__PURE__*/_jsxDEV(Link, {\n                            to: \"/privacy\",\n                            className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                            children: \"Privacy Policy\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 269,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(Link, {\n                            to: \"/terms\",\n                            className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                            children: \"Terms of Service\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 270,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(Link, {\n                            to: \"/cookies\",\n                            className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                            children: \"Cookie Policy\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 271,\n                            columnNumber: 17\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 268,\n                          columnNumber: 15\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 13\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 11\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 9\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "ModernNavigation", "CartProvider", "UserProvider", "Admin<PERSON><PERSON><PERSON>", "ProductProvider", "ToastProvider", "ThemeProvider", "HomePage", "ProductsPage", "DigitalProductsPage", "PcGamingPage", "PcGamingComparePage", "PcGamingProductDetailPage", "PcBuilder", "AboutPage", "ContactPage", "CheckoutPage", "LoginPage", "RegisterPage", "ResetPasswordPage", "AccountPage", "WishlistPage", "AdminLoginPage", "AdminDashboardPage", "AdminProductsPage", "AdminCategoriesPage", "ProtectedRoute", "AdminProtectedRoute", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage", "MultiLanguageSupport", "EmailNotifications", "jsxDEV", "_jsxDEV", "App", "children", "className", "style", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "requireAuth", "requiredPermission", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ModernNavigation from './components/ModernNavigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport { AdminProvider } from './contexts/AdminContext';\nimport { ProductProvider } from './contexts/ProductContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport PcGamingPage from './pages/PcGamingPage';\nimport PcGamingComparePage from './pages/PcGamingComparePage';\nimport PcGamingProductDetailPage from './pages/PcGamingProductDetailPage';\nimport PcBuilder from './components/PcBuilder';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport AccountPage from './pages/AccountPage';\nimport WishlistPage from './pages/WishlistPage';\n\nimport AdminLoginPage from './pages/AdminLoginPage';\nimport AdminDashboardPage from './pages/AdminDashboardPage';\nimport AdminProductsPage from './pages/AdminProductsPage';\nimport AdminCategoriesPage from './pages/AdminCategoriesPage';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport AdminProtectedRoute from './components/AdminProtectedRoute';\nimport {\n  HelpPage,\n  ReturnsPage,\n  ShippingPage,\n  TrackOrderPage,\n  PrivacyPage,\n  TermsPage,\n  CookiesPage,\n  OrdersPage\n} from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <ProductProvider>\n        <AdminProvider>\n          <UserProvider>\n            <CartProvider>\n              <ToastProvider>\n                <Router>\n            <div className=\"min-h-screen theme-transition\" style={{ backgroundColor: 'var(--bg-primary)' }}>\n          <ModernNavigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/pc-gaming\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <PcGamingPage />\n              </motion.div>\n            } />\n            <Route path=\"/pc-builder\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <PcBuilder />\n              </motion.div>\n            } />\n            <Route path=\"/pc-gaming/compare\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <PcGamingComparePage />\n              </motion.div>\n            } />\n            <Route path=\"/pc-gaming/product/:id\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <PcGamingProductDetailPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n            <Route path=\"/checkout\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <CheckoutPage />\n              </motion.div>\n            } />\n            <Route path=\"/help\" element={<HelpPage />} />\n            <Route path=\"/returns\" element={<ReturnsPage />} />\n            <Route path=\"/shipping\" element={<ShippingPage />} />\n            <Route path=\"/track\" element={<TrackOrderPage />} />\n            <Route path=\"/orders\" element={<OrdersPage />} />\n            <Route path=\"/privacy\" element={<PrivacyPage />} />\n            <Route path=\"/terms\" element={<TermsPage />} />\n            <Route path=\"/cookies\" element={<CookiesPage />} />\n            <Route path=\"/login\" element={\n              <ProtectedRoute requireAuth={false}>\n                <LoginPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/register\" element={\n              <ProtectedRoute requireAuth={false}>\n                <RegisterPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/reset-password\" element={\n              <ProtectedRoute requireAuth={false}>\n                <ResetPasswordPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/account\" element={\n              <ProtectedRoute>\n                <AccountPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/wishlist\" element={\n              <ProtectedRoute>\n                <WishlistPage />\n              </ProtectedRoute>\n            } />\n\n\n            {/* Admin Routes */}\n            <Route path=\"/admin/login\" element={<AdminLoginPage />} />\n            <Route path=\"/admin/dashboard\" element={\n              <AdminProtectedRoute>\n                <AdminDashboardPage />\n              </AdminProtectedRoute>\n            } />\n            <Route path=\"/admin/products\" element={\n              <AdminProtectedRoute requiredPermission=\"products\">\n                <AdminProductsPage />\n              </AdminProtectedRoute>\n            } />\n            <Route path=\"/admin/categories\" element={\n              <AdminProtectedRoute requiredPermission=\"categories\">\n                <AdminCategoriesPage />\n              </AdminProtectedRoute>\n            } />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/help\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</Link></li>\n                  <li><Link to=\"/returns\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</Link></li>\n                  <li><Link to=\"/shipping\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</Link></li>\n                  <li><Link to=\"/track\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</Link>\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</Link>\n                <Link to=\"/cookies\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n        </div>\n              </Router>\n            </ToastProvider>\n          </CartProvider>\n        </UserProvider>\n      </AdminProvider>\n    </ProductProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,yBAAyB,MAAM,mCAAmC;AACzE,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAE/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SACEC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,QACL,yBAAyB;AAChC,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACjC,aAAa;IAAAmC,QAAA,eACZF,OAAA,CAACnC,eAAe;MAAAqC,QAAA,eACdF,OAAA,CAACpC,aAAa;QAAAsC,QAAA,eACZF,OAAA,CAACrC,YAAY;UAAAuC,QAAA,eACXF,OAAA,CAACtC,YAAY;YAAAwC,QAAA,eACXF,OAAA,CAAClC,aAAa;cAAAoC,QAAA,eACZF,OAAA,CAAC7C,MAAM;gBAAA+C,QAAA,eACXF,OAAA;kBAAKG,SAAS,EAAC,+BAA+B;kBAACC,KAAK,EAAE;oBAAEC,eAAe,EAAE;kBAAoB,CAAE;kBAAAH,QAAA,gBACjGF,OAAA,CAACvC,gBAAgB;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEtBT,OAAA,CAACxC,eAAe;oBAACkD,IAAI,EAAC,MAAM;oBAAAR,QAAA,eAC1BF,OAAA,CAAC5C,MAAM;sBAAA8C,QAAA,gBACLF,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,GAAG;wBAACC,OAAO,eACrBZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAChC,QAAQ;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,WAAW;wBAACC,OAAO,eAC7BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC/B,YAAY;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,mBAAmB;wBAACC,OAAO,eACrCZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC9B,mBAAmB;4BAAAoC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,YAAY;wBAACC,OAAO,eAC9BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC7B,YAAY;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,aAAa;wBAACC,OAAO,eAC/BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC1B,SAAS;4BAAAgC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,oBAAoB;wBAACC,OAAO,eACtCZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC5B,mBAAmB;4BAAAkC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,wBAAwB;wBAACC,OAAO,eAC1CZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAAC3B,yBAAyB;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,QAAQ;wBAACC,OAAO,eAC1BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAACzB,SAAS;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,UAAU;wBAACC,OAAO,eAC5BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAACxB,WAAW;4BAAA8B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,WAAW;wBAACC,OAAO,eAC7BZ,OAAA,CAACzC,MAAM,CAACsD,GAAG;0BACTC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAG,CAAE;0BAC/BC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BE,IAAI,EAAE;4BAAEH,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAC7BG,UAAU,EAAE;4BAAEC,QAAQ,EAAE;0BAAI,CAAE;0BAAAlB,QAAA,eAE9BF,OAAA,CAACvB,YAAY;4BAAA6B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,OAAO;wBAACC,OAAO,eAAEZ,OAAA,CAACX,QAAQ;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,UAAU;wBAACC,OAAO,eAAEZ,OAAA,CAACV,WAAW;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,WAAW;wBAACC,OAAO,eAAEZ,OAAA,CAACT,YAAY;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACrDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,QAAQ;wBAACC,OAAO,eAAEZ,OAAA,CAACR,cAAc;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,SAAS;wBAACC,OAAO,eAAEZ,OAAA,CAACJ,UAAU;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,UAAU;wBAACC,OAAO,eAAEZ,OAAA,CAACP,WAAW;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,QAAQ;wBAACC,OAAO,eAAEZ,OAAA,CAACN,SAAS;0BAAAY,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/CT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,UAAU;wBAACC,OAAO,eAAEZ,OAAA,CAACL,WAAW;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,QAAQ;wBAACC,OAAO,eAC1BZ,OAAA,CAACb,cAAc;0BAACkC,WAAW,EAAE,KAAM;0BAAAnB,QAAA,eACjCF,OAAA,CAACtB,SAAS;4BAAA4B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBACjB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,WAAW;wBAACC,OAAO,eAC7BZ,OAAA,CAACb,cAAc;0BAACkC,WAAW,EAAE,KAAM;0BAAAnB,QAAA,eACjCF,OAAA,CAACrB,YAAY;4BAAA2B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBACjB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,iBAAiB;wBAACC,OAAO,eACnCZ,OAAA,CAACb,cAAc;0BAACkC,WAAW,EAAE,KAAM;0BAAAnB,QAAA,eACjCF,OAAA,CAACpB,iBAAiB;4BAAA0B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP;sBACjB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,UAAU;wBAACC,OAAO,eAC5BZ,OAAA,CAACb,cAAc;0BAAAe,QAAA,eACbF,OAAA,CAACnB,WAAW;4BAAAyB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD;sBACjB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,WAAW;wBAACC,OAAO,eAC7BZ,OAAA,CAACb,cAAc;0BAAAe,QAAA,eACbF,OAAA,CAAClB,YAAY;4BAAAwB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBACjB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAIJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,cAAc;wBAACC,OAAO,eAAEZ,OAAA,CAACjB,cAAc;0BAAAuB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1DT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,kBAAkB;wBAACC,OAAO,eACpCZ,OAAA,CAACZ,mBAAmB;0BAAAc,QAAA,eAClBF,OAAA,CAAChB,kBAAkB;4BAAAsB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACtB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,iBAAiB;wBAACC,OAAO,eACnCZ,OAAA,CAACZ,mBAAmB;0BAACkC,kBAAkB,EAAC,UAAU;0BAAApB,QAAA,eAChDF,OAAA,CAACf,iBAAiB;4BAAAqB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBACtB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACJT,OAAA,CAAC3C,KAAK;wBAACsD,IAAI,EAAC,mBAAmB;wBAACC,OAAO,eACrCZ,OAAA,CAACZ,mBAAmB;0BAACkC,kBAAkB,EAAC,YAAY;0BAAApB,QAAA,eAClDF,OAAA,CAACd,mBAAmB;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBACtB;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM,CAAC,eAGlBT,OAAA;oBAAQG,SAAS,EAAC,uDAAuD;oBAAAD,QAAA,eACvEF,OAAA;sBAAKG,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,gBAC3DF,OAAA;wBAAKG,SAAS,EAAC,uCAAuC;wBAAAD,QAAA,gBAEpDF,OAAA;0BAAKG,SAAS,EAAC,0BAA0B;0BAAAD,QAAA,gBACvCF,OAAA;4BAAKG,SAAS,EAAC,kCAAkC;4BAAAD,QAAA,gBAC/CF,OAAA;8BAAKG,SAAS,EAAC,oHAAoH;8BAAAD,QAAA,eACjIF,OAAA;gCAAKG,SAAS,EAAC,oBAAoB;gCAACoB,IAAI,EAAC,MAAM;gCAACC,MAAM,EAAC,cAAc;gCAACC,OAAO,EAAC,WAAW;gCAAAvB,QAAA,eACvFF,OAAA;kCAAM0B,aAAa,EAAC,OAAO;kCAACC,cAAc,EAAC,OAAO;kCAACC,WAAW,EAAE,CAAE;kCAACC,CAAC,EAAC;gCAA4C;kCAAAvB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACjH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eACNT,OAAA;8BAAMG,SAAS,EAAC,oBAAoB;8BAAAD,QAAA,EAAC;4BAAO;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChD,CAAC,eACNT,OAAA;4BAAGG,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EAAC;0BAG3C;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACJT,OAAA;4BAAKG,SAAS,EAAC,gBAAgB;4BAAAD,QAAA,gBAC7BF,OAAA,CAACH,oBAAoB;8BAAAS,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACxBT,OAAA,CAACF,kBAAkB;8BAAAQ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGNT,OAAA;0BAAAE,QAAA,gBACEF,OAAA;4BAAIG,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,EAAC;0BAAW;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC3DT,OAAA;4BAAIG,SAAS,EAAC,WAAW;4BAAAD,QAAA,gBACvBF,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,GAAG;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAI;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzGT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,WAAW;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAQ;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACrHT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,mBAAmB;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAgB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACrIT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,QAAQ;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAQ;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClHT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,UAAU;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAO;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eAGNT,OAAA;0BAAAE,QAAA,gBACEF,OAAA;4BAAIG,SAAS,EAAC,4BAA4B;4BAAAD,QAAA,EAAC;0BAAgB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChET,OAAA;4BAAIG,SAAS,EAAC,WAAW;4BAAAD,QAAA,gBACvBF,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,OAAO;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAW;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACpHT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,UAAU;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAO;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnHT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,WAAW;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAa;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC1HT,OAAA;8BAAAE,QAAA,eAAIF,OAAA,CAAC1C,IAAI;gCAACwE,EAAE,EAAC,QAAQ;gCAAC3B,SAAS,EAAC,6DAA6D;gCAAAD,QAAA,EAAC;8BAAW;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENT,OAAA;wBAAKG,SAAS,EAAC,2FAA2F;wBAAAD,QAAA,gBACxGF,OAAA;0BAAGG,SAAS,EAAC,uBAAuB;0BAAAD,QAAA,EAAC;wBAErC;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACJT,OAAA;0BAAKG,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,gBAC1CF,OAAA,CAAC1C,IAAI;4BAACwE,EAAE,EAAC,UAAU;4BAAC3B,SAAS,EAAC,qEAAqE;4BAAAD,QAAA,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzHT,OAAA,CAAC1C,IAAI;4BAACwE,EAAE,EAAC,QAAQ;4BAAC3B,SAAS,EAAC,qEAAqE;4BAAAD,QAAA,EAAC;0BAAgB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzHT,OAAA,CAAC1C,IAAI;4BAACwE,EAAE,EAAC,UAAU;4BAAC3B,SAAS,EAAC,qEAAqE;4BAAAD,QAAA,EAAC;0BAAa;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEpB;AAACsB,EAAA,GA9OQ9B,GAAG;AAgPZ,eAAeA,GAAG;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}