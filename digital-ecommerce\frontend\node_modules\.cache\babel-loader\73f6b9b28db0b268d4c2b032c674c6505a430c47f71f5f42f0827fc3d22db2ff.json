{"ast": null, "code": "import * as React from \"react\";\nfunction VideoCameraIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(VideoCameraIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "VideoCameraIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction VideoCameraIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(VideoCameraIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAeA,CAAC;EACvBC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,cAAc;IACtB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAET,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DS,EAAE,EAAEZ;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDU,aAAa,EAAE,OAAO;IACtBC,cAAc,EAAE,OAAO;IACvBC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcnB,KAAK,CAACoB,UAAU,CAACnB,eAAe,CAAC;AAClE,eAAekB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}