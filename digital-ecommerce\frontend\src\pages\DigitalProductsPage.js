import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  ArrowDownTrayIcon as CloudDownloadIcon,
  ShieldCheckIcon,
  ClockIcon,
  StarIcon,
  ComputerDesktopIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { getDigitalProducts } from '../data/products';
import { useCart } from '../components/ShoppingCart';
import ProductPreviewModal from '../components/ProductPreviewModal';
import toast, { Toaster } from 'react-hot-toast';

const DigitalProductsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPlatform, setSelectedPlatform] = useState('all');
  const [previewProduct, setPreviewProduct] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const { addToCart } = useCart();

  const digitalProducts = getDigitalProducts();

  const handleAddToCart = (product) => {
    addToCart(product);
    toast.success(`${product.name} added to cart!`, {
      duration: 3000,
      position: 'top-right',
    });
  };

  const handleProductPreview = (product) => {
    setPreviewProduct(product);
    setIsPreviewOpen(true);
  };

  const closePreview = () => {
    setIsPreviewOpen(false);
    setPreviewProduct(null);
  };
  
  const digitalCategories = [
    { id: 'all', name: 'All Digital Products', icon: '💿' },
    { id: 'software', name: 'Software & Licenses', icon: '💻' },
    { id: 'gaming', name: 'Gaming', icon: '🎮' }
  ];

  const platforms = [
    { id: 'all', name: 'All Platforms' },
    { id: 'Windows', name: 'Windows' },
    { id: 'macOS', name: 'macOS' },
    { id: 'Steam', name: 'Steam' },
    { id: 'Xbox Console', name: 'Xbox' },
    { id: 'PlayStation', name: 'PlayStation' }
  ];

  const filteredProducts = digitalProducts.filter(product => {
    const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;
    const platformMatch = selectedPlatform === 'all' || 
      (product.platforms && product.platforms.includes(selectedPlatform)) ||
      product.platform === selectedPlatform;
    return categoryMatch && platformMatch;
  });

  const DigitalProductCard = ({ product }) => (
    <motion.div
      whileHover={{ y: -5 }}
      className="bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer"
    >
      <div className="relative">
        <img
          src={product.images[0]}
          alt={product.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 left-4">
          <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
            {product.badge || 'Digital'}
          </span>
        </div>
        <div className="absolute top-4 right-4">
          <span className="bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center">
            <ClockIcon className="w-3 h-3 mr-1" />
            Instant
          </span>
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
        
        <div className="flex items-center mb-3">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              i < Math.floor(product.rating) ? (
                <StarIconSolid key={i} className="w-4 h-4 text-yellow-400" />
              ) : (
                <StarIcon key={i} className="w-4 h-4 text-gray-300" />
              )
            ))}
          </div>
          <span className="text-sm text-gray-600 ml-2">
            {product.rating} ({product.reviews})
          </span>
        </div>

        <div className="flex items-center space-x-2 mb-4">
          <span className="text-2xl font-bold text-light-orange-600">
            ${product.price}
          </span>
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="text-lg text-gray-500 line-through">
              ${product.originalPrice}
            </span>
          )}
        </div>

        {/* Platform/License Info */}
        <div className="mb-4 space-y-2">
          {product.platforms && (
            <div className="flex items-center space-x-2">
              <ComputerDesktopIcon className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {product.platforms.join(', ')}
              </span>
            </div>
          )}
          {product.licenseType && (
            <div className="flex items-center space-x-2">
              <ShieldCheckIcon className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-600">{product.licenseType}</span>
            </div>
          )}
          {product.validityPeriod && (
            <div className="flex items-center space-x-2">
              <ClockIcon className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-blue-600">{product.validityPeriod}</span>
            </div>
          )}
        </div>

        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleAddToCart(product)}
            className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2"
          >
            <CloudDownloadIcon className="w-5 h-5" />
            <span>Get Instantly</span>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleProductPreview(product)}
            className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            title="Quick Preview"
          >
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </motion.button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Digital Products
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto mb-8">
              Instant access to software licenses, games, and digital content. 
              Download immediately after purchase with lifetime support.
            </p>
            
            {/* Key Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6">
                <CloudDownloadIcon className="w-8 h-8 text-white mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">Instant Delivery</h3>
                <p className="text-blue-100 text-sm">Get your license keys and download links immediately</p>
              </div>
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6">
                <ShieldCheckIcon className="w-8 h-8 text-white mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">100% Genuine</h3>
                <p className="text-blue-100 text-sm">All licenses are authentic and verified</p>
              </div>
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6">
                <CheckCircleIcon className="w-8 h-8 text-white mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">Lifetime Support</h3>
                <p className="text-blue-100 text-sm">Get help whenever you need it</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-4 justify-center">
            {digitalCategories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
              
              {/* Platform Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Platform</h4>
                <div className="space-y-2">
                  {platforms.map(platform => (
                    <button
                      key={platform.id}
                      onClick={() => setSelectedPlatform(platform.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        selectedPlatform === platform.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {platform.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Digital Product Info */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <InformationCircleIcon className="w-5 h-5 text-blue-600 mr-2" />
                  <h4 className="font-medium text-blue-900">Digital Delivery</h4>
                </div>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Instant email delivery</li>
                  <li>• No shipping required</li>
                  <li>• 24/7 download access</li>
                  <li>• Secure activation</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            <div className="mb-6">
              <p className="text-gray-600">
                Showing {filteredProducts.length} digital products
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
              {filteredProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <DigitalProductCard product={product} />
                </motion.div>
              ))}
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <ComputerDesktopIcon className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No digital products found</h3>
                <p className="text-gray-600">Try adjusting your filters to see more results.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Need Help Choosing?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Our experts are here to help you find the perfect software solution
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </div>

      {/* Product Preview Modal */}
      <ProductPreviewModal
        product={previewProduct}
        isOpen={isPreviewOpen}
        onClose={closePreview}
      />
    </div>
  );
};

export default DigitalProductsPage;
