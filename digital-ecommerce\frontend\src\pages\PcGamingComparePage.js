import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeftIcon,
  ShoppingCartIcon,
  HeartIcon,
  CheckIcon,
  XMarkIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';
import { useCart } from '../components/ShoppingCart';

const PcGamingComparePage = () => {
  const [searchParams] = useSearchParams();
  const { getProductById } = useProducts();
  const { showSuccess } = useToast();
  const { addToCart } = useCart();
  const [products, setProducts] = useState([]);

  useEffect(() => {
    const productIds = searchParams.get('products')?.split(',') || [];
    const loadedProducts = productIds.map(id => getProductById(id)).filter(Boolean);
    setProducts(loadedProducts);
  }, [searchParams, getProductById]);

  const handleAddToCart = (product) => {
    addToCart(product);
    showSuccess('Added to Cart', `${product.name} added to cart.`);
  };

  const addToWishlist = (product) => {
    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);
  };

  if (products.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">No Products to Compare</h1>
          <Link
            to="/pc-gaming"
            className="inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5" />
            <span>Back to PC Gaming</span>
          </Link>
        </div>
      </div>
    );
  }

  const getSpecificationKeys = () => {
    const allKeys = new Set();
    products.forEach(product => {
      if (product.specifications) {
        Object.keys(product.specifications).forEach(key => allKeys.add(key));
      }
    });
    return Array.from(allKeys);
  };

  const specKeys = getSpecificationKeys();

  return (
    <div className="min-h-screen bg-gradient-to-br from-light-orange-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                to="/pc-gaming"
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
                <span>Back to PC Gaming</span>
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">
                Compare Products ({products.length})
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Comparison Table */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Product Headers */}
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left p-6 w-48 bg-gray-50">
                    <span className="text-sm font-medium text-gray-500">PRODUCT</span>
                  </th>
                  {products.map(product => (
                    <th key={product.id} className="p-6 text-center min-w-80">
                      <div className="space-y-4">
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="w-32 h-32 object-cover rounded-lg mx-auto"
                        />
                        <div>
                          <h3 className="font-semibold text-lg text-gray-900 mb-2">
                            {product.name}
                          </h3>
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="text-sm text-gray-600 ml-1">({product.reviews})</span>
                            </div>
                          </div>
                          <div className="text-center mb-4">
                            <span className="text-2xl font-bold text-light-orange-600">
                              ${product.price.toLocaleString()}
                            </span>
                            {product.originalPrice && product.originalPrice > product.price && (
                              <span className="text-sm text-gray-500 line-through ml-2">
                                ${product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleAddToCart(product)}
                              disabled={!product.inStock}
                              className={`flex-1 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 ${
                                product.inStock
                                  ? 'bg-light-orange-500 text-white hover:bg-light-orange-600'
                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              }`}
                            >
                              <ShoppingCartIcon className="w-4 h-4" />
                              <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                            </button>
                            <button
                              onClick={() => addToWishlist(product)}
                              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                              <HeartIcon className="w-4 h-4 text-gray-600" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>

              {/* Specifications */}
              <tbody>
                {/* Basic Info */}
                <tr className="border-b border-gray-100">
                  <td className="p-4 bg-gray-50 font-medium text-gray-700">Description</td>
                  {products.map(product => (
                    <td key={product.id} className="p-4 text-sm text-gray-600">
                      {product.description}
                    </td>
                  ))}
                </tr>

                <tr className="border-b border-gray-100">
                  <td className="p-4 bg-gray-50 font-medium text-gray-700">Category</td>
                  {products.map(product => (
                    <td key={product.id} className="p-4 text-sm text-gray-600">
                      {product.subcategory?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </td>
                  ))}
                </tr>

                <tr className="border-b border-gray-100">
                  <td className="p-4 bg-gray-50 font-medium text-gray-700">In Stock</td>
                  {products.map(product => (
                    <td key={product.id} className="p-4">
                      {product.inStock ? (
                        <div className="flex items-center space-x-2 text-green-600">
                          <CheckIcon className="w-4 h-4" />
                          <span className="text-sm font-medium">In Stock</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2 text-red-600">
                          <XMarkIcon className="w-4 h-4" />
                          <span className="text-sm font-medium">Out of Stock</span>
                        </div>
                      )}
                    </td>
                  ))}
                </tr>

                {/* Specifications */}
                {specKeys.map(specKey => (
                  <tr key={specKey} className="border-b border-gray-100">
                    <td className="p-4 bg-gray-50 font-medium text-gray-700">
                      {specKey}
                    </td>
                    {products.map(product => (
                      <td key={product.id} className="p-4 text-sm text-gray-600">
                        {product.specifications?.[specKey] || '-'}
                      </td>
                    ))}
                  </tr>
                ))}

                {/* Performance (if available) */}
                {products.some(p => p.performance) && (
                  <>
                    <tr className="bg-gray-50">
                      <td colSpan={products.length + 1} className="p-4 font-semibold text-gray-900">
                        Performance
                      </td>
                    </tr>
                    {['1080p_ultra', '1440p_ultra', '4k_high', 'rayTracing'].map(perfKey => (
                      <tr key={perfKey} className="border-b border-gray-100">
                        <td className="p-4 bg-gray-50 font-medium text-gray-700">
                          {perfKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </td>
                        {products.map(product => (
                          <td key={product.id} className="p-4 text-sm text-gray-600">
                            {product.performance?.[perfKey] || '-'}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </>
                )}

                {/* Features */}
                {products.some(p => p.features) && (
                  <>
                    <tr className="bg-gray-50">
                      <td colSpan={products.length + 1} className="p-4 font-semibold text-gray-900">
                        Features
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="p-4 bg-gray-50 font-medium text-gray-700">Key Features</td>
                      {products.map(product => (
                        <td key={product.id} className="p-4">
                          {product.features ? (
                            <ul className="text-sm text-gray-600 space-y-1">
                              {product.features.slice(0, 5).map((feature, index) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckIcon className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span>{feature}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      ))}
                    </tr>
                  </>
                )}

                {/* Warranty */}
                <tr className="border-b border-gray-100">
                  <td className="p-4 bg-gray-50 font-medium text-gray-700">Warranty</td>
                  {products.map(product => (
                    <td key={product.id} className="p-4 text-sm text-gray-600">
                      {product.warranty || '-'}
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 text-center">
          <Link
            to="/pc-gaming"
            className="inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
          >
            <span>Continue Shopping</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PcGamingComparePage;
