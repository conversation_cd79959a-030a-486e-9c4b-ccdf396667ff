{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = ({\n  className = '',\n  showLabel = false\n}) => {\n  _s();\n  const [theme, setTheme] = useState('light');\n  const [isHovered, setIsHovered] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setTheme(savedTheme);\n      applyTheme(savedTheme);\n    } else {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      setTheme(systemTheme);\n      applyTheme(systemTheme);\n    }\n  }, []);\n  const applyTheme = newTheme => {\n    document.documentElement.setAttribute('data-theme', newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    applyTheme(newTheme);\n\n    // Gaming feedback effect\n    if (navigator.vibrate) {\n      navigator.vibrate(50);\n    }\n  };\n\n  // Gaming-inspired SVG icons\n  const SunIcon = () => /*#__PURE__*/_jsxDEV(motion.svg, {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    initial: {\n      rotate: 0,\n      scale: 1\n    },\n    animate: {\n      rotate: theme === 'light' ? 0 : -90,\n      scale: theme === 'light' ? 1 : 0.8\n    },\n    transition: {\n      duration: 0.3,\n      ease: \"easeInOut\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n      cx: \"12\",\n      cy: \"12\",\n      r: \"4\",\n      fill: \"currentColor\",\n      className: \"drop-shadow-[0_0_8px_rgba(255,179,102,0.6)]\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), [0, 45, 90, 135, 180, 225, 270, 315].map((angle, index) => /*#__PURE__*/_jsxDEV(motion.line, {\n      x1: \"12\",\n      y1: \"2\",\n      x2: \"12\",\n      y2: \"4\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      transform: `rotate(${angle} 12 12)`,\n      initial: {\n        opacity: 0.6,\n        scale: 1\n      },\n      animate: {\n        opacity: theme === 'light' ? [0.6, 1, 0.6] : 0.3,\n        scale: theme === 'light' ? [1, 1.2, 1] : 0.8\n      },\n      transition: {\n        duration: 2,\n        repeat: theme === 'light' ? Infinity : 0,\n        delay: index * 0.1\n      }\n    }, angle, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n  const MoonIcon = () => /*#__PURE__*/_jsxDEV(motion.svg, {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    initial: {\n      rotate: 0,\n      scale: 1\n    },\n    animate: {\n      rotate: theme === 'dark' ? 0 : 90,\n      scale: theme === 'dark' ? 1 : 0.8\n    },\n    transition: {\n      duration: 0.3,\n      ease: \"easeInOut\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(motion.path, {\n      d: \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\",\n      fill: \"currentColor\",\n      className: \"drop-shadow-[0_0_8px_rgba(0,212,255,0.6)]\",\n      initial: {\n        pathLength: 0\n      },\n      animate: {\n        pathLength: 1\n      },\n      transition: {\n        duration: 0.5,\n        ease: \"easeInOut\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), [{\n      x: 6,\n      y: 6,\n      delay: 0\n    }, {\n      x: 8,\n      y: 4,\n      delay: 0.2\n    }, {\n      x: 4,\n      y: 8,\n      delay: 0.4\n    }].map((star, index) => /*#__PURE__*/_jsxDEV(motion.circle, {\n      cx: star.x,\n      cy: star.y,\n      r: \"1\",\n      fill: \"currentColor\",\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: theme === 'dark' ? [0, 1, 0] : 0,\n        scale: theme === 'dark' ? [0, 1, 0] : 0\n      },\n      transition: {\n        duration: 1.5,\n        repeat: theme === 'dark' ? Infinity : 0,\n        delay: star.delay\n      }\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n  const getThemeLabel = () => {\n    return theme === 'light' ? 'Light' : 'Dark';\n  };\n  const getTooltipText = () => {\n    const nextTheme = theme === 'light' ? 'dark' : 'light';\n    return `Switch to ${nextTheme} mode`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      onClick: toggleTheme,\n      onMouseEnter: () => {\n        setIsHovered(true);\n        setShowTooltip(true);\n      },\n      onMouseLeave: () => {\n        setIsHovered(false);\n        setShowTooltip(false);\n      },\n      onFocus: () => setShowTooltip(true),\n      onBlur: () => setShowTooltip(false),\n      className: `\n          relative flex items-center justify-center\n          w-10 h-10 rounded-lg\n          bg-gray-100 dark:bg-gray-800\n          border border-gray-200 dark:border-gray-700\n          hover:border-orange-300 dark:hover:border-orange-500\n          focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2\n          dark:focus:ring-offset-gray-900\n          transition-all duration-300 ease-out\n          group gaming-glow\n          ${className}\n        `,\n      style: {\n        backgroundColor: 'var(--bg-elevated)',\n        borderColor: 'var(--border-primary)',\n        color: 'var(--text-primary)'\n      },\n      whileHover: {\n        scale: 1.05,\n        boxShadow: 'var(--rgb-glow-strong)'\n      },\n      whileTap: {\n        scale: 0.95,\n        transition: {\n          duration: 0.1\n        }\n      },\n      \"aria-label\": `Toggle theme. Current: ${getThemeLabel()}`,\n      \"aria-checked\": theme === 'dark',\n      role: \"switch\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n        style: {\n          background: theme === 'dark' ? 'linear-gradient(135deg, rgba(0,212,255,0.1), rgba(168,85,247,0.1))' : 'linear-gradient(135deg, rgba(255,179,102,0.1), rgba(0,255,136,0.1))'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: theme === 'light' ? /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              rotate: -180\n            },\n            animate: {\n              opacity: 1,\n              rotate: 0\n            },\n            exit: {\n              opacity: 0,\n              rotate: 180\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(SunIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)\n          }, \"sun\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              rotate: -180\n            },\n            animate: {\n              opacity: 1,\n              rotate: 0\n            },\n            exit: {\n              opacity: 0,\n              rotate: 180\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(MoonIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)\n          }, \"moon\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 rounded-lg border-2 border-transparent\",\n        style: {\n          background: isHovered ? 'linear-gradient(45deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary), var(--accent-primary)) border-box' : 'none',\n          mask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',\n          maskComposite: 'exclude'\n        },\n        animate: {\n          opacity: isHovered ? 1 : 0\n        },\n        transition: {\n          duration: 0.3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), showLabel && /*#__PURE__*/_jsxDEV(motion.span, {\n      className: \"ml-2 text-sm font-medium\",\n      style: {\n        color: 'var(--text-secondary)'\n      },\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: getThemeLabel()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showTooltip && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 10,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          y: 10,\n          scale: 0.9\n        },\n        transition: {\n          duration: 0.2,\n          ease: \"easeOut\"\n        },\n        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-3 py-2 text-xs font-medium rounded-lg shadow-lg border\",\n          style: {\n            backgroundColor: 'var(--bg-elevated)',\n            borderColor: 'var(--border-secondary)',\n            color: 'var(--text-primary)',\n            boxShadow: 'var(--shadow-lg), var(--rgb-glow)'\n          },\n          children: [getTooltipText(), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\",\n            style: {\n              borderLeft: '4px solid transparent',\n              borderRight: '4px solid transparent',\n              borderTop: '4px solid var(--border-secondary)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sr-only\",\n      children: \"Press Ctrl+Shift+T to toggle theme\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"avIjbG3ufGD3y3q/H6tRsTStvXk=\");\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "SunIcon", "MoonIcon", "jsxDEV", "_jsxDEV", "ThemeToggle", "className", "showLabel", "_s", "theme", "setTheme", "isHovered", "setIsHovered", "showTooltip", "setShowTooltip", "savedTheme", "localStorage", "getItem", "applyTheme", "systemTheme", "window", "matchMedia", "matches", "newTheme", "document", "documentElement", "setAttribute", "setItem", "toggleTheme", "navigator", "vibrate", "svg", "width", "height", "viewBox", "fill", "xmlns", "initial", "rotate", "scale", "animate", "transition", "duration", "ease", "children", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "angle", "index", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "strokeLinecap", "transform", "opacity", "repeat", "Infinity", "delay", "path", "d", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "star", "circle", "getThemeLabel", "getTooltipText", "nextTheme", "button", "onClick", "onMouseEnter", "onMouseLeave", "onFocus", "onBlur", "style", "backgroundColor", "borderColor", "color", "whileHover", "boxShadow", "whileTap", "role", "div", "background", "mode", "exit", "mask", "maskComposite", "span", "borderLeft", "borderRight", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ThemeToggle.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\n\nconst ThemeToggle = ({ className = '', showLabel = false }) => {\n  const [theme, setTheme] = useState('light');\n  const [isHovered, setIsHovered] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setTheme(savedTheme);\n      applyTheme(savedTheme);\n    } else {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      setTheme(systemTheme);\n      applyTheme(systemTheme);\n    }\n  }, []);\n\n  const applyTheme = (newTheme) => {\n    document.documentElement.setAttribute('data-theme', newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    applyTheme(newTheme);\n\n    // Gaming feedback effect\n    if (navigator.vibrate) {\n      navigator.vibrate(50);\n    }\n  };\n\n  // Gaming-inspired SVG icons\n  const SunIcon = () => (\n    <motion.svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      initial={{ rotate: 0, scale: 1 }}\n      animate={{ \n        rotate: theme === 'light' ? 0 : -90,\n        scale: theme === 'light' ? 1 : 0.8\n      }}\n      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n    >\n      {/* Gaming-style sun with RGB glow effect */}\n      <circle\n        cx=\"12\"\n        cy=\"12\"\n        r=\"4\"\n        fill=\"currentColor\"\n        className=\"drop-shadow-[0_0_8px_rgba(255,179,102,0.6)]\"\n      />\n      {/* Animated rays */}\n      {[0, 45, 90, 135, 180, 225, 270, 315].map((angle, index) => (\n        <motion.line\n          key={angle}\n          x1=\"12\"\n          y1=\"2\"\n          x2=\"12\"\n          y2=\"4\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          transform={`rotate(${angle} 12 12)`}\n          initial={{ opacity: 0.6, scale: 1 }}\n          animate={{ \n            opacity: theme === 'light' ? [0.6, 1, 0.6] : 0.3,\n            scale: theme === 'light' ? [1, 1.2, 1] : 0.8\n          }}\n          transition={{ \n            duration: 2,\n            repeat: theme === 'light' ? Infinity : 0,\n            delay: index * 0.1\n          }}\n        />\n      ))}\n    </motion.svg>\n  );\n\n  const MoonIcon = () => (\n    <motion.svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      initial={{ rotate: 0, scale: 1 }}\n      animate={{ \n        rotate: theme === 'dark' ? 0 : 90,\n        scale: theme === 'dark' ? 1 : 0.8\n      }}\n      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n    >\n      {/* Gaming-style moon with cyber glow */}\n      <motion.path\n        d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n        fill=\"currentColor\"\n        className=\"drop-shadow-[0_0_8px_rgba(0,212,255,0.6)]\"\n        initial={{ pathLength: 0 }}\n        animate={{ pathLength: 1 }}\n        transition={{ duration: 0.5, ease: \"easeInOut\" }}\n      />\n      {/* Gaming stars */}\n      {[\n        { x: 6, y: 6, delay: 0 },\n        { x: 8, y: 4, delay: 0.2 },\n        { x: 4, y: 8, delay: 0.4 }\n      ].map((star, index) => (\n        <motion.circle\n          key={index}\n          cx={star.x}\n          cy={star.y}\n          r=\"1\"\n          fill=\"currentColor\"\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ \n            opacity: theme === 'dark' ? [0, 1, 0] : 0,\n            scale: theme === 'dark' ? [0, 1, 0] : 0\n          }}\n          transition={{ \n            duration: 1.5,\n            repeat: theme === 'dark' ? Infinity : 0,\n            delay: star.delay\n          }}\n        />\n      ))}\n    </motion.svg>\n  );\n\n\n\n  const getThemeLabel = () => {\n    return theme === 'light' ? 'Light' : 'Dark';\n  };\n\n  const getTooltipText = () => {\n    const nextTheme = theme === 'light' ? 'dark' : 'light';\n    return `Switch to ${nextTheme} mode`;\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Main Toggle Button */}\n      <motion.button\n        onClick={toggleTheme}\n        onMouseEnter={() => {\n          setIsHovered(true);\n          setShowTooltip(true);\n        }}\n        onMouseLeave={() => {\n          setIsHovered(false);\n          setShowTooltip(false);\n        }}\n        onFocus={() => setShowTooltip(true)}\n        onBlur={() => setShowTooltip(false)}\n        className={`\n          relative flex items-center justify-center\n          w-10 h-10 rounded-lg\n          bg-gray-100 dark:bg-gray-800\n          border border-gray-200 dark:border-gray-700\n          hover:border-orange-300 dark:hover:border-orange-500\n          focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2\n          dark:focus:ring-offset-gray-900\n          transition-all duration-300 ease-out\n          group gaming-glow\n          ${className}\n        `}\n        style={{\n          backgroundColor: 'var(--bg-elevated)',\n          borderColor: 'var(--border-primary)',\n          color: 'var(--text-primary)'\n        }}\n        whileHover={{ \n          scale: 1.05,\n          boxShadow: 'var(--rgb-glow-strong)'\n        }}\n        whileTap={{ \n          scale: 0.95,\n          transition: { duration: 0.1 }\n        }}\n        aria-label={`Toggle theme. Current: ${getThemeLabel()}`}\n        aria-checked={theme === 'dark'}\n        role=\"switch\"\n      >\n        {/* Background gradient effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n          style={{\n            background: theme === 'dark' \n              ? 'linear-gradient(135deg, rgba(0,212,255,0.1), rgba(168,85,247,0.1))'\n              : 'linear-gradient(135deg, rgba(255,179,102,0.1), rgba(0,255,136,0.1))'\n          }}\n        />\n\n        {/* Icon Container */}\n        <div className=\"relative z-10 flex items-center justify-center\">\n          <AnimatePresence mode=\"wait\">\n            {theme === 'light' ? (\n              <motion.div\n                key=\"sun\"\n                initial={{ opacity: 0, rotate: -180 }}\n                animate={{ opacity: 1, rotate: 0 }}\n                exit={{ opacity: 0, rotate: 180 }}\n                transition={{ duration: 0.3 }}\n              >\n                <SunIcon className=\"w-5 h-5\" />\n              </motion.div>\n            ) : (\n              <motion.div\n                key=\"moon\"\n                initial={{ opacity: 0, rotate: -180 }}\n                animate={{ opacity: 1, rotate: 0 }}\n                exit={{ opacity: 0, rotate: 180 }}\n                transition={{ duration: 0.3 }}\n              >\n                <MoonIcon className=\"w-5 h-5\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Gaming RGB border effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-lg border-2 border-transparent\"\n          style={{\n            background: isHovered \n              ? 'linear-gradient(45deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary), var(--accent-primary)) border-box'\n              : 'none',\n            mask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',\n            maskComposite: 'exclude'\n          }}\n          animate={{\n            opacity: isHovered ? 1 : 0\n          }}\n          transition={{ duration: 0.3 }}\n        />\n      </motion.button>\n\n      {/* Label (optional) */}\n      {showLabel && (\n        <motion.span\n          className=\"ml-2 text-sm font-medium\"\n          style={{ color: 'var(--text-secondary)' }}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.1 }}\n        >\n          {getThemeLabel()}\n        </motion.span>\n      )}\n\n      {/* Gaming-style Tooltip */}\n      <AnimatePresence>\n        {showTooltip && (\n          <motion.div\n            initial={{ opacity: 0, y: 10, scale: 0.9 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: 10, scale: 0.9 }}\n            transition={{ duration: 0.2, ease: \"easeOut\" }}\n            className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\"\n          >\n            <div\n              className=\"px-3 py-2 text-xs font-medium rounded-lg shadow-lg border\"\n              style={{\n                backgroundColor: 'var(--bg-elevated)',\n                borderColor: 'var(--border-secondary)',\n                color: 'var(--text-primary)',\n                boxShadow: 'var(--shadow-lg), var(--rgb-glow)'\n              }}\n            >\n              {getTooltipText()}\n              \n              {/* Gaming arrow */}\n              <div\n                className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n                style={{\n                  borderLeft: '4px solid transparent',\n                  borderRight: '4px solid transparent',\n                  borderTop: '4px solid var(--border-secondary)'\n                }}\n              />\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Keyboard shortcut hint */}\n      <div className=\"sr-only\">\n        Press Ctrl+Shift+T to toggle theme\n      </div>\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,EAAEC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACdL,QAAQ,CAACK,UAAU,CAAC;MACpBG,UAAU,CAACH,UAAU,CAAC;IACxB,CAAC,MAAM;MACL,MAAMI,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,GAAG,MAAM,GAAG,OAAO;MAChGZ,QAAQ,CAACS,WAAW,CAAC;MACrBD,UAAU,CAACC,WAAW,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAIK,QAAQ,IAAK;IAC/BC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAEH,QAAQ,CAAC;IAC7DP,YAAY,CAACW,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAAC;EACzC,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAML,QAAQ,GAAGd,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACrDC,QAAQ,CAACa,QAAQ,CAAC;IAClBL,UAAU,CAACK,QAAQ,CAAC;;IAEpB;IACA,IAAIM,SAAS,CAACC,OAAO,EAAE;MACrBD,SAAS,CAACC,OAAO,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM7B,OAAO,GAAGA,CAAA,kBACdG,OAAA,CAACL,MAAM,CAACgC,GAAG;IACTC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,OAAO,EAAC,WAAW;IACnBC,IAAI,EAAC,MAAM;IACXC,KAAK,EAAC,4BAA4B;IAClCC,OAAO,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IACjCC,OAAO,EAAE;MACPF,MAAM,EAAE7B,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;MACnC8B,KAAK,EAAE9B,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG;IACjC,CAAE;IACFgC,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAY,CAAE;IAAAC,QAAA,gBAGjDxC,OAAA;MACEyC,EAAE,EAAC,IAAI;MACPC,EAAE,EAAC,IAAI;MACPC,CAAC,EAAC,GAAG;MACLZ,IAAI,EAAC,cAAc;MACnB7B,SAAS,EAAC;IAA6C;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,EAED,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACrDlD,OAAA,CAACL,MAAM,CAACwD,IAAI;MAEVC,EAAE,EAAC,IAAI;MACPC,EAAE,EAAC,GAAG;MACNC,EAAE,EAAC,IAAI;MACPC,EAAE,EAAC,GAAG;MACNC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MACfC,aAAa,EAAC,OAAO;MACrBC,SAAS,EAAE,UAAUV,KAAK,SAAU;MACpChB,OAAO,EAAE;QAAE2B,OAAO,EAAE,GAAG;QAAEzB,KAAK,EAAE;MAAE,CAAE;MACpCC,OAAO,EAAE;QACPwB,OAAO,EAAEvD,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;QAChD8B,KAAK,EAAE9B,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG;MAC3C,CAAE;MACFgC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXuB,MAAM,EAAExD,KAAK,KAAK,OAAO,GAAGyD,QAAQ,GAAG,CAAC;QACxCC,KAAK,EAAEb,KAAK,GAAG;MACjB;IAAE,GAlBGD,KAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBX,CACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CACb;EAED,MAAMjD,QAAQ,GAAGA,CAAA,kBACfE,OAAA,CAACL,MAAM,CAACgC,GAAG;IACTC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,OAAO,EAAC,WAAW;IACnBC,IAAI,EAAC,MAAM;IACXC,KAAK,EAAC,4BAA4B;IAClCC,OAAO,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IACjCC,OAAO,EAAE;MACPF,MAAM,EAAE7B,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE;MACjC8B,KAAK,EAAE9B,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG;IAChC,CAAE;IACFgC,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAY,CAAE;IAAAC,QAAA,gBAGjDxC,OAAA,CAACL,MAAM,CAACqE,IAAI;MACVC,CAAC,EAAC,iDAAiD;MACnDlC,IAAI,EAAC,cAAc;MACnB7B,SAAS,EAAC,2CAA2C;MACrD+B,OAAO,EAAE;QAAEiC,UAAU,EAAE;MAAE,CAAE;MAC3B9B,OAAO,EAAE;QAAE8B,UAAU,EAAE;MAAE,CAAE;MAC3B7B,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAY;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAED,CACC;MAAEoB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEL,KAAK,EAAE;IAAE,CAAC,EACxB;MAAEI,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEL,KAAK,EAAE;IAAI,CAAC,EAC1B;MAAEI,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEL,KAAK,EAAE;IAAI,CAAC,CAC3B,CAACf,GAAG,CAAC,CAACqB,IAAI,EAAEnB,KAAK,kBAChBlD,OAAA,CAACL,MAAM,CAAC2E,MAAM;MAEZ7B,EAAE,EAAE4B,IAAI,CAACF,CAAE;MACXzB,EAAE,EAAE2B,IAAI,CAACD,CAAE;MACXzB,CAAC,EAAC,GAAG;MACLZ,IAAI,EAAC,cAAc;MACnBE,OAAO,EAAE;QAAE2B,OAAO,EAAE,CAAC;QAAEzB,KAAK,EAAE;MAAE,CAAE;MAClCC,OAAO,EAAE;QACPwB,OAAO,EAAEvD,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QACzC8B,KAAK,EAAE9B,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG;MACxC,CAAE;MACFgC,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbuB,MAAM,EAAExD,KAAK,KAAK,MAAM,GAAGyD,QAAQ,GAAG,CAAC;QACvCC,KAAK,EAAEM,IAAI,CAACN;MACd;IAAE,GAdGb,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeX,CACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CACb;EAID,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOlE,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;EAC7C,CAAC;EAED,MAAMmE,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,SAAS,GAAGpE,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACtD,OAAO,aAAaoE,SAAS,OAAO;EACtC,CAAC;EAED,oBACEzE,OAAA;IAAKE,SAAS,EAAC,UAAU;IAAAsC,QAAA,gBAEvBxC,OAAA,CAACL,MAAM,CAAC+E,MAAM;MACZC,OAAO,EAAEnD,WAAY;MACrBoD,YAAY,EAAEA,CAAA,KAAM;QAClBpE,YAAY,CAAC,IAAI,CAAC;QAClBE,cAAc,CAAC,IAAI,CAAC;MACtB,CAAE;MACFmE,YAAY,EAAEA,CAAA,KAAM;QAClBrE,YAAY,CAAC,KAAK,CAAC;QACnBE,cAAc,CAAC,KAAK,CAAC;MACvB,CAAE;MACFoE,OAAO,EAAEA,CAAA,KAAMpE,cAAc,CAAC,IAAI,CAAE;MACpCqE,MAAM,EAAEA,CAAA,KAAMrE,cAAc,CAAC,KAAK,CAAE;MACpCR,SAAS,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYA,SAAS;AACrB,SAAU;MACF8E,KAAK,EAAE;QACLC,eAAe,EAAE,oBAAoB;QACrCC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE;MACT,CAAE;MACFC,UAAU,EAAE;QACVjD,KAAK,EAAE,IAAI;QACXkD,SAAS,EAAE;MACb,CAAE;MACFC,QAAQ,EAAE;QACRnD,KAAK,EAAE,IAAI;QACXE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAC9B,CAAE;MACF,cAAY,0BAA0BiC,aAAa,CAAC,CAAC,EAAG;MACxD,gBAAclE,KAAK,KAAK,MAAO;MAC/BkF,IAAI,EAAC,QAAQ;MAAA/C,QAAA,gBAGbxC,OAAA,CAACL,MAAM,CAAC6F,GAAG;QACTtF,SAAS,EAAC,+FAA+F;QACzG8E,KAAK,EAAE;UACLS,UAAU,EAAEpF,KAAK,KAAK,MAAM,GACxB,oEAAoE,GACpE;QACN;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF/C,OAAA;QAAKE,SAAS,EAAC,gDAAgD;QAAAsC,QAAA,eAC7DxC,OAAA,CAACJ,eAAe;UAAC8F,IAAI,EAAC,MAAM;UAAAlD,QAAA,EACzBnC,KAAK,KAAK,OAAO,gBAChBL,OAAA,CAACL,MAAM,CAAC6F,GAAG;YAETvD,OAAO,EAAE;cAAE2B,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE,CAAC;YAAI,CAAE;YACtCE,OAAO,EAAE;cAAEwB,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE;YAAE,CAAE;YACnCyD,IAAI,EAAE;cAAE/B,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE;YAAI,CAAE;YAClCG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAE,QAAA,eAE9BxC,OAAA,CAACH,OAAO;cAACK,SAAS,EAAC;YAAS;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAN3B,KAAK;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOC,CAAC,gBAEb/C,OAAA,CAACL,MAAM,CAAC6F,GAAG;YAETvD,OAAO,EAAE;cAAE2B,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE,CAAC;YAAI,CAAE;YACtCE,OAAO,EAAE;cAAEwB,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE;YAAE,CAAE;YACnCyD,IAAI,EAAE;cAAE/B,OAAO,EAAE,CAAC;cAAE1B,MAAM,EAAE;YAAI,CAAE;YAClCG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAE,QAAA,eAE9BxC,OAAA,CAACF,QAAQ;cAACI,SAAS,EAAC;YAAS;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAN5B,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOA;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGN/C,OAAA,CAACL,MAAM,CAAC6F,GAAG;QACTtF,SAAS,EAAC,yDAAyD;QACnE8E,KAAK,EAAE;UACLS,UAAU,EAAElF,SAAS,GACjB,kIAAkI,GAClI,MAAM;UACVqF,IAAI,EAAE,kEAAkE;UACxEC,aAAa,EAAE;QACjB,CAAE;QACFzD,OAAO,EAAE;UACPwB,OAAO,EAAErD,SAAS,GAAG,CAAC,GAAG;QAC3B,CAAE;QACF8B,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAGf5C,SAAS,iBACRH,OAAA,CAACL,MAAM,CAACmG,IAAI;MACV5F,SAAS,EAAC,0BAA0B;MACpC8E,KAAK,EAAE;QAAEG,KAAK,EAAE;MAAwB,CAAE;MAC1ClD,OAAO,EAAE;QAAE2B,OAAO,EAAE;MAAE,CAAE;MACxBxB,OAAO,EAAE;QAAEwB,OAAO,EAAE;MAAE,CAAE;MACxBvB,UAAU,EAAE;QAAE0B,KAAK,EAAE;MAAI,CAAE;MAAAvB,QAAA,EAE1B+B,aAAa,CAAC;IAAC;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACd,eAGD/C,OAAA,CAACJ,eAAe;MAAA4C,QAAA,EACb/B,WAAW,iBACVT,OAAA,CAACL,MAAM,CAAC6F,GAAG;QACTvD,OAAO,EAAE;UAAE2B,OAAO,EAAE,CAAC;UAAEQ,CAAC,EAAE,EAAE;UAAEjC,KAAK,EAAE;QAAI,CAAE;QAC3CC,OAAO,EAAE;UAAEwB,OAAO,EAAE,CAAC;UAAEQ,CAAC,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAE,CAAE;QACxCwD,IAAI,EAAE;UAAE/B,OAAO,EAAE,CAAC;UAAEQ,CAAC,EAAE,EAAE;UAAEjC,KAAK,EAAE;QAAI,CAAE;QACxCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAC/CrC,SAAS,EAAC,oEAAoE;QAAAsC,QAAA,eAE9ExC,OAAA;UACEE,SAAS,EAAC,2DAA2D;UACrE8E,KAAK,EAAE;YACLC,eAAe,EAAE,oBAAoB;YACrCC,WAAW,EAAE,yBAAyB;YACtCC,KAAK,EAAE,qBAAqB;YAC5BE,SAAS,EAAE;UACb,CAAE;UAAA7C,QAAA,GAEDgC,cAAc,CAAC,CAAC,eAGjBxE,OAAA;YACEE,SAAS,EAAC,+DAA+D;YACzE8E,KAAK,EAAE;cACLe,UAAU,EAAE,uBAAuB;cACnCC,WAAW,EAAE,uBAAuB;cACpCC,SAAS,EAAE;YACb;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlB/C,OAAA;MAAKE,SAAS,EAAC,SAAS;MAAAsC,QAAA,EAAC;IAEzB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAzSIH,WAAW;AAAAiG,EAAA,GAAXjG,WAAW;AA2SjB,eAAeA,WAAW;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}