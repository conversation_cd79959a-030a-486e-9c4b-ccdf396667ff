import React, { useState, useEffect, Fragment } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Disclosure, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import ShoppingCart from './ShoppingCart';
import ThemeToggle from './ThemeToggle';
import { useUser } from '../contexts/UserContext';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

const ModernNavigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const { user, isAuthenticated, logout } = useUser();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      description: 'Welcome to ShopHub',
      color: 'text-blue-600'
    },
    {
      name: 'Products',
      href: '/products',
      icon: TagIcon,
      description: 'Browse all products',
      color: 'text-purple-600',
      megaMenu: true,
      categories: [
        {
          name: 'Electronics',
          href: '/products?category=electronics',
          icon: '💻',
          description: 'Laptops, phones, tablets & more',
          featured: true
        },
        {
          name: 'Software',
          href: '/products?category=software',
          icon: '💿',
          description: 'Professional software & licenses'
        },
        {
          name: 'Gaming',
          href: '/products?category=gaming',
          icon: '🎮',
          description: 'Gaming gear & accessories',
          featured: true
        },
        {
          name: 'Accessories',
          href: '/products?category=accessories',
          icon: '🎧',
          description: 'Headphones, keyboards & more'
        }
      ],
      featuredProducts: [
        { name: 'MacBook Pro M3', price: '$1,999', image: '/api/placeholder/100/100' },
        { name: 'Gaming Headset', price: '$199', image: '/api/placeholder/100/100' }
      ]
    },
    {
      name: 'Digital',
      href: '/digital-products',
      icon: TagIcon,
      description: 'Instant downloads',
      color: 'text-green-600',
      badge: { text: 'Instant', color: 'bg-green-500' }
    },
    {
      name: 'PC Gaming',
      href: '/pc-gaming',
      icon: TagIcon,
      description: 'Gaming hardware & software',
      color: 'text-red-600',
      badge: { text: 'Hot', color: 'bg-red-500' }
    },
    {
      name: 'About',
      href: '/about',
      icon: InformationCircleIcon,
      description: 'Learn about us',
      color: 'text-gray-600'
    },
    {
      name: 'Contact',
      href: '/contact',
      icon: PhoneIcon,
      description: 'Get in touch',
      color: 'text-indigo-600'
    }
  ];

  const userNavigation = [
    { name: 'Your Profile', href: '/account', icon: UserIcon },
    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },
    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },
    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <Disclosure as="nav" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${
        isScrolled
          ? 'backdrop-blur-xl shadow-xl border-b'
          : 'backdrop-blur-sm'
      }`}
      style={{
        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',
        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',
        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none',
        minHeight: '80px'
      }}>
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-6 lg:px-8">
              <div className="flex h-20 items-center justify-between gap-8">
                {/* Logo - Fixed Width */}
                <div className="flex items-center flex-shrink-0 w-48">
                  <Link to="/" className="flex items-center space-x-3 group">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                      className="relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                    >
                      <ShoppingBagIcon className="w-7 h-7 text-white" />
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                    </motion.div>
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold transition-all duration-300 theme-transition whitespace-nowrap"
                            style={{ color: 'var(--text-primary)' }}>
                        ShopHub
                      </span>
                      <span className="text-xs font-medium transition-all duration-300 theme-transition whitespace-nowrap"
                            style={{ color: 'var(--accent-primary)' }}>
                        Premium Store
                      </span>
                    </div>
                  </Link>
                </div>

                {/* Enhanced Desktop Navigation - Flex Grow */}
                <div className="hidden lg:flex flex-1 justify-center max-w-2xl">
                  <div className="flex items-center space-x-1">
                    {navigation.map((item) => (
                      <motion.div
                        key={item.name}
                        whileHover={{ y: -2 }}
                        transition={{ duration: 0.2 }}
                        className="relative group"
                      >
                        <Link
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'text-white shadow-lg gaming-glow'
                              : 'hover:shadow-lg',
                            'relative flex items-center space-x-2 px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition group whitespace-nowrap'
                          )}
                          style={{
                            backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'var(--bg-secondary)',
                            color: isActive(item.href) ? 'white' : 'var(--text-primary)',
                            borderColor: 'var(--border-primary)',
                            border: '1px solid'
                          }}
                        >
                          <item.icon className={`w-4 h-4 ${isActive(item.href) ? 'text-white' : item.color || 'text-gray-500'}`} />
                          <span className="relative z-10 font-medium">{item.name}</span>
                          {item.badge && (
                            <motion.span
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className={`ml-1 px-1.5 py-0.5 text-xs font-bold rounded-full text-white ${item.badge.color || 'bg-red-500'} pulse-glow`}
                            >
                              {item.badge.text || item.badge}
                            </motion.span>
                          )}

                          {/* Hover Effect */}
                          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                          {/* Active Indicator */}
                          {isActive(item.href) && (
                            <motion.div
                              layoutId="activeTab"
                              className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-white"
                              initial={false}
                              transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            />
                          )}
                        </Link>

                        {/* Enhanced Mega Menu for Products */}
                        {item.megaMenu && (
                          <motion.div
                            initial={{ opacity: 0, y: 10, scale: 0.95 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: 10, scale: 0.95 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-[600px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50"
                          >
                            <div className="rounded-3xl shadow-2xl border backdrop-blur-xl theme-transition overflow-hidden"
                                 style={{
                                   backgroundColor: 'var(--bg-primary)',
                                   borderColor: 'var(--border-primary)',
                                   boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)'
                                 }}>

                              {/* Header */}
                              <div className="p-6 border-b theme-transition"
                                   style={{
                                     borderBottomColor: 'var(--border-primary)',
                                     background: 'linear-gradient(135deg, var(--accent-primary), var(--accent-secondary))'
                                   }}>
                                <h3 className="text-xl font-bold text-white mb-2">
                                  Product Categories
                                </h3>
                                <p className="text-white/80 text-sm">
                                  Discover our premium collection of tech products
                                </p>
                              </div>

                              <div className="p-6">
                                <div className="grid grid-cols-2 gap-4 mb-6">
                                  {item.categories?.map((category) => (
                                    <motion.div
                                      key={category.name}
                                      whileHover={{ scale: 1.02, y: -2 }}
                                      transition={{ duration: 0.2 }}
                                    >
                                      <Link
                                        to={category.href}
                                        className="block p-4 rounded-2xl transition-all duration-300 gaming-glow group/item"
                                        style={{
                                          backgroundColor: 'var(--bg-secondary)',
                                          border: '1px solid var(--border-primary)'
                                        }}
                                      >
                                        <div className="flex items-center space-x-3 mb-2">
                                          <span className="text-3xl">{category.icon}</span>
                                          <div className="flex-1">
                                            <div className="font-semibold theme-transition flex items-center"
                                                 style={{ color: 'var(--text-primary)' }}>
                                              {category.name}
                                              {category.featured && (
                                                <span className="ml-2 px-2 py-0.5 text-xs font-bold rounded-full bg-yellow-400 text-yellow-900">
                                                  Featured
                                                </span>
                                              )}
                                            </div>
                                            <div className="text-xs theme-transition mt-1"
                                                 style={{ color: 'var(--text-secondary)' }}>
                                              {category.description}
                                            </div>
                                          </div>
                                        </div>

                                        {/* Hover Arrow */}
                                        <div className="flex items-center justify-between">
                                          <span className="text-xs font-medium theme-transition"
                                                style={{ color: 'var(--accent-primary)' }}>
                                            Explore →
                                          </span>
                                        </div>
                                      </Link>
                                    </motion.div>
                                  ))}
                                </div>

                                {/* Featured Products Section */}
                                {item.featuredProducts && (
                                  <div className="border-t pt-4 theme-transition"
                                       style={{ borderTopColor: 'var(--border-primary)' }}>
                                    <h4 className="font-semibold mb-3 theme-transition"
                                        style={{ color: 'var(--text-primary)' }}>
                                      Featured Products
                                    </h4>
                                    <div className="grid grid-cols-2 gap-3">
                                      {item.featuredProducts.map((product, index) => (
                                        <div key={index}
                                             className="flex items-center space-x-3 p-3 rounded-xl transition-colors"
                                             style={{ backgroundColor: 'var(--bg-tertiary)' }}>
                                          <img
                                            src={product.image}
                                            alt={product.name}
                                            className="w-12 h-12 rounded-lg object-cover"
                                          />
                                          <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium truncate theme-transition"
                                               style={{ color: 'var(--text-primary)' }}>
                                              {product.name}
                                            </p>
                                            <p className="text-sm font-bold theme-transition"
                                               style={{ color: 'var(--accent-primary)' }}>
                                              {product.price}
                                            </p>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Premium Search Bar - Fixed Width */}
                <div className="hidden md:flex items-center flex-shrink-0 w-96">
                  <motion.div
                    className="relative w-full group"
                    whileHover={{ scale: 1.01 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Search Icon */}
                    <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none z-10">
                      <motion.div
                        animate={{ rotate: searchQuery ? 360 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <MagnifyingGlassIcon className="h-5 w-5 transition-colors duration-300"
                                             style={{ color: searchQuery ? 'var(--accent-primary)' : 'var(--text-secondary)' }} />
                      </motion.div>
                    </div>

                    {/* Search Input */}
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className="w-full pl-12 pr-16 py-3 rounded-xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition gaming-glow"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        borderColor: searchQuery ? 'var(--accent-primary)' : 'var(--border-primary)',
                        color: 'var(--text-primary)',
                        fontSize: '14px'
                      }}
                    />

                    {/* Right Side Actions */}
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center space-x-2">
                      {searchQuery && (
                        <motion.button
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          onClick={() => setSearchQuery('')}
                          className="p-1 rounded-full transition-colors"
                          style={{ color: 'var(--text-secondary)' }}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </motion.button>
                      )}

                      <div className="h-6 w-px bg-gray-300"></div>

                      <kbd className="hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded-lg border theme-transition"
                           style={{
                             backgroundColor: 'var(--bg-tertiary)',
                             borderColor: 'var(--border-secondary)',
                             color: 'var(--text-muted)'
                           }}>
                        ⌘K
                      </kbd>
                    </div>

                    {/* Enhanced Search Suggestions */}
                    {searchQuery && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute top-full left-0 right-0 mt-3 rounded-2xl shadow-2xl border backdrop-blur-xl z-50 theme-transition overflow-hidden"
                        style={{
                          backgroundColor: 'var(--bg-primary)',
                          borderColor: 'var(--border-primary)'
                        }}
                      >
                        <div className="p-6">
                          {/* Quick Suggestions */}
                          <div className="mb-4">
                            <div className="text-sm font-semibold mb-3 theme-transition flex items-center"
                                 style={{ color: 'var(--text-primary)' }}>
                              <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                              Quick Suggestions
                            </div>
                            <div className="space-y-2">
                              {['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets', 'Wireless Keyboards'].map((suggestion) => (
                                <motion.button
                                  key={suggestion}
                                  whileHover={{ x: 4 }}
                                  className="w-full text-left px-4 py-3 rounded-xl transition-all duration-200 gaming-glow group"
                                  style={{
                                    backgroundColor: 'var(--bg-secondary)',
                                    color: 'var(--text-primary)'
                                  }}
                                >
                                  <div className="flex items-center justify-between">
                                    <span className="font-medium">{suggestion}</span>
                                    <svg className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  </div>
                                </motion.button>
                              ))}
                            </div>
                          </div>

                          {/* Popular Categories */}
                          <div className="border-t pt-4 theme-transition"
                               style={{ borderTopColor: 'var(--border-primary)' }}>
                            <div className="text-sm font-semibold mb-3 theme-transition"
                                 style={{ color: 'var(--text-primary)' }}>
                              Popular Categories
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {['Electronics', 'Gaming', 'Software', 'Accessories'].map((category) => (
                                <motion.button
                                  key={category}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="px-3 py-2 rounded-lg text-sm font-medium transition-all"
                                  style={{
                                    backgroundColor: 'var(--accent-primary)',
                                    color: 'white'
                                  }}
                                >
                                  {category}
                                </motion.button>
                              ))}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                </div>

                {/* Enhanced Right Side Actions - Fixed Width */}
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {/* Theme Toggle */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ThemeToggle />
                  </motion.div>

                  {/* Wishlist */}
                  <Link to="/wishlist">
                    <motion.button
                      whileHover={{ scale: 1.05, y: -1 }}
                      whileTap={{ scale: 0.95 }}
                      className="relative p-2.5 rounded-xl transition-all duration-300 gaming-glow"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        borderColor: 'var(--border-primary)',
                        border: '1px solid',
                        color: 'var(--text-primary)'
                      }}
                      title="Wishlist"
                    >
                      <HeartIcon className="w-5 h-5" />
                      {/* Wishlist Count Badge */}
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center pulse-glow"
                      >
                        3
                      </motion.span>
                    </motion.button>
                  </Link>

                  {/* Shopping Cart */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative"
                  >
                    <ShoppingCart />
                  </motion.div>

                  {/* User menu */}
                  {isAuthenticated ? (
                    <Menu as="div" className="relative ml-3">
                      <div>
                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${
                          isScrolled
                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'
                        }`}>
                          <span className="sr-only">Open user menu</span>
                          {user?.profilePicture ? (
                            <img
                              className="h-8 w-8 rounded-full ring-2 ring-white/20"
                              src={user.profilePicture}
                              alt=""
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center">
                              <UserIcon className="w-5 h-5 text-white" />
                            </div>
                          )}
                          <span className="hidden md:block text-sm font-medium">
                            {user?.firstName || 'Account'}
                          </span>
                          <ChevronDownIcon className="w-4 h-4" />
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden">
                          {/* User info header */}
                          <div className="px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white">
                            <div className="flex items-center space-x-3">
                              {user?.profilePicture ? (
                                <img
                                  className="w-12 h-12 rounded-full ring-2 ring-white/30"
                                  src={user.profilePicture}
                                  alt=""
                                />
                              ) : (
                                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                  <UserIcon className="w-6 h-6 text-white" />
                                </div>
                              )}
                              <div>
                                <p className="font-semibold text-white">
                                  {user?.firstName} {user?.lastName}
                                </p>
                                <p className="text-sm text-white/80">{user?.email}</p>
                              </div>
                            </div>
                          </div>
                          
                          {/* Menu items */}
                          <div className="py-2">
                            {userNavigation.map((item) => (
                              <Menu.Item key={item.name}>
                                {({ active }) => (
                                  <Link
                                    to={item.href}
                                    className={classNames(
                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',
                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <item.icon className="w-5 h-5" />
                                    <span>{item.name}</span>
                                  </Link>
                                )}
                              </Menu.Item>
                            ))}
                            <div className="border-t border-gray-100 mt-2 pt-2">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={logout}
                                    className={classNames(
                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',
                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <ArrowRightOnRectangleIcon className="w-5 h-5" />
                                    <span>Sign out</span>
                                  </button>
                                )}
                              </Menu.Item>
                            </div>
                          </div>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Link to="/login">
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 whitespace-nowrap"
                          style={{
                            backgroundColor: 'var(--bg-secondary)',
                            borderColor: 'var(--border-primary)',
                            border: '1px solid',
                            color: 'var(--text-primary)'
                          }}
                        >
                          Sign In
                        </motion.button>
                      </Link>
                      <Link to="/register">
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl whitespace-nowrap"
                          style={{
                            backgroundColor: 'var(--accent-primary)',
                            color: 'white'
                          }}
                        >
                          Sign Up
                        </motion.button>
                      </Link>
                    </div>
                  )}

                  {/* Mobile menu button */}
                  <div className="lg:hidden ml-2">
                    <Disclosure.Button className="relative inline-flex items-center justify-center rounded-xl p-2.5 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-inset"
                                       style={{
                                         backgroundColor: 'var(--bg-secondary)',
                                         borderColor: 'var(--border-primary)',
                                         border: '1px solid',
                                         color: 'var(--text-primary)'
                                       }}>
                      <span className="sr-only">Open main menu</span>
                      <motion.div
                        animate={{ rotate: open ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {open ? (
                          <XMarkIcon className="block h-5 w-5" aria-hidden="true" />
                        ) : (
                          <Bars3Icon className="block h-5 w-5" aria-hidden="true" />
                        )}
                      </motion.div>
                    </Disclosure.Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile menu */}
            <Disclosure.Panel className="lg:hidden">
              <motion.div
                initial={{ opacity: 0, height: 0, y: -20 }}
                animate={{ opacity: 1, height: 'auto', y: 0 }}
                exit={{ opacity: 0, height: 0, y: -20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl"
              >
                <div className="space-y-1 px-6 pb-6 pt-6">
                  {/* Mobile Search */}
                  <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="relative mb-6"
                  >
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className="w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300"
                    />
                  </motion.div>

                  {/* Mobile Navigation */}
                  <div className="space-y-3">
                    {navigation.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * (index + 2) }}
                      >
                        <Disclosure.Button
                          as={Link}
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'
                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',
                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'
                          )}
                        >
                          <item.icon className={classNames(
                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',
                            'w-6 h-6'
                          )} />
                          <span className="font-semibold text-lg">{item.name}</span>
                        </Disclosure.Button>
                      </motion.div>
                    ))}
                  </div>

                  {/* Mobile Auth Buttons */}
                  {!isAuthenticated && (
                    <motion.div 
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex space-x-4 pt-6"
                    >
                      <Link to="/login" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300"
                        >
                          Sign In
                        </Disclosure.Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg"
                        >
                          Sign Up
                        </Disclosure.Button>
                      </Link>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>

      {/* Spacer */}
      <div className="h-20"></div>
    </>
  );
};

export default ModernNavigation;
