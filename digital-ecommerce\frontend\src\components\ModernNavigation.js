import React, { useState, useEffect, Fragment } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Disclosure, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import ShoppingCart from './ShoppingCart';
import { useUser } from '../contexts/UserContext';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

const ModernNavigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const { user, isAuthenticated, logout } = useUser();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      description: 'Welcome to ShopHub'
    },
    {
      name: 'Products',
      href: '/products',
      icon: TagIcon,
      description: 'Browse all products',
      megaMenu: true,
      categories: [
        { name: 'Electronics', href: '/products?category=electronics', icon: '💻' },
        { name: 'Software', href: '/products?category=software', icon: '💿' },
        { name: 'Gaming', href: '/products?category=gaming', icon: '🎮' }
      ]
    },
    {
      name: 'Digital',
      href: '/digital-products',
      icon: TagIcon,
      description: 'Instant downloads',
      badge: 'Instant'
    },
    {
      name: 'PC Gaming',
      href: '/pc-gaming',
      icon: TagIcon,
      description: 'Gaming hardware & software',
      badge: 'Hot'
    },
    {
      name: 'About',
      href: '/about',
      icon: InformationCircleIcon,
      description: 'Learn about us'
    },
    {
      name: 'Contact',
      href: '/contact',
      icon: PhoneIcon,
      description: 'Get in touch'
    }
  ];

  const userNavigation = [
    { name: 'Your Profile', href: '/account', icon: UserIcon },
    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },
    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },
    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <Disclosure as="nav" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${
        isScrolled
          ? 'backdrop-blur-xl shadow-xl border-b'
          : 'backdrop-blur-sm'
      }`}
      style={{
        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',
        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',
        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none'
      }}>
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="flex h-20 items-center justify-between">
                {/* Logo */}
                <div className="flex items-center">
                  <Link to="/" className="flex items-center space-x-3 group">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                      className="relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                    >
                      <ShoppingBagIcon className="w-7 h-7 text-white" />
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                    </motion.div>
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold transition-all duration-300 theme-transition"
                            style={{ color: 'var(--text-primary)' }}>
                        ShopHub
                      </span>
                      <span className="text-xs font-medium transition-all duration-300 theme-transition"
                            style={{ color: 'var(--accent-primary)' }}>
                        Premium Store
                      </span>
                    </div>
                  </Link>
                </div>

                {/* Desktop Navigation */}
                <div className="hidden lg:block">
                  <div className="flex items-center space-x-1">
                    {navigation.map((item) => (
                      <motion.div
                        key={item.name}
                        whileHover={{ y: -2 }}
                        transition={{ duration: 0.2 }}
                        className="relative group"
                      >
                        <Link
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'text-white shadow-lg'
                              : 'hover:shadow-lg',
                            'relative flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition'
                          )}
                          style={{
                            backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'transparent',
                            color: isActive(item.href) ? 'white' : 'var(--text-primary)',
                            borderColor: 'var(--border-primary)'
                          }}
                        >
                          <item.icon className="w-4 h-4" />
                          <span className="relative z-10">{item.name}</span>
                          {item.badge && (
                            <span className="ml-1 px-2 py-0.5 text-xs font-bold rounded-full bg-red-500 text-white">
                              {item.badge}
                            </span>
                          )}
                          {!isActive(item.href) && (
                            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                          )}
                        </Link>

                        {/* Mega Menu for Products */}
                        {item.megaMenu && (
                          <div className="absolute top-full left-0 mt-2 w-80 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div className="bg-white rounded-2xl shadow-2xl border border-gray-100 p-6 theme-transition"
                                 style={{
                                   backgroundColor: 'var(--bg-primary)',
                                   borderColor: 'var(--border-primary)'
                                 }}>
                              <div className="mb-4">
                                <h3 className="text-lg font-bold theme-transition"
                                    style={{ color: 'var(--text-primary)' }}>
                                  Product Categories
                                </h3>
                                <p className="text-sm theme-transition"
                                   style={{ color: 'var(--text-secondary)' }}>
                                  Explore our diverse product range
                                </p>
                              </div>
                              <div className="grid grid-cols-1 gap-2">
                                {item.categories?.map((category) => (
                                  <Link
                                    key={category.name}
                                    to={category.href}
                                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition"
                                    style={{ backgroundColor: 'transparent' }}
                                  >
                                    <span className="text-2xl">{category.icon}</span>
                                    <div>
                                      <div className="font-medium theme-transition"
                                           style={{ color: 'var(--text-primary)' }}>
                                        {category.name}
                                      </div>
                                      <div className="text-xs theme-transition"
                                           style={{ color: 'var(--text-secondary)' }}>
                                        Browse {category.name.toLowerCase()}
                                      </div>
                                    </div>
                                  </Link>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Enhanced Search Bar */}
                <div className="hidden md:flex items-center flex-1 max-w-2xl mx-8">
                  <motion.div
                    className="relative w-full group"
                    whileHover={{ scale: 1.01 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                      <MagnifyingGlassIcon className="h-5 w-5 transition-colors duration-300"
                                           style={{ color: 'var(--text-secondary)' }} />
                    </div>
                    <input
                      type="text"
                      placeholder="Search products, brands, categories..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className="w-full pl-12 pr-16 py-4 rounded-2xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        borderColor: 'var(--border-primary)',
                        color: 'var(--text-primary)'
                      }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                      <div className="flex items-center space-x-2">
                        <kbd className="hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded border theme-transition"
                             style={{
                               backgroundColor: 'var(--bg-tertiary)',
                               borderColor: 'var(--border-secondary)',
                               color: 'var(--text-muted)'
                             }}>
                          ⌘K
                        </kbd>
                      </div>
                    </div>

                    {/* Search suggestions dropdown */}
                    {searchQuery && (
                      <div className="absolute top-full left-0 right-0 mt-2 rounded-2xl shadow-2xl border z-50 theme-transition"
                           style={{
                             backgroundColor: 'var(--bg-primary)',
                             borderColor: 'var(--border-primary)'
                           }}>
                        <div className="p-4">
                          <div className="text-sm font-medium mb-2 theme-transition"
                               style={{ color: 'var(--text-secondary)' }}>
                            Quick suggestions
                          </div>
                          <div className="space-y-2">
                            {['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets'].map((suggestion) => (
                              <button
                                key={suggestion}
                                className="w-full text-left px-3 py-2 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition"
                                style={{ color: 'var(--text-primary)' }}
                              >
                                <MagnifyingGlassIcon className="w-4 h-4 inline mr-2" />
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>

                {/* Right side items */}
                <div className="flex items-center space-x-3">
                  {/* Wishlist */}
                  <Link to="/wishlist">
                    <motion.button
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative p-3 rounded-xl transition-all duration-300 group ${
                        isScrolled
                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                          : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'
                      }`}
                    >
                      <HeartIcon className="w-6 h-6" />
                    </motion.button>
                  </Link>

                  {/* Shopping Cart */}
                  <div className="relative">
                    <ShoppingCart />
                  </div>

                  {/* User menu */}
                  {isAuthenticated ? (
                    <Menu as="div" className="relative ml-3">
                      <div>
                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${
                          isScrolled
                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'
                        }`}>
                          <span className="sr-only">Open user menu</span>
                          {user?.profilePicture ? (
                            <img
                              className="h-8 w-8 rounded-full ring-2 ring-white/20"
                              src={user.profilePicture}
                              alt=""
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center">
                              <UserIcon className="w-5 h-5 text-white" />
                            </div>
                          )}
                          <span className="hidden md:block text-sm font-medium">
                            {user?.firstName || 'Account'}
                          </span>
                          <ChevronDownIcon className="w-4 h-4" />
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden">
                          {/* User info header */}
                          <div className="px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white">
                            <div className="flex items-center space-x-3">
                              {user?.profilePicture ? (
                                <img
                                  className="w-12 h-12 rounded-full ring-2 ring-white/30"
                                  src={user.profilePicture}
                                  alt=""
                                />
                              ) : (
                                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                  <UserIcon className="w-6 h-6 text-white" />
                                </div>
                              )}
                              <div>
                                <p className="font-semibold text-white">
                                  {user?.firstName} {user?.lastName}
                                </p>
                                <p className="text-sm text-white/80">{user?.email}</p>
                              </div>
                            </div>
                          </div>
                          
                          {/* Menu items */}
                          <div className="py-2">
                            {userNavigation.map((item) => (
                              <Menu.Item key={item.name}>
                                {({ active }) => (
                                  <Link
                                    to={item.href}
                                    className={classNames(
                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',
                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <item.icon className="w-5 h-5" />
                                    <span>{item.name}</span>
                                  </Link>
                                )}
                              </Menu.Item>
                            ))}
                            <div className="border-t border-gray-100 mt-2 pt-2">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={logout}
                                    className={classNames(
                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',
                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <ArrowRightOnRectangleIcon className="w-5 h-5" />
                                    <span>Sign out</span>
                                  </button>
                                )}
                              </Menu.Item>
                            </div>
                          </div>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <Link to="/login">
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${
                            isScrolled
                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'
                              : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'
                          }`}
                        >
                          Sign In
                        </motion.button>
                      </Link>
                      <Link to="/register">
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                        >
                          Sign Up
                        </motion.button>
                      </Link>
                    </div>
                  )}

                  {/* Mobile menu button */}
                  <div className="lg:hidden">
                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${
                      isScrolled
                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                        : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'
                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>
                      <span className="sr-only">Open main menu</span>
                      <motion.div
                        animate={{ rotate: open ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {open ? (
                          <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                        ) : (
                          <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                        )}
                      </motion.div>
                    </Disclosure.Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile menu */}
            <Disclosure.Panel className="lg:hidden">
              <motion.div
                initial={{ opacity: 0, height: 0, y: -20 }}
                animate={{ opacity: 1, height: 'auto', y: 0 }}
                exit={{ opacity: 0, height: 0, y: -20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl"
              >
                <div className="space-y-1 px-6 pb-6 pt-6">
                  {/* Mobile Search */}
                  <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="relative mb-6"
                  >
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className="w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300"
                    />
                  </motion.div>

                  {/* Mobile Navigation */}
                  <div className="space-y-3">
                    {navigation.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * (index + 2) }}
                      >
                        <Disclosure.Button
                          as={Link}
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'
                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',
                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'
                          )}
                        >
                          <item.icon className={classNames(
                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',
                            'w-6 h-6'
                          )} />
                          <span className="font-semibold text-lg">{item.name}</span>
                        </Disclosure.Button>
                      </motion.div>
                    ))}
                  </div>

                  {/* Mobile Auth Buttons */}
                  {!isAuthenticated && (
                    <motion.div 
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex space-x-4 pt-6"
                    >
                      <Link to="/login" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300"
                        >
                          Sign In
                        </Disclosure.Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg"
                        >
                          Sign Up
                        </Disclosure.Button>
                      </Link>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>

      {/* Spacer */}
      <div className="h-20"></div>
    </>
  );
};

export default ModernNavigation;
