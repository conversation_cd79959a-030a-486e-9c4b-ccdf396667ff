import React, { useState, useEffect, Fragment } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Disclosure, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import ShoppingCart from './ShoppingCart';
import { useUser } from '../contexts/UserContext';
import ThemeToggle from './ThemeToggle';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

const ModernNavigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const { user, isAuthenticated, logout } = useUser();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Products', href: '/products', icon: TagIcon },
    { name: 'Digital', href: '/digital-products', icon: TagIcon },
    { name: 'PC Gaming', href: '/pc-gaming', icon: TagIcon },
    { name: 'About', href: '/about', icon: InformationCircleIcon },
    { name: 'Contact', href: '/contact', icon: PhoneIcon }
  ];

  const userNavigation = [
    { name: 'Your Profile', href: '/account', icon: UserIcon },
    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },
    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },
    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <Disclosure as="nav" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${
        isScrolled
          ? 'backdrop-blur-xl shadow-xl'
          : 'backdrop-blur-sm'
      }`}
      style={{
        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'rgba(255, 255, 255, 0.1)',
        borderBottomColor: 'var(--border-primary)'
      }}>
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="flex h-20 items-center justify-between">
                {/* Logo */}
                <div className="flex items-center">
                  <Link to="/" className="flex items-center space-x-3 group">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                      className="relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                    >
                      <ShoppingBagIcon className="w-7 h-7 text-white" />
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                    </motion.div>
                    <div className="flex flex-col">
                      <span className={`text-2xl font-bold transition-all duration-300 ${
                        isScrolled ? 'text-gray-900' : 'text-gray-900 drop-shadow-lg'
                      }`}>
                        ShopHub
                      </span>
                      <span className={`text-xs font-medium transition-all duration-300 ${
                        isScrolled ? 'text-light-orange-600' : 'text-light-orange-600 drop-shadow-sm'
                      }`}>
                        Premium Store
                      </span>
                    </div>
                  </Link>
                </div>

                {/* Desktop Navigation */}
                <div className="hidden lg:block">
                  <div className="flex items-baseline space-x-2">
                    {navigation.map((item) => (
                      <motion.div
                        key={item.name}
                        whileHover={{ y: -2 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Link
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'
                              : isScrolled
                                ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                                : 'text-gray-900 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm shadow-sm border border-white/20',
                            'relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group'
                          )}
                        >
                          <span className="relative z-10">{item.name}</span>
                          {!isActive(item.href) && (
                            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                          )}
                        </Link>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Search Bar */}
                <div className="hidden md:flex items-center flex-1 max-w-lg mx-8">
                  <motion.div 
                    className="relative w-full group"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${
                        isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'
                      }`} />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for products, brands, and more..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${
                        isScrolled
                          ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'
                          : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'
                      } focus:outline-none shadow-lg hover:shadow-xl`}
                    />
                  </motion.div>
                </div>

                {/* Right side items */}
                <div className="flex items-center space-x-3">
                  {/* Wishlist */}
                  <Link to="/wishlist">
                    <motion.button
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative p-3 rounded-xl transition-all duration-300 group ${
                        isScrolled
                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                          : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'
                      }`}
                    >
                      <HeartIcon className="w-6 h-6" />
                    </motion.button>
                  </Link>

                  {/* Theme Toggle */}
                  <ThemeToggle className="theme-toggle-mobile" />

                  {/* Shopping Cart */}
                  <div className="relative">
                    <ShoppingCart />
                  </div>

                  {/* User menu */}
                  {isAuthenticated ? (
                    <Menu as="div" className="relative ml-3">
                      <div>
                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${
                          isScrolled
                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'
                        }`}>
                          <span className="sr-only">Open user menu</span>
                          {user?.profilePicture ? (
                            <img
                              className="h-8 w-8 rounded-full ring-2 ring-white/20"
                              src={user.profilePicture}
                              alt=""
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center">
                              <UserIcon className="w-5 h-5 text-white" />
                            </div>
                          )}
                          <span className="hidden md:block text-sm font-medium">
                            {user?.firstName || 'Account'}
                          </span>
                          <ChevronDownIcon className="w-4 h-4" />
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden">
                          {/* User info header */}
                          <div className="px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white">
                            <div className="flex items-center space-x-3">
                              {user?.profilePicture ? (
                                <img
                                  className="w-12 h-12 rounded-full ring-2 ring-white/30"
                                  src={user.profilePicture}
                                  alt=""
                                />
                              ) : (
                                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                  <UserIcon className="w-6 h-6 text-white" />
                                </div>
                              )}
                              <div>
                                <p className="font-semibold text-white">
                                  {user?.firstName} {user?.lastName}
                                </p>
                                <p className="text-sm text-white/80">{user?.email}</p>
                              </div>
                            </div>
                          </div>
                          
                          {/* Menu items */}
                          <div className="py-2">
                            {userNavigation.map((item) => (
                              <Menu.Item key={item.name}>
                                {({ active }) => (
                                  <Link
                                    to={item.href}
                                    className={classNames(
                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',
                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <item.icon className="w-5 h-5" />
                                    <span>{item.name}</span>
                                  </Link>
                                )}
                              </Menu.Item>
                            ))}
                            <div className="border-t border-gray-100 mt-2 pt-2">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={logout}
                                    className={classNames(
                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',
                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'
                                    )}
                                  >
                                    <ArrowRightOnRectangleIcon className="w-5 h-5" />
                                    <span>Sign out</span>
                                  </button>
                                )}
                              </Menu.Item>
                            </div>
                          </div>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <Link to="/login">
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${
                            isScrolled
                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'
                              : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'
                          }`}
                        >
                          Sign In
                        </motion.button>
                      </Link>
                      <Link to="/register">
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                        >
                          Sign Up
                        </motion.button>
                      </Link>
                    </div>
                  )}

                  {/* Mobile menu button */}
                  <div className="lg:hidden">
                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${
                      isScrolled
                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                        : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'
                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>
                      <span className="sr-only">Open main menu</span>
                      <motion.div
                        animate={{ rotate: open ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {open ? (
                          <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                        ) : (
                          <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                        )}
                      </motion.div>
                    </Disclosure.Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile menu */}
            <Disclosure.Panel className="lg:hidden">
              <motion.div
                initial={{ opacity: 0, height: 0, y: -20 }}
                animate={{ opacity: 1, height: 'auto', y: 0 }}
                exit={{ opacity: 0, height: 0, y: -20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl"
              >
                <div className="space-y-1 px-6 pb-6 pt-6">
                  {/* Mobile Search */}
                  <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="relative mb-6"
                  >
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                      className="w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300"
                    />
                  </motion.div>

                  {/* Mobile Navigation */}
                  <div className="space-y-3">
                    {navigation.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * (index + 2) }}
                      >
                        <Disclosure.Button
                          as={Link}
                          to={item.href}
                          className={classNames(
                            isActive(item.href)
                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'
                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',
                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'
                          )}
                        >
                          <item.icon className={classNames(
                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',
                            'w-6 h-6'
                          )} />
                          <span className="font-semibold text-lg">{item.name}</span>
                        </Disclosure.Button>
                      </motion.div>
                    ))}
                  </div>

                  {/* Mobile Auth Buttons */}
                  {!isAuthenticated && (
                    <motion.div 
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex space-x-4 pt-6"
                    >
                      <Link to="/login" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300"
                        >
                          Sign In
                        </Disclosure.Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Disclosure.Button
                          as="button"
                          className="w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg"
                        >
                          Sign Up
                        </Disclosure.Button>
                      </Link>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>

      {/* Spacer */}
      <div className="h-20"></div>
    </>
  );
};

export default ModernNavigation;
