import React, { createContext, useContext, useState, useEffect } from 'react';
import { products as initialProducts, categories as initialCategories } from '../data/products';
import { pcGamingProducts, pcGamingCategories, pcGamingBundles, compatibilityMatrix } from '../data/pcGamingProducts';

const ProductContext = createContext();

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

export const ProductProvider = ({ children }) => {
  const [products, setProducts] = useState([...initialProducts, ...pcGamingProducts]);
  const [categories, setCategories] = useState(initialCategories);
  const [pcCategories, setPcCategories] = useState(pcGamingCategories);
  const [bundles, setBundles] = useState(pcGamingBundles);
  const [isLoading, setIsLoading] = useState(false);

  // Load products from localStorage on mount
  useEffect(() => {
    const savedProducts = localStorage.getItem('products');
    const savedCategories = localStorage.getItem('categories');
    
    if (savedProducts) {
      try {
        setProducts(JSON.parse(savedProducts));
      } catch (error) {
        console.error('Error loading products from localStorage:', error);
      }
    }
    
    if (savedCategories) {
      try {
        setCategories(JSON.parse(savedCategories));
      } catch (error) {
        console.error('Error loading categories from localStorage:', error);
      }
    }
  }, []);

  // Save products to localStorage whenever products change
  useEffect(() => {
    localStorage.setItem('products', JSON.stringify(products));
  }, [products]);

  // Save categories to localStorage whenever categories change
  useEffect(() => {
    localStorage.setItem('categories', JSON.stringify(categories));
  }, [categories]);

  const addProduct = async (productData) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate unique ID
      const newId = `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Process images (in a real app, you'd upload to a server)
      const processedImages = productData.images.map((image, index) => ({
        id: `img-${newId}-${index}`,
        url: image.url, // In production, this would be the uploaded URL
        alt: productData.name,
        isPrimary: index === 0
      }));

      // Create new product object
      const newProduct = {
        id: newId,
        name: productData.name,
        description: productData.description,
        shortDescription: productData.shortDescription,
        price: parseFloat(productData.price),
        discountPrice: productData.discountPrice ? parseFloat(productData.discountPrice) : null,
        currency: productData.currency,
        category: productData.category,
        subcategory: productData.subcategory,
        type: productData.type,
        stockCount: productData.type === 'physical' ? parseInt(productData.stockCount) : null,
        sku: productData.sku,
        tags: productData.tags,
        keywords: productData.keywords,
        isActive: productData.isActive,
        isFeatured: productData.isFeatured,
        specifications: productData.specifications,
        images: processedImages,
        image: processedImages[0]?.url, // Main image for backward compatibility
        inStock: productData.type === 'digital' ? true : parseInt(productData.stockCount) > 0,
        rating: 0,
        reviews: 0,
        sold: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to products list
      setProducts(prevProducts => [newProduct, ...prevProducts]);
      
      setIsLoading(false);
      return { success: true, product: newProduct };
    } catch (error) {
      setIsLoading(false);
      console.error('Error adding product:', error);
      return { success: false, error: error.message };
    }
  };

  const updateProduct = async (productId, updates) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prevProducts =>
        prevProducts.map(product =>
          product.id === productId
            ? { ...product, ...updates, updatedAt: new Date().toISOString() }
            : product
        )
      );
      
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      setIsLoading(false);
      console.error('Error updating product:', error);
      return { success: false, error: error.message };
    }
  };

  const deleteProduct = async (productId) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prevProducts =>
        prevProducts.filter(product => product.id !== productId)
      );
      
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      setIsLoading(false);
      console.error('Error deleting product:', error);
      return { success: false, error: error.message };
    }
  };

  const addCategory = async (categoryData) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newId = `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const newCategory = {
        id: newId,
        name: categoryData.name,
        description: categoryData.description,
        icon: categoryData.icon,
        subcategories: categoryData.subcategories || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      setCategories(prevCategories => [...prevCategories, newCategory]);
      
      setIsLoading(false);
      return { success: true, category: newCategory };
    } catch (error) {
      setIsLoading(false);
      console.error('Error adding category:', error);
      return { success: false, error: error.message };
    }
  };

  const updateCategory = async (categoryId, updates) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCategories(prevCategories =>
        prevCategories.map(category =>
          category.id === categoryId
            ? { ...category, ...updates, updatedAt: new Date().toISOString() }
            : category
        )
      );
      
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      setIsLoading(false);
      console.error('Error updating category:', error);
      return { success: false, error: error.message };
    }
  };

  const deleteCategory = async (categoryId) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if any products use this category
      const productsUsingCategory = products.filter(product => product.category === categoryId);
      if (productsUsingCategory.length > 0) {
        throw new Error(`Cannot delete category. ${productsUsingCategory.length} products are using this category.`);
      }
      
      setCategories(prevCategories =>
        prevCategories.filter(category => category.id !== categoryId)
      );
      
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      setIsLoading(false);
      console.error('Error deleting category:', error);
      return { success: false, error: error.message };
    }
  };

  const getProductById = (productId) => {
    return products.find(product => product.id === productId);
  };

  const getCategoryById = (categoryId) => {
    return categories.find(category => category.id === categoryId);
  };

  const getProductsByCategory = (categoryId) => {
    return products.filter(product => product.category === categoryId);
  };

  const searchProducts = (query) => {
    const lowercaseQuery = query.toLowerCase();
    return products.filter(product =>
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.description.toLowerCase().includes(lowercaseQuery) ||
      (product.tags && product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))) ||
      (product.keywords && product.keywords.toLowerCase().includes(lowercaseQuery))
    );
  };

  // PC Gaming specific functions
  const getPcGamingProducts = (subcategory = null, componentType = null) => {
    return products.filter(product => {
      const isPcGaming = product.category === 'pc-gaming';
      const matchesSubcategory = !subcategory || product.subcategory === subcategory;
      const matchesComponentType = !componentType || product.componentType === componentType;

      return isPcGaming && matchesSubcategory && matchesComponentType;
    });
  };

  const checkCompatibility = (components) => {
    // Basic compatibility checking logic
    const cpu = components.find(c => c.componentType === 'cpu');
    const motherboard = components.find(c => c.componentType === 'motherboard');

    const issues = [];

    if (cpu && motherboard) {
      const cpuSocket = cpu.compatibility?.socket;
      const mbSocket = motherboard.compatibility?.socket;
      if (cpuSocket && mbSocket && cpuSocket !== mbSocket) {
        issues.push(`CPU socket ${cpuSocket} is not compatible with motherboard socket ${mbSocket}`);
      }
    }

    return {
      compatible: issues.length === 0,
      issues
    };
  };

  const calculateBundlePrice = (productIds) => {
    const bundleProducts = products.filter(p => productIds.includes(p.id));
    const totalPrice = bundleProducts.reduce((sum, product) => sum + product.price, 0);
    const bundleDiscount = totalPrice * 0.05; // 5% bundle discount

    return {
      originalPrice: totalPrice,
      bundlePrice: totalPrice - bundleDiscount,
      savings: bundleDiscount,
      products: bundleProducts
    };
  };

  const getRecommendedComponents = (baseComponent) => {
    // Simple recommendation logic based on component type and price range
    const priceRange = {
      budget: baseComponent.price < 200,
      midRange: baseComponent.price >= 200 && baseComponent.price < 500,
      highEnd: baseComponent.price >= 500
    };

    return products.filter(product => {
      if (product.category !== 'pc-gaming' || product.subcategory !== 'pc-component') {
        return false;
      }

      // Don't recommend the same component type
      if (product.componentType === baseComponent.componentType) {
        return false;
      }

      // Match price tier
      if (priceRange.budget && product.price > 300) return false;
      if (priceRange.midRange && (product.price < 150 || product.price > 800)) return false;
      if (priceRange.highEnd && product.price < 400) return false;

      return true;
    }).slice(0, 6); // Return top 6 recommendations
  };

  const value = {
    products,
    categories,
    pcCategories,
    bundles,
    isLoading,
    addProduct,
    updateProduct,
    deleteProduct,
    addCategory,
    updateCategory,
    deleteCategory,
    getProductById,
    getCategoryById,
    getProductsByCategory,
    searchProducts,
    // PC Gaming specific functions
    getPcGamingProducts,
    checkCompatibility,
    calculateBundlePrice,
    getRecommendedComponents,
    compatibilityMatrix
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};

export default ProductContext;
