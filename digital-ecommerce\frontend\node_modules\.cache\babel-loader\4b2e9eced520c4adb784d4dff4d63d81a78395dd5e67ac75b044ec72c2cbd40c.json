{"ast": null, "code": "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nconst svgMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState\n  })\n};\nexport { svgMotionConfig };", "map": {"version": 3, "names": ["makeUseVisualState", "createSvgRenderState", "scrapeMotionValuesFromProps", "svgMotionConfig", "useVisualState", "createRenderState"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n    }),\n};\n\nexport { svgMotionConfig };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,eAAe,GAAG;EACpBC,cAAc,EAAEJ,kBAAkB,CAAC;IAC/BE,2BAA2B,EAAEA,2BAA2B;IACxDG,iBAAiB,EAAEJ;EACvB,CAAC;AACL,CAAC;AAED,SAASE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}