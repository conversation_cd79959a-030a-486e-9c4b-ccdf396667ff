{"ast": null, "code": "import { statsBuffer, isSVGElement, isSVGSVGElement, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, frame, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\nconst metrics = {\n  nodes: 0,\n  calculatedTargetDeltas: 0,\n  calculatedProjections: 0\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = {\n  visibility: \"hidden\"\n};\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n  const {\n    latestValues\n  } = visualElement;\n  // Record the distorting transform and then temporarily set it to 0\n  if (latestValues[key]) {\n    values[key] = latestValues[key];\n    visualElement.setStaticValue(key, 0);\n    if (sharedAnimationValues) {\n      sharedAnimationValues[key] = 0;\n    }\n  }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n  projectionNode.hasCheckedOptimisedAppear = true;\n  if (projectionNode.root === projectionNode) return;\n  const {\n    visualElement\n  } = projectionNode.options;\n  if (!visualElement) return;\n  const appearId = getOptimisedAppearId(visualElement);\n  if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n    const {\n      layout,\n      layoutId\n    } = projectionNode.options;\n    window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n  }\n  const {\n    parent\n  } = projectionNode;\n  if (parent && !parent.hasCheckedOptimisedAppear) {\n    cancelTreeOptimisedTransformAnimations(parent);\n  }\n}\nfunction createProjectionNode({\n  attachResizeListener,\n  defaultParent,\n  measureScroll,\n  checkIsScrollRoot,\n  resetTransform\n}) {\n  return class ProjectionNode {\n    constructor(latestValues = {}, parent = defaultParent?.()) {\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this node needs\n       * recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Flag to true if the layout *or* transform has changed. This then gets propagated\n       * throughout the projection tree, forcing any element below to recalculate on the next frame.\n       */\n      this.isSharedProjectionDirty = false;\n      /**\n       * Flag transform dirty. This gets propagated throughout the whole tree but is only\n       * respected by shared nodes.\n       */\n      this.isTransformDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * Store whether this node has been checked for optimised appear animations. As\n       * effects fire bottom-up, and we want to look up the tree for appear animations,\n       * this makes sure we only check each path once, stopping at nodes that\n       * have already been checked.\n       */\n      this.hasCheckedOptimisedAppear = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      this.hasTreeAnimated = false;\n      // Note: Currently only running on root node\n      this.updateScheduled = false;\n      this.scheduleUpdate = () => this.update();\n      this.projectionUpdateScheduled = false;\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.projectionUpdateScheduled = false;\n        /**\n         * Reset debug counts. Manually resetting rather than creating a new\n         * object each frame.\n         */\n        if (statsBuffer.value) {\n          metrics.nodes = metrics.calculatedTargetDeltas = metrics.calculatedProjections = 0;\n        }\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n        this.nodes.forEach(cleanDirtyNodes);\n        if (statsBuffer.addProjectionMetrics) {\n          statsBuffer.addProjectionMetrics(metrics);\n        }\n      };\n      /**\n       * Frame calculations\n       */\n      this.resolvedRelativeTargetAt = 0.0;\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name, ...args) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      subscriptionManager && subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance) {\n      if (this.instance) return;\n      this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      this.parent && this.parent.children.add(this);\n      if (this.root.hasTreeAnimated && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", ({\n          delta,\n          hasLayoutChanged,\n          hasRelativeLayoutChanged,\n          layout: newLayout\n        }) => {\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = this.options.transition || visualElement.getDefaultTransition() || defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const hasTargetChanged = !this.targetLayout || !boxEqualsRounded(this.targetLayout, newLayout);\n          /*\n           * Note: Disabled to fix relative animations always triggering new\n           * layout animations. If this causes further issues, we can try\n           * a different approach to detecting relative target changes.\n           */\n          // || hasRelativeLayoutChanged\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n          if (this.options.layoutRoot || this.resumeFrom || hasOnlyRelativeTargetChanged || hasLayoutChanged && (hasTargetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            const animationOptions = {\n              ...getValueTransition(layoutTransition, \"layout\"),\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            };\n            if (visualElement.shouldReduceMotion || this.options.layoutRoot) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n            /**\n             * Set animation origin after starting animation to avoid layout jump\n             * caused by stopping previous layout animation\n             */\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged) {\n              finishAnimation(this);\n            }\n            if (this.isLead() && this.options.onExitComplete) {\n              this.options.onExitComplete();\n            }\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      const stack = this.getStack();\n      stack && stack.remove(this);\n      this.parent && this.parent.children.delete(this);\n      this.instance = undefined;\n      this.eventHandlers.clear();\n      cancelFrame(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      this.nodes && this.nodes.forEach(resetSkewAndRotation);\n      this.animationId++;\n    }\n    getTransformTemplate() {\n      const {\n        visualElement\n      } = this.options;\n      return visualElement && visualElement.getProps().transformTemplate;\n    }\n    willUpdate(shouldNotifyListeners = true) {\n      this.root.hasTreeAnimated = true;\n      if (this.root.isUpdateBlocked()) {\n        this.options.onExitComplete && this.options.onExitComplete();\n        return;\n      }\n      /**\n       * If we're running optimised appear animations then these must be\n       * cancelled before measuring the DOM. This is so we can measure\n       * the true layout of the element rather than the WAAPI animation\n       * which will be unaffected by the resetSkewAndRotate step.\n       *\n       * Note: This is a DOM write. Worst case scenario is this is sandwiched\n       * between other snapshot reads which will cause unnecessary style recalculations.\n       * This has to happen here though, as we don't yet know which nodes will need\n       * snapshots in startUpdate(), but we only want to cancel optimised animations\n       * if a layout animation measurement is actually going to be affected by them.\n       */\n      if (window.MotionCancelOptimisedAnimation && !this.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(this);\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n        if (node.options.layoutRoot) {\n          node.willUpdate(false);\n        }\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = this.getTransformTemplate();\n      this.prevTransformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    update() {\n      this.updateScheduled = false;\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) {\n        this.nodes.forEach(clearIsLayoutDirty);\n      }\n      this.isUpdating = false;\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      /**\n       * Manually flush any pending updates. Ideally\n       * we could leave this to the following requestAnimationFrame but this seems\n       * to leave a flash of incorrectly styled content.\n       */\n      const now = time.now();\n      frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n      frameData.timestamp = now;\n      frameData.isProcessing = true;\n      frameSteps.update.process(frameData);\n      frameSteps.preRender.process(frameData);\n      frameSteps.render.process(frameData);\n      frameData.isProcessing = false;\n    }\n    didUpdate() {\n      if (!this.updateScheduled) {\n        this.updateScheduled = true;\n        microtask.read(this.scheduleUpdate);\n      }\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      if (!this.projectionUpdateScheduled) {\n        this.projectionUpdateScheduled = true;\n        frame.preRender(this.updateProjection, false, true);\n      }\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      frame.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n      if (this.snapshot && !calcLength(this.snapshot.measuredBox.x) && !calcLength(this.snapshot.measuredBox.y)) {\n        this.snapshot = undefined;\n      }\n    }\n    updateLayout() {\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      const {\n        visualElement\n      } = this.options;\n      visualElement && visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n    }\n    updateScroll(phase = \"measure\") {\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement && this.instance) {\n        const isRoot = checkIsScrollRoot(this.instance);\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot,\n          offset: measureScroll(this.instance),\n          wasRoot: this.scroll ? this.scroll.isRoot : isRoot\n        };\n      }\n    }\n    resetTransform() {\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform || this.options.alwaysMeasureLayout;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = this.getTransformTemplate();\n      const transformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && this.instance && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure(removeTransform = true) {\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      const wasInScrollRoot = this.scroll?.wasRoot || this.path.some(checkNodeWasScrollRoot);\n      if (!wasInScrollRoot) {\n        // Remove viewport scroll to give page-relative coordinates\n        const {\n          scroll\n        } = this.root;\n        if (scroll) {\n          translateAxis(box.x, scroll.offset.x);\n          translateAxis(box.y, scroll.offset.y);\n        }\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      if (this.scroll?.wasRoot) {\n        return boxWithoutScroll;\n      }\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.wasRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box, transformOnly = false) {\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.root.scheduleUpdateProjection();\n      this.isProjectionDirty = true;\n    }\n    setOptions(options) {\n      this.options = {\n        ...this.options,\n        ...options,\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      };\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    forceRelativeParentToResolveTarget() {\n      if (!this.relativeParent) return;\n      /**\n       * If the parent target isn't up-to-date, force it to update.\n       * This is an unfortunate de-optimisation as it means any updating relative\n       * projection will cause all the relative parents to recalculate back\n       * up the tree.\n       */\n      if (this.relativeParent.resolvedRelativeTargetAt !== frameData.timestamp) {\n        this.relativeParent.resolveTargetDelta(true);\n      }\n    }\n    resolveTargetDelta(forceRecalculation = false) {\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      const canSkip = !(forceRecalculation || isShared && this.isSharedProjectionDirty || this.isProjectionDirty || this.parent?.isProjectionDirty || this.attemptToResolveRelativeTarget || this.root.updateBlockedByResize);\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      this.resolvedRelativeTargetAt = frameData.timestamp;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      if (!this.targetDelta && !this.relativeTarget) {\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target) {\n        this.forceRelativeParentToResolveTarget();\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * Increase debug counter for resolved target deltas\n       */\n      if (statsBuffer.value) {\n        metrics.calculatedTargetDeltas++;\n      }\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) {\n        return undefined;\n      }\n      if (this.parent.isProjecting()) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    isProjecting() {\n      return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout);\n    }\n    calcProjection() {\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      /**\n       * If this is a normal layout animation and neither this node nor its nearest projecting\n       * is dirty then we can't skip.\n       */\n      if (this.isProjectionDirty || this.parent?.isProjectionDirty) {\n        canSkip = false;\n      }\n      /**\n       * If this is a shared layout animation and this node's shared projection is dirty then\n       * we can't skip.\n       */\n      if (isShared && (this.isSharedProjectionDirty || this.isTransformDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If we have resolved the target this frame we must recalculate the\n       * projection to ensure it visually represents the internal calculations.\n       */\n      if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n        canSkip = false;\n      }\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Record previous tree scales before updating.\n       */\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      /**\n       * If this layer needs to perform scale correction but doesn't have a target,\n       * use the layout as the target.\n       */\n      if (lead.layout && !lead.target && (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n        lead.target = lead.layout.layoutBox;\n        lead.targetWithTransforms = createBox();\n      }\n      const {\n        target\n      } = lead;\n      if (!target) {\n        /**\n         * If we don't have a target to project into, but we were previously\n         * projecting, we want to remove the stored transform and schedule\n         * a render to ensure the elements reflect the removed transform.\n         */\n        if (this.prevProjectionDelta) {\n          this.createProjectionDeltas();\n          this.scheduleRender();\n        }\n        return;\n      }\n      if (!this.projectionDelta || !this.prevProjectionDelta) {\n        this.createProjectionDeltas();\n      } else {\n        copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n        copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n      }\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      if (this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY || !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) || !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n      /**\n       * Increase debug counter for recalculated projections\n       */\n      if (statsBuffer.value) {\n        metrics.calculatedProjections++;\n      }\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender(notifyAll = true) {\n      this.options.visualElement?.scheduleRender();\n      if (notifyAll) {\n        const stack = this.getStack();\n        stack && stack.scheduleRender();\n      }\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    createProjectionDeltas() {\n      this.prevProjectionDelta = createDelta();\n      this.projectionDelta = createDelta();\n      this.projectionDeltaWithTransform = createDelta();\n    }\n    setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n      const mixedValues = {\n        ...this.latestValues\n      };\n      const targetDelta = createDelta();\n      if (!this.relativeParent || !this.relativeParent.options.layoutRoot) {\n        this.relativeTarget = this.relativeTargetOrigin = undefined;\n      }\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const snapshotSource = snapshot ? snapshot.source : undefined;\n      const layoutSource = this.layout ? this.layout.source : undefined;\n      const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n      const stack = this.getStack();\n      const isOnlyMember = !stack || stack.members.length <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      let prevRelativeTarget;\n      this.mixTargetDelta = latest => {\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n          /**\n           * If this is an unchanged relative target we can consider the\n           * projection not dirty.\n           */\n          if (prevRelativeTarget && boxEquals(this.relativeTarget, prevRelativeTarget)) {\n            this.isProjectionDirty = false;\n          }\n          if (!prevRelativeTarget) prevRelativeTarget = createBox();\n          copyBoxInto(prevRelativeTarget, this.relativeTarget);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n    }\n    startAnimation(options) {\n      this.notifyListeners(\"animationStart\");\n      this.currentAnimation?.stop();\n      this.resumingFrom?.currentAnimation?.stop();\n      if (this.pendingAnimation) {\n        cancelFrame(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = frame.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        activeAnimations.layout++;\n        this.motionValue || (this.motionValue = motionValue(0));\n        this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], {\n          ...options,\n          velocity: 0,\n          isSync: true,\n          onUpdate: latest => {\n            this.mixTargetDelta(latest);\n            options.onUpdate && options.onUpdate(latest);\n          },\n          onStop: () => {\n            activeAnimations.layout--;\n          },\n          onComplete: () => {\n            activeAnimations.layout--;\n            options.onComplete && options.onComplete();\n            this.completeAnimation();\n          }\n        });\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      const stack = this.getStack();\n      stack && stack.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      if (this.currentAnimation) {\n        this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      const config = node.options.initialPromotionConfig;\n      node.promote({\n        transition: config ? config.transition : undefined,\n        preserveFollowOpacity: config && config.shouldPreserveFollowOpacity ? config.shouldPreserveFollowOpacity(node) : undefined\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? this.getStack()?.lead || this : this;\n    }\n    getPrevLead() {\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? this.getStack()?.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote({\n      needsReset,\n      transition,\n      preserveFollowOpacity\n    } = {}) {\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetSkewAndRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected skew or rotation values, we can early return without a forced render.\n      let hasDistortingTransform = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.z || latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ || latestValues.skewX || latestValues.skewY) {\n        hasDistortingTransform = true;\n      }\n      // If there's no distorting values, we don't need to do any more.\n      if (!hasDistortingTransform) return;\n      const resetValues = {};\n      if (latestValues.z) {\n        resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n      }\n      // Check the skew and rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n        resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n      }\n      // Force a render of this element to apply the transform with all skews and rotations\n      // set to 0.\n      visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n        if (this.animationValues) {\n          this.animationValues[key] = resetValues[key];\n        }\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp) {\n      if (!this.instance || this.isSVG) return undefined;\n      if (!this.isVisible) {\n        return hiddenVisibility;\n      }\n      const styles = {\n        visibility: \"\"\n      };\n      const transformTemplate = this.getTransformTemplate();\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp?.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp?.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity ?? this.latestValues.opacity ?? 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo,\n          isCSSVariable\n        } = scaleCorrectors[key];\n        /**\n         * Only apply scale correction to the value if we have an\n         * active projection transform. Otherwise these values become\n         * vulnerable to distortion if the element changes size without\n         * a corresponding layout animation.\n         */\n        const corrected = styles.transform === \"none\" ? valuesToRender[key] : correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          // If this is a CSS variable, set it directly on the instance.\n          // Replacing this function from creating styles to setting them\n          // would be a good place to remove per frame object creation\n          if (isCSSVariable) {\n            this.options.visualElement.renderState.vars[key] = corrected;\n          } else {\n            styles[key] = corrected;\n          }\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp?.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => node.currentAnimation?.stop());\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  const snapshot = node.resumeFrom?.snapshot || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n        /**\n         * Ensure relative target gets resized and rerendererd\n         */\n        if (node.relativeTarget && !node.currentAnimation) {\n          node.isProjectionDirty = true;\n          node.relativeTarget[axis].max = node.relativeTarget[axis].min + length;\n        }\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeLayoutChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n            hasRelativeLayoutChanged = true;\n          }\n          if (relativeParent.options.layoutRoot) {\n            node.relativeTarget = relativeLayout;\n            node.relativeTargetOrigin = relativeSnapshot;\n            node.relativeParent = relativeParent;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeLayoutChanged\n    });\n  } else if (node.isLead()) {\n    const {\n      onExitComplete\n    } = node.options;\n    onExitComplete && onExitComplete();\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Increase debug counter for nodes encountered this frame\n   */\n  if (statsBuffer.value) {\n    metrics.nodes++;\n  }\n  if (!node.parent) return;\n  /**\n   * If this node isn't projecting, propagate isProjectionDirty. It will have\n   * no performance impact but it will allow the next child that *is* projecting\n   * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n   * correcting.\n   */\n  if (!node.isProjecting()) {\n    node.isProjectionDirty = node.parent.isProjectionDirty;\n  }\n  /**\n   * Propagate isSharedProjectionDirty and isTransformDirty\n   * throughout the whole tree. A future revision can take another look at\n   * this but for safety we still recalcualte shared nodes.\n   */\n  node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty || node.parent.isProjectionDirty || node.parent.isSharedProjectionDirty));\n  node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n  node.isProjectionDirty = node.isSharedProjectionDirty = node.isTransformDirty = false;\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n  node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n  node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n  node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mixNumber(delta.translate, 0, p);\n  output.scale = mixNumber(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mixNumber(from.min, to.min, p);\n  output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nconst userAgentContains = string => typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\") ? Math.round : noop;\nfunction roundAxis(axis) {\n  // Round to the nearest .5 pixels to support subpixel layouts\n  axis.min = roundPoint(axis.min);\n  axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nfunction checkNodeWasScrollRoot(node) {\n  return node !== node.root && node.scroll?.wasRoot;\n}\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["statsBuffer", "isSVGElement", "isSVGSVGElement", "getValueTransition", "cancelFrame", "time", "frameData", "frameSteps", "microtask", "frame", "activeAnimations", "motionValue", "mixNumber", "SubscriptionManager", "clamp", "noop", "animateSingleValue", "getOptimisedAppearId", "FlatTree", "delay", "resolveMotionValue", "mixValues", "copyBoxInto", "copyAxisDeltaInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcLength", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "boxEqualsRounded", "isDeltaZero", "axisDeltaEquals", "aspectRatio", "boxEquals", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "globalProjectionState", "metrics", "nodes", "calculatedTargetDeltas", "calculatedProjections", "transformAxes", "hiddenVisibility", "visibility", "animationTarget", "id", "resetDistortingTransform", "key", "visualElement", "values", "sharedAnimationValues", "latestValues", "setStaticValue", "cancelTreeOptimisedTransformAnimations", "projectionNode", "hasCheckedOptimisedAppear", "root", "options", "appearId", "window", "MotionHasOptimisedAnimation", "layout", "layoutId", "MotionCancelOptimisedAnimation", "parent", "createProjectionNode", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "animationId", "children", "Set", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isProjectionDirty", "isSharedProjectionDirty", "isTransformDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "hasTreeAnimated", "updateScheduled", "scheduleUpdate", "update", "projectionUpdateScheduled", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "value", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "cleanDirtyNodes", "addProjectionMetrics", "resolvedRelativeTargetAt", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "path", "depth", "i", "length", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "args", "subscriptionManager", "notify", "hasListeners", "mount", "instance", "current", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "animate", "delta", "hasLayoutChanged", "hasRelativeLayoutChanged", "newLayout", "isTreeAnimationBlocked", "target", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "hasTargetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "layoutRoot", "resumeFrom", "currentAnimation", "resumingFrom", "animationOptions", "onPlay", "onComplete", "shouldReduceMotion", "type", "startAnimation", "setAnimationOrigin", "isLead", "onExitComplete", "unmount", "willUpdate", "remove", "stack", "getStack", "delete", "clear", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetSkewAndRotation", "getTransformTemplate", "transformTemplate", "shouldNotifyListeners", "node", "updateScroll", "prevTransformTemplateValue", "updateSnapshot", "updateWasBlocked", "clearMeasurements", "clearIsLayoutDirty", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "now", "timestamp", "isProcessing", "process", "preRender", "render", "didUpdate", "read", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "measuredBox", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "wasRoot", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "source", "box", "measureViewportBox", "wasInScrollRoot", "some", "checkNodeWasScrollRoot", "boxWithoutScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "forceRelativeParentToResolveTarget", "relativeParent", "forceRecalculation", "lead", "getLead", "isShared", "canSkip", "attemptToResolveRelativeTarget", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isProjecting", "pendingAnimation", "prevTreeScaleX", "prevTreeScaleY", "prevProjectionDelta", "createProjectionDeltas", "hide", "show", "notifyAll", "projectionDeltaWithTransform", "snapshotLatestValues", "mixedValues", "relativeLayout", "snapshotSource", "layoutSource", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "hasOpacityCrossfade", "prevRelativeTarget", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "velocity", "isSync", "onUpdate", "onStop", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "config", "initialPromotionConfig", "promote", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasDistortingTransform", "z", "rotate", "rotateX", "rotateY", "rotateZ", "skewX", "skewY", "resetValues", "getProjectionStyles", "styleProp", "styles", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "origin", "opacityExit", "correct", "applyTo", "isCSSVariable", "corrected", "num", "renderState", "vars", "resetTree", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "userAgentContains", "string", "navigator", "userAgent", "toLowerCase", "includes", "roundPoint", "Math", "round", "roundAxis"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { statsBuffer, isSVGElement, isSVGSVGElement, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, frame, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\n\nconst metrics = {\n    nodes: 0,\n    calculatedTargetDeltas: 0,\n    calculatedProjections: 0,\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n    const { latestValues } = visualElement;\n    // Record the distorting transform and then temporarily set it to 0\n    if (latestValues[key]) {\n        values[key] = latestValues[key];\n        visualElement.setStaticValue(key, 0);\n        if (sharedAnimationValues) {\n            sharedAnimationValues[key] = 0;\n        }\n    }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n    projectionNode.hasCheckedOptimisedAppear = true;\n    if (projectionNode.root === projectionNode)\n        return;\n    const { visualElement } = projectionNode.options;\n    if (!visualElement)\n        return;\n    const appearId = getOptimisedAppearId(visualElement);\n    if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n        const { layout, layoutId } = projectionNode.options;\n        window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n    }\n    const { parent } = projectionNode;\n    if (parent && !parent.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(parent);\n    }\n}\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent?.()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * Store whether this node has been checked for optimised appear animations. As\n             * effects fire bottom-up, and we want to look up the tree for appear animations,\n             * this makes sure we only check each path once, stopping at nodes that\n             * have already been checked.\n             */\n            this.hasCheckedOptimisedAppear = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.scheduleUpdate = () => this.update();\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                if (statsBuffer.value) {\n                    metrics.nodes =\n                        metrics.calculatedTargetDeltas =\n                            metrics.calculatedProjections =\n                                0;\n                }\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                if (statsBuffer.addProjectionMetrics) {\n                    statsBuffer.addProjectionMetrics(metrics);\n                }\n            };\n            /**\n             * Frame calculations\n             */\n            this.resolvedRelativeTargetAt = 0.0;\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (this.root.hasTreeAnimated && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeLayoutChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const hasTargetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout);\n                    /*\n                     * Note: Disabled to fix relative animations always triggering new\n                     * layout animations. If this causes further issues, we can try\n                     * a different approach to detecting relative target changes.\n                     */\n                    // || hasRelativeLayoutChanged\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n                    if (this.options.layoutRoot ||\n                        this.resumeFrom ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (hasTargetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                        /**\n                         * Set animation origin after starting animation to avoid layout jump\n                         * caused by stopping previous layout animation\n                         */\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            this.eventHandlers.clear();\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetSkewAndRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            /**\n             * If we're running optimised appear animations then these must be\n             * cancelled before measuring the DOM. This is so we can measure\n             * the true layout of the element rather than the WAAPI animation\n             * which will be unaffected by the resetSkewAndRotate step.\n             *\n             * Note: This is a DOM write. Worst case scenario is this is sandwiched\n             * between other snapshot reads which will cause unnecessary style recalculations.\n             * This has to happen here though, as we don't yet know which nodes will need\n             * snapshots in startUpdate(), but we only want to cancel optimised animations\n             * if a layout animation measurement is actually going to be affected by them.\n             */\n            if (window.MotionCancelOptimisedAnimation &&\n                !this.hasCheckedOptimisedAppear) {\n                cancelTreeOptimisedTransformAnimations(this);\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = time.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            frameSteps.update.process(frameData);\n            frameSteps.preRender.process(frameData);\n            frameSteps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                microtask.read(this.scheduleUpdate);\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n            if (this.snapshot &&\n                !calcLength(this.snapshot.measuredBox.x) &&\n                !calcLength(this.snapshot.measuredBox.y)) {\n                this.snapshot = undefined;\n            }\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement && this.instance) {\n                const isRoot = checkIsScrollRoot(this.instance);\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot,\n                    offset: measureScroll(this.instance),\n                    wasRoot: this.scroll ? this.scroll.isRoot : isRoot,\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty ||\n                this.shouldResetTransform ||\n                this.options.alwaysMeasureLayout;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                this.instance &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            const wasInScrollRoot = this.scroll?.wasRoot || this.path.some(checkNodeWasScrollRoot);\n            if (!wasInScrollRoot) {\n                // Remove viewport scroll to give page-relative coordinates\n                const { scroll } = this.root;\n                if (scroll) {\n                    translateAxis(box.x, scroll.offset.x);\n                    translateAxis(box.y, scroll.offset.y);\n                }\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            if (this.scroll?.wasRoot) {\n                return boxWithoutScroll;\n            }\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.wasRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                this.parent?.isProjectionDirty ||\n                this.attemptToResolveRelativeTarget ||\n                this.root.updateBlockedByResize);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            if (!this.targetDelta && !this.relativeTarget) {\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedTargetDeltas++;\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || this.parent?.isProjectionDirty) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n                lead.targetWithTransforms = createBox();\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.prevProjectionDelta) {\n                    this.createProjectionDeltas();\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta || !this.prevProjectionDelta) {\n                this.createProjectionDeltas();\n            }\n            else {\n                copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n                copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n            }\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            if (this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY ||\n                !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) ||\n                !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedProjections++;\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.visualElement?.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        createProjectionDeltas() {\n            this.prevProjectionDelta = createDelta();\n            this.projectionDelta = createDelta();\n            this.projectionDeltaWithTransform = createDelta();\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation?.stop();\n            this.resumingFrom?.currentAnimation?.stop();\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                activeAnimations.layout++;\n                this.motionValue || (this.motionValue = motionValue(0));\n                this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], {\n                    ...options,\n                    velocity: 0,\n                    isSync: true,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onStop: () => {\n                        activeAnimations.layout--;\n                    },\n                    onComplete: () => {\n                        activeAnimations.layout--;\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.lead || this : this;\n        }\n        getPrevLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetSkewAndRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected skew or rotation values, we can early return without a forced render.\n            let hasDistortingTransform = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.z ||\n                latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ ||\n                latestValues.skewX ||\n                latestValues.skewY) {\n                hasDistortingTransform = true;\n            }\n            // If there's no distorting values, we don't need to do any more.\n            if (!hasDistortingTransform)\n                return;\n            const resetValues = {};\n            if (latestValues.z) {\n                resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n            }\n            // Check the skew and rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n                resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n            }\n            // Force a render of this element to apply the transform with all skews and rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n                if (this.animationValues) {\n                    this.animationValues[key] = resetValues[key];\n                }\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity ??\n                            this.latestValues.opacity ??\n                            1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo, isCSSVariable } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    // If this is a CSS variable, set it directly on the instance.\n                    // Replacing this function from creating styles to setting them\n                    // would be a good place to remove per frame object creation\n                    if (isCSSVariable) {\n                        this.options.visualElement.renderState.vars[key] = corrected;\n                    }\n                    else {\n                        styles[key] = corrected;\n                    }\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp?.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => node.currentAnimation?.stop());\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    const snapshot = node.resumeFrom?.snapshot || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeLayoutChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeLayoutChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeLayoutChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    if (statsBuffer.value) {\n        metrics.nodes++;\n    }\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n    node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mixNumber(delta.translate, 0, p);\n    output.scale = mixNumber(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mixNumber(from.min, to.min, p);\n    output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\nfunction checkNodeWasScrollRoot(node) {\n    return node !== node.root && node.scroll?.wasRoot;\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AACjM,SAASC,mBAAmB,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAC/D,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AACrE,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,UAAU,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,QAAQ,uBAAuB;AAC9G,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,qBAAqB,QAAQ,aAAa;AAEnD,MAAMC,OAAO,GAAG;EACZC,KAAK,EAAE,CAAC;EACRC,sBAAsB,EAAE,CAAC;EACzBC,qBAAqB,EAAE;AAC3B,CAAC;AACD,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,gBAAgB,GAAG;EAAEC,UAAU,EAAE;AAAS,CAAC;AACjD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV,SAASC,wBAAwBA,CAACC,GAAG,EAAEC,aAAa,EAAEC,MAAM,EAAEC,qBAAqB,EAAE;EACjF,MAAM;IAAEC;EAAa,CAAC,GAAGH,aAAa;EACtC;EACA,IAAIG,YAAY,CAACJ,GAAG,CAAC,EAAE;IACnBE,MAAM,CAACF,GAAG,CAAC,GAAGI,YAAY,CAACJ,GAAG,CAAC;IAC/BC,aAAa,CAACI,cAAc,CAACL,GAAG,EAAE,CAAC,CAAC;IACpC,IAAIG,qBAAqB,EAAE;MACvBA,qBAAqB,CAACH,GAAG,CAAC,GAAG,CAAC;IAClC;EACJ;AACJ;AACA,SAASM,sCAAsCA,CAACC,cAAc,EAAE;EAC5DA,cAAc,CAACC,yBAAyB,GAAG,IAAI;EAC/C,IAAID,cAAc,CAACE,IAAI,KAAKF,cAAc,EACtC;EACJ,MAAM;IAAEN;EAAc,CAAC,GAAGM,cAAc,CAACG,OAAO;EAChD,IAAI,CAACT,aAAa,EACd;EACJ,MAAMU,QAAQ,GAAGrD,oBAAoB,CAAC2C,aAAa,CAAC;EACpD,IAAIW,MAAM,CAACC,2BAA2B,CAACF,QAAQ,EAAE,WAAW,CAAC,EAAE;IAC3D,MAAM;MAAEG,MAAM;MAAEC;IAAS,CAAC,GAAGR,cAAc,CAACG,OAAO;IACnDE,MAAM,CAACI,8BAA8B,CAACL,QAAQ,EAAE,WAAW,EAAE7D,KAAK,EAAE,EAAEgE,MAAM,IAAIC,QAAQ,CAAC,CAAC;EAC9F;EACA,MAAM;IAAEE;EAAO,CAAC,GAAGV,cAAc;EACjC,IAAIU,MAAM,IAAI,CAACA,MAAM,CAACT,yBAAyB,EAAE;IAC7CF,sCAAsC,CAACW,MAAM,CAAC;EAClD;AACJ;AACA,SAASC,oBAAoBA,CAAC;EAAEC,oBAAoB;EAAEC,aAAa;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC;AAAgB,CAAC,EAAE;EACtH,OAAO,MAAMC,cAAc,CAAC;IACxBC,WAAWA,CAACrB,YAAY,GAAG,CAAC,CAAC,EAAEa,MAAM,GAAGG,aAAa,GAAG,CAAC,EAAE;MACvD;AACZ;AACA;MACY,IAAI,CAACtB,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAAC4B,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAAClB,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACmB,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;AACA;MACY,IAAI,CAACC,uBAAuB,GAAG,KAAK;MACpC;AACZ;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChC,yBAAyB,GAAG,KAAK;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACiC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B;MACA,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,cAAc,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;MACzC,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACd,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACe,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC;AAChB;AACA;AACA;QACgB,IAAI7G,WAAW,CAACiH,KAAK,EAAE;UACnBhE,OAAO,CAACC,KAAK,GACTD,OAAO,CAACE,sBAAsB,GAC1BF,OAAO,CAACG,qBAAqB,GACzB,CAAC;QACjB;QACA,IAAI,CAACF,KAAK,CAACgE,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACjE,KAAK,CAACgE,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAAClE,KAAK,CAACgE,OAAO,CAACG,cAAc,CAAC;QAClC,IAAI,CAACnE,KAAK,CAACgE,OAAO,CAACI,eAAe,CAAC;QACnC,IAAItH,WAAW,CAACuH,oBAAoB,EAAE;UAClCvH,WAAW,CAACuH,oBAAoB,CAACtE,OAAO,CAAC;QAC7C;MACJ,CAAC;MACD;AACZ;AACA;MACY,IAAI,CAACuE,wBAAwB,GAAG,GAAG;MACnC,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIpB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACzC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACK,IAAI,GAAGQ,MAAM,GAAGA,MAAM,CAACR,IAAI,IAAIQ,MAAM,GAAG,IAAI;MACjD,IAAI,CAACiD,IAAI,GAAGjD,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACiD,IAAI,EAAEjD,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACkD,KAAK,GAAGlD,MAAM,GAAGA,MAAM,CAACkD,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,CAAC5B,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAAC/B,IAAI,KAAK,IAAI,EAClB,IAAI,CAAClB,KAAK,GAAG,IAAIhC,QAAQ,CAAC,CAAC;IACnC;IACA+G,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAAC5B,aAAa,CAAC6B,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAAC3B,aAAa,CAAC8B,GAAG,CAACH,IAAI,EAAE,IAAIrH,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAAC0F,aAAa,CAAC+B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAE,GAAGO,IAAI,EAAE;MAC3B,MAAMC,mBAAmB,GAAG,IAAI,CAACnC,aAAa,CAAC+B,GAAG,CAACJ,IAAI,CAAC;MACxDQ,mBAAmB,IAAIA,mBAAmB,CAACC,MAAM,CAAC,GAAGF,IAAI,CAAC;IAC9D;IACAG,YAAYA,CAACV,IAAI,EAAE;MACf,OAAO,IAAI,CAAC3B,aAAa,CAAC6B,GAAG,CAACF,IAAI,CAAC;IACvC;IACA;AACR;AACA;IACQW,KAAKA,CAACC,QAAQ,EAAE;MACZ,IAAI,IAAI,CAACA,QAAQ,EACb;MACJ,IAAI,CAAC7C,KAAK,GAAGhG,YAAY,CAAC6I,QAAQ,CAAC,IAAI,CAAC5I,eAAe,CAAC4I,QAAQ,CAAC;MACjE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEpE,QAAQ;QAAED,MAAM;QAAEb;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACxD,IAAIT,aAAa,IAAI,CAACA,aAAa,CAACmF,OAAO,EAAE;QACzCnF,aAAa,CAACiF,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAAC1E,IAAI,CAAClB,KAAK,CAACqF,GAAG,CAAC,IAAI,CAAC;MACzB,IAAI,CAAC3D,MAAM,IAAI,IAAI,CAACA,MAAM,CAACU,QAAQ,CAACiD,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAI,IAAI,CAACnE,IAAI,CAACqC,eAAe,KAAKhC,MAAM,IAAIC,QAAQ,CAAC,EAAE;QACnD,IAAI,CAACgB,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIZ,oBAAoB,EAAE;QACtB,IAAIkE,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAAC7E,IAAI,CAAC2B,qBAAqB,GAAG,KAAM;QAC3EjB,oBAAoB,CAACgE,QAAQ,EAAE,MAAM;UACjC,IAAI,CAAC1E,IAAI,CAAC2B,qBAAqB,GAAG,IAAI;UACtCiD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAG7H,KAAK,CAAC8H,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAIjG,qBAAqB,CAACkG,sBAAsB,EAAE;YAC9ClG,qBAAqB,CAACkG,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAAChG,KAAK,CAACgE,OAAO,CAACiC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIzE,QAAQ,EAAE;QACV,IAAI,CAACN,IAAI,CAACgF,kBAAkB,CAAC1E,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACL,OAAO,CAACgF,OAAO,KAAK,KAAK,IAC9BzF,aAAa,KACZc,QAAQ,IAAID,MAAM,CAAC,EAAE;QACtB,IAAI,CAACwD,gBAAgB,CAAC,WAAW,EAAE,CAAC;UAAEqB,KAAK;UAAEC,gBAAgB;UAAEC,wBAAwB;UAAE/E,MAAM,EAAEgF;QAAW,CAAC,KAAK;UAC9G,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAGC,SAAS;YACvB,IAAI,CAACC,cAAc,GAAGD,SAAS;YAC/B;UACJ;UACA;UACA,MAAME,gBAAgB,GAAG,IAAI,CAACzF,OAAO,CAAC0F,UAAU,IAC5CnG,aAAa,CAACoG,oBAAoB,CAAC,CAAC,IACpCC,uBAAuB;UAC3B,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAGvG,aAAa,CAACwG,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,gBAAgB,GAAG,CAAC,IAAI,CAACC,YAAY,IACvC,CAAClI,gBAAgB,CAAC,IAAI,CAACkI,YAAY,EAAEb,SAAS,CAAC;UACnD;AACpB;AACA;AACA;AACA;UACoB;UACA;AACpB;AACA;AACA;AACA;UACoB,MAAMc,4BAA4B,GAAG,CAAChB,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,IAAI,CAACnF,OAAO,CAACmG,UAAU,IACvB,IAAI,CAACC,UAAU,IACfF,4BAA4B,IAC3BhB,gBAAgB,KACZc,gBAAgB,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAAE,EAAE;YACnD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAGf,SAAS;YAC9C;YACA,MAAMgB,gBAAgB,GAAG;cACrB,GAAGzK,kBAAkB,CAAC2J,gBAAgB,EAAE,QAAQ,CAAC;cACjDe,MAAM,EAAEX,sBAAsB;cAC9BY,UAAU,EAAEX;YAChB,CAAC;YACD,IAAIvG,aAAa,CAACmH,kBAAkB,IAChC,IAAI,CAAC1G,OAAO,CAACmG,UAAU,EAAE;cACzBI,gBAAgB,CAACzJ,KAAK,GAAG,CAAC;cAC1ByJ,gBAAgB,CAACI,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACC,cAAc,CAACL,gBAAgB,CAAC;YACrC;AACxB;AACA;AACA;YACwB,IAAI,CAACM,kBAAkB,CAAC5B,KAAK,EAAEiB,4BAA4B,CAAC;UAChE,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAAChB,gBAAgB,EAAE;cACnBJ,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,IAAI,CAACgC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC9G,OAAO,CAAC+G,cAAc,EAAE;cAC9C,IAAI,CAAC/G,OAAO,CAAC+G,cAAc,CAAC,CAAC;YACjC;UACJ;UACA,IAAI,CAACd,YAAY,GAAGb,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACA4B,OAAOA,CAAA,EAAG;MACN,IAAI,CAAChH,OAAO,CAACK,QAAQ,IAAI,IAAI,CAAC4G,UAAU,CAAC,CAAC;MAC1C,IAAI,CAAClH,IAAI,CAAClB,KAAK,CAACqI,MAAM,CAAC,IAAI,CAAC;MAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAC3G,MAAM,IAAI,IAAI,CAACA,MAAM,CAACU,QAAQ,CAACoG,MAAM,CAAC,IAAI,CAAC;MAChD,IAAI,CAAC5C,QAAQ,GAAGc,SAAS;MACzB,IAAI,CAACrD,aAAa,CAACoF,KAAK,CAAC,CAAC;MAC1BvL,WAAW,CAAC,IAAI,CAAC4G,gBAAgB,CAAC;IACtC;IACA;IACA4E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC9F,qBAAqB,GAAG,IAAI;IACrC;IACA+F,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC/F,qBAAqB,GAAG,KAAK;IACtC;IACAgG,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAAChG,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACA2D,sBAAsBA,CAAA,EAAG;MACrB,OAAQ,IAAI,CAACjE,kBAAkB,IAC1B,IAAI,CAACb,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8E,sBAAsB,CAAC,CAAE,IACrD,KAAK;IACb;IACA;IACAqC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAAC9F,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC9C,KAAK,IAAI,IAAI,CAACA,KAAK,CAACgE,OAAO,CAAC8E,oBAAoB,CAAC;MACtD,IAAI,CAAC3G,WAAW,EAAE;IACtB;IACA4G,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAErI;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,OAAOT,aAAa,IAAIA,aAAa,CAACwG,QAAQ,CAAC,CAAC,CAAC8B,iBAAiB;IACtE;IACAZ,UAAUA,CAACa,qBAAqB,GAAG,IAAI,EAAE;MACrC,IAAI,CAAC/H,IAAI,CAACqC,eAAe,GAAG,IAAI;MAChC,IAAI,IAAI,CAACrC,IAAI,CAAC0H,eAAe,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACzH,OAAO,CAAC+G,cAAc,IAAI,IAAI,CAAC/G,OAAO,CAAC+G,cAAc,CAAC,CAAC;QAC5D;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI7G,MAAM,CAACI,8BAA8B,IACrC,CAAC,IAAI,CAACR,yBAAyB,EAAE;QACjCF,sCAAsC,CAAC,IAAI,CAAC;MAChD;MACA,CAAC,IAAI,CAACG,IAAI,CAAC4B,UAAU,IAAI,IAAI,CAAC5B,IAAI,CAAC2H,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACrG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzBqE,IAAI,CAACjG,oBAAoB,GAAG,IAAI;QAChCiG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAID,IAAI,CAAC/H,OAAO,CAACmG,UAAU,EAAE;UACzB4B,IAAI,CAACd,UAAU,CAAC,KAAK,CAAC;QAC1B;MACJ;MACA,MAAM;QAAE5G,QAAQ;QAAED;MAAO,CAAC,GAAG,IAAI,CAACJ,OAAO;MACzC,IAAIK,QAAQ,KAAKkF,SAAS,IAAI,CAACnF,MAAM,EACjC;MACJ,MAAMyH,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAACK,0BAA0B,GAAGJ,iBAAiB,GAC7CA,iBAAiB,CAAC,IAAI,CAACnI,YAAY,EAAE,EAAE,CAAC,GACxC6F,SAAS;MACf,IAAI,CAAC2C,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAC3D,eAAe,CAAC,YAAY,CAAC;IAC/D;IACA5B,MAAMA,CAAA,EAAG;MACL,IAAI,CAACF,eAAe,GAAG,KAAK;MAC5B,MAAM8F,gBAAgB,GAAG,IAAI,CAACV,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIU,gBAAgB,EAAE;QAClB,IAAI,CAACX,aAAa,CAAC,CAAC;QACpB,IAAI,CAAC9E,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAAC7D,KAAK,CAACgE,OAAO,CAACuF,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAACzG,UAAU,EAAE;QAClB,IAAI,CAAC9C,KAAK,CAACgE,OAAO,CAACwF,kBAAkB,CAAC;MAC1C;MACA,IAAI,CAAC1G,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAAC9C,KAAK,CAACgE,OAAO,CAACyF,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAACzJ,KAAK,CAACgE,OAAO,CAAC0F,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAAC1J,KAAK,CAACgE,OAAO,CAAC2F,kBAAkB,CAAC;MACtC,IAAI,CAAC9F,iBAAiB,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;MACY,MAAM+F,GAAG,GAAGzM,IAAI,CAACyM,GAAG,CAAC,CAAC;MACtBxM,SAAS,CAACgJ,KAAK,GAAGxI,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAEgM,GAAG,GAAGxM,SAAS,CAACyM,SAAS,CAAC;MAChEzM,SAAS,CAACyM,SAAS,GAAGD,GAAG;MACzBxM,SAAS,CAAC0M,YAAY,GAAG,IAAI;MAC7BzM,UAAU,CAACqG,MAAM,CAACqG,OAAO,CAAC3M,SAAS,CAAC;MACpCC,UAAU,CAAC2M,SAAS,CAACD,OAAO,CAAC3M,SAAS,CAAC;MACvCC,UAAU,CAAC4M,MAAM,CAACF,OAAO,CAAC3M,SAAS,CAAC;MACpCA,SAAS,CAAC0M,YAAY,GAAG,KAAK;IAClC;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC1G,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI;QAC3BlG,SAAS,CAAC6M,IAAI,CAAC,IAAI,CAAC1G,cAAc,CAAC;MACvC;IACJ;IACAI,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC7D,KAAK,CAACgE,OAAO,CAACoG,aAAa,CAAC;MACjC,IAAI,CAAC1F,WAAW,CAACV,OAAO,CAACqG,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAC3G,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrCpG,KAAK,CAACyM,SAAS,CAAC,IAAI,CAAClG,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;MACvD;IACJ;IACAyG,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYhN,KAAK,CAACiN,UAAU,CAAC,MAAM;QACnB,IAAI,IAAI,CAAChI,aAAa,EAAE;UACpB,IAAI,CAACtB,IAAI,CAACgJ,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAAChJ,IAAI,CAAC0C,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQyF,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACoB,QAAQ,IAAI,CAAC,IAAI,CAAC7E,QAAQ,EAC/B;MACJ,IAAI,CAAC6E,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC9B,IAAI,IAAI,CAACD,QAAQ,IACb,CAAC/L,UAAU,CAAC,IAAI,CAAC+L,QAAQ,CAACE,WAAW,CAACxH,CAAC,CAAC,IACxC,CAACzE,UAAU,CAAC,IAAI,CAAC+L,QAAQ,CAACE,WAAW,CAACvH,CAAC,CAAC,EAAE;QAC1C,IAAI,CAACqH,QAAQ,GAAG/D,SAAS;MAC7B;IACJ;IACAgD,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAAC9D,QAAQ,EACd;MACJ;MACA,IAAI,CAACuD,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAAChI,OAAO,CAACyJ,mBAAmB,IAAI,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAACzF,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAC+E,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAAC3B,QAAQ,EAAE;QAC9C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;UACzBqE,IAAI,CAACC,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAM0B,UAAU,GAAG,IAAI,CAACtJ,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAACmJ,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACI,eAAe,GAAG9L,SAAS,CAAC,CAAC;MAClC,IAAI,CAACwD,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACuI,eAAe,GAAGrE,SAAS;MAChC,IAAI,CAACpB,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC/D,MAAM,CAACyJ,SAAS,CAAC;MACtD,MAAM;QAAEtK;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtCT,aAAa,IACTA,aAAa,CAAC+E,MAAM,CAAC,eAAe,EAAE,IAAI,CAAClE,MAAM,CAACyJ,SAAS,EAAEH,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAGtE,SAAS,CAAC;IACnH;IACAyC,YAAYA,CAAC8B,KAAK,GAAG,SAAS,EAAE;MAC5B,IAAIC,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAChK,OAAO,CAACiK,YAAY,IAAI,IAAI,CAACxF,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAACyF,MAAM,IACX,IAAI,CAACA,MAAM,CAAClJ,WAAW,KAAK,IAAI,CAACjB,IAAI,CAACiB,WAAW,IACjD,IAAI,CAACkJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,IAAI,IAAI,CAACtF,QAAQ,EAAE;QACnC,MAAM0F,MAAM,GAAGvJ,iBAAiB,CAAC,IAAI,CAAC6D,QAAQ,CAAC;QAC/C,IAAI,CAACyF,MAAM,GAAG;UACVlJ,WAAW,EAAE,IAAI,CAACjB,IAAI,CAACiB,WAAW;UAClC8I,KAAK;UACLK,MAAM;UACNC,MAAM,EAAEzJ,aAAa,CAAC,IAAI,CAAC8D,QAAQ,CAAC;UACpC4F,OAAO,EAAE,IAAI,CAACH,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,MAAM,GAAGA;QAChD,CAAC;MACL;IACJ;IACAtJ,cAAcA,CAAA,EAAG;MACb,IAAI,CAACA,cAAc,EACf;MACJ,MAAMyJ,gBAAgB,GAAG,IAAI,CAACjJ,aAAa,IACvC,IAAI,CAACS,oBAAoB,IACzB,IAAI,CAAC9B,OAAO,CAACyJ,mBAAmB;MACpC,MAAMc,aAAa,GAAG,IAAI,CAACX,eAAe,IAAI,CAAC5L,WAAW,CAAC,IAAI,CAAC4L,eAAe,CAAC;MAChF,MAAM/B,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,MAAM4C,sBAAsB,GAAG3C,iBAAiB,GAC1CA,iBAAiB,CAAC,IAAI,CAACnI,YAAY,EAAE,EAAE,CAAC,GACxC6F,SAAS;MACf,MAAMkF,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAACvC,0BAA0B;MAC9F,IAAIqC,gBAAgB,IAChB,IAAI,CAAC7F,QAAQ,KACZ8F,aAAa,IACV/L,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,IAC/B+K,2BAA2B,CAAC,EAAE;QAClC5J,cAAc,CAAC,IAAI,CAAC4D,QAAQ,EAAE+F,sBAAsB,CAAC;QACrD,IAAI,CAAC1I,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC4I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAnB,OAAOA,CAACoB,eAAe,GAAG,IAAI,EAAE;MAC5B,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIhB,SAAS,GAAG,IAAI,CAACiB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBd,SAAS,GAAG,IAAI,CAACc,eAAe,CAACd,SAAS,CAAC;MAC/C;MACAkB,QAAQ,CAAClB,SAAS,CAAC;MACnB,OAAO;QACH7I,WAAW,EAAE,IAAI,CAACjB,IAAI,CAACiB,WAAW;QAClCwI,WAAW,EAAEoB,OAAO;QACpBf,SAAS;QACTnK,YAAY,EAAE,CAAC,CAAC;QAChBsL,MAAM,EAAE,IAAI,CAAC5L;MACjB,CAAC;IACL;IACAyL,cAAcA,CAAA,EAAG;MACb,MAAM;QAAEtL;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,IAAI,CAACT,aAAa,EACd,OAAO1B,SAAS,CAAC,CAAC;MACtB,MAAMoN,GAAG,GAAG1L,aAAa,CAAC2L,kBAAkB,CAAC,CAAC;MAC9C,MAAMC,eAAe,GAAG,IAAI,CAACjB,MAAM,EAAEG,OAAO,IAAI,IAAI,CAAC7G,IAAI,CAAC4H,IAAI,CAACC,sBAAsB,CAAC;MACtF,IAAI,CAACF,eAAe,EAAE;QAClB;QACA,MAAM;UAAEjB;QAAO,CAAC,GAAG,IAAI,CAACnK,IAAI;QAC5B,IAAImK,MAAM,EAAE;UACR/M,aAAa,CAAC8N,GAAG,CAACjJ,CAAC,EAAEkI,MAAM,CAACE,MAAM,CAACpI,CAAC,CAAC;UACrC7E,aAAa,CAAC8N,GAAG,CAAChJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;QACzC;MACJ;MACA,OAAOgJ,GAAG;IACd;IACAH,mBAAmBA,CAACG,GAAG,EAAE;MACrB,MAAMK,gBAAgB,GAAGzN,SAAS,CAAC,CAAC;MACpCZ,WAAW,CAACqO,gBAAgB,EAAEL,GAAG,CAAC;MAClC,IAAI,IAAI,CAACf,MAAM,EAAEG,OAAO,EAAE;QACtB,OAAOiB,gBAAgB;MAC3B;MACA;AACZ;AACA;AACA;MACY,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,MAAM;UAAEwG,MAAM;UAAElK;QAAQ,CAAC,GAAG+H,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAAChI,IAAI,IAAImK,MAAM,IAAIlK,OAAO,CAACiK,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACG,OAAO,EAAE;YAChBpN,WAAW,CAACqO,gBAAgB,EAAEL,GAAG,CAAC;UACtC;UACA9N,aAAa,CAACmO,gBAAgB,CAACtJ,CAAC,EAAEkI,MAAM,CAACE,MAAM,CAACpI,CAAC,CAAC;UAClD7E,aAAa,CAACmO,gBAAgB,CAACrJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOqJ,gBAAgB;IAC3B;IACAC,cAAcA,CAACN,GAAG,EAAEO,aAAa,GAAG,KAAK,EAAE;MACvC,MAAMC,cAAc,GAAG5N,SAAS,CAAC,CAAC;MAClCZ,WAAW,CAACwO,cAAc,EAAER,GAAG,CAAC;MAChC,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAAC8H,aAAa,IACdzD,IAAI,CAAC/H,OAAO,CAACiK,YAAY,IACzBlC,IAAI,CAACmC,MAAM,IACXnC,IAAI,KAAKA,IAAI,CAAChI,IAAI,EAAE;UACpB3C,YAAY,CAACqO,cAAc,EAAE;YACzBzJ,CAAC,EAAE,CAAC+F,IAAI,CAACmC,MAAM,CAACE,MAAM,CAACpI,CAAC;YACxBC,CAAC,EAAE,CAAC8F,IAAI,CAACmC,MAAM,CAACE,MAAM,CAACnI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAACzD,YAAY,CAACuJ,IAAI,CAACrI,YAAY,CAAC,EAChC;QACJtC,YAAY,CAACqO,cAAc,EAAE1D,IAAI,CAACrI,YAAY,CAAC;MACnD;MACA,IAAIlB,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;QACjCtC,YAAY,CAACqO,cAAc,EAAE,IAAI,CAAC/L,YAAY,CAAC;MACnD;MACA,OAAO+L,cAAc;IACzB;IACAd,eAAeA,CAACM,GAAG,EAAE;MACjB,MAAMS,mBAAmB,GAAG7N,SAAS,CAAC,CAAC;MACvCZ,WAAW,CAACyO,mBAAmB,EAAET,GAAG,CAAC;MACrC,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMqE,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACqE,IAAI,CAACtD,QAAQ,EACd;QACJ,IAAI,CAACjG,YAAY,CAACuJ,IAAI,CAACrI,YAAY,CAAC,EAChC;QACJjB,QAAQ,CAACsJ,IAAI,CAACrI,YAAY,CAAC,IAAIqI,IAAI,CAACG,cAAc,CAAC,CAAC;QACpD,MAAMyD,SAAS,GAAG9N,SAAS,CAAC,CAAC;QAC7B,MAAM+N,OAAO,GAAG7D,IAAI,CAAC8C,cAAc,CAAC,CAAC;QACrC5N,WAAW,CAAC0O,SAAS,EAAEC,OAAO,CAAC;QAC/BhO,mBAAmB,CAAC8N,mBAAmB,EAAE3D,IAAI,CAACrI,YAAY,EAAEqI,IAAI,CAACuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ,CAACO,SAAS,GAAGtE,SAAS,EAAEoG,SAAS,CAAC;MAC/H;MACA,IAAInN,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;QACjC9B,mBAAmB,CAAC8N,mBAAmB,EAAE,IAAI,CAAChM,YAAY,CAAC;MAC/D;MACA,OAAOgM,mBAAmB;IAC9B;IACAG,cAAcA,CAAC5G,KAAK,EAAE;MAClB,IAAI,CAAC6G,WAAW,GAAG7G,KAAK;MACxB,IAAI,CAAClF,IAAI,CAACoJ,wBAAwB,CAAC,CAAC;MACpC,IAAI,CAAC7H,iBAAiB,GAAG,IAAI;IACjC;IACAyK,UAAUA,CAAC/L,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG;QACX,GAAG,IAAI,CAACA,OAAO;QACf,GAAGA,OAAO;QACVgM,SAAS,EAAEhM,OAAO,CAACgM,SAAS,KAAKzG,SAAS,GAAGvF,OAAO,CAACgM,SAAS,GAAG;MACrE,CAAC;IACL;IACA5D,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC8B,MAAM,GAAG3E,SAAS;MACvB,IAAI,CAACnF,MAAM,GAAGmF,SAAS;MACvB,IAAI,CAAC+D,QAAQ,GAAG/D,SAAS;MACzB,IAAI,CAAC0C,0BAA0B,GAAG1C,SAAS;MAC3C,IAAI,CAACuG,WAAW,GAAGvG,SAAS;MAC5B,IAAI,CAACD,MAAM,GAAGC,SAAS;MACvB,IAAI,CAAClE,aAAa,GAAG,KAAK;IAC9B;IACA4K,kCAAkCA,CAAA,EAAG;MACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EACpB;MACJ;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACA,cAAc,CAAC/I,wBAAwB,KAC5ClH,SAAS,CAACyM,SAAS,EAAE;QACrB,IAAI,CAACwD,cAAc,CAACnJ,kBAAkB,CAAC,IAAI,CAAC;MAChD;IACJ;IACAA,kBAAkBA,CAACoJ,kBAAkB,GAAG,KAAK,EAAE;MAC3C;AACZ;AACA;AACA;AACA;MACY,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC/K,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAG8K,IAAI,CAAC9K,iBAAiB,CAAC;MAC3E,IAAI,CAACE,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG4K,IAAI,CAAC5K,gBAAgB,CAAC;MACxE,IAAI,CAACD,uBAAuB,KAAK,IAAI,CAACA,uBAAuB,GAAG6K,IAAI,CAAC7K,uBAAuB,CAAC;MAC7F,MAAM+K,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D;AACZ;AACA;AACA;MACY,MAAMG,OAAO,GAAG,EAAEJ,kBAAkB,IAC/BG,QAAQ,IAAI,IAAI,CAAC/K,uBAAwB,IAC1C,IAAI,CAACD,iBAAiB,IACtB,IAAI,CAACf,MAAM,EAAEe,iBAAiB,IAC9B,IAAI,CAACkL,8BAA8B,IACnC,IAAI,CAACzM,IAAI,CAAC2B,qBAAqB,CAAC;MACpC,IAAI6K,OAAO,EACP;MACJ,MAAM;QAAEnM,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACI,MAAM,IAAI,EAAEA,MAAM,IAAIC,QAAQ,CAAC,EACrC;MACJ,IAAI,CAAC8C,wBAAwB,GAAGlH,SAAS,CAACyM,SAAS;MACnD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACoD,WAAW,IAAI,CAAC,IAAI,CAACtG,cAAc,EAAE;QAC3C,MAAM0G,cAAc,GAAG,IAAI,CAACO,0BAA0B,CAAC,CAAC;QACxD,IAAIP,cAAc,IACdA,cAAc,CAAC9L,MAAM,IACrB,IAAI,CAACkD,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC4I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACzG,cAAc,GAAG3H,SAAS,CAAC,CAAC;UACjC,IAAI,CAAC6O,oBAAoB,GAAG7O,SAAS,CAAC,CAAC;UACvCL,oBAAoB,CAAC,IAAI,CAACkP,oBAAoB,EAAE,IAAI,CAACtM,MAAM,CAACyJ,SAAS,EAAEqC,cAAc,CAAC9L,MAAM,CAACyJ,SAAS,CAAC;UACvG5M,WAAW,CAAC,IAAI,CAACuI,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACR,cAAc,GAAG,IAAI,CAAC1G,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAACsG,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACxG,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAGzH,SAAS,CAAC,CAAC;QACzB,IAAI,CAAC8O,oBAAoB,GAAG9O,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC2H,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAACR,cAAc,IACnB,IAAI,CAACA,cAAc,CAAC5G,MAAM,EAAE;QAC5B,IAAI,CAAC2G,kCAAkC,CAAC,CAAC;QACzCxO,eAAe,CAAC,IAAI,CAAC6H,MAAM,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAAC0G,cAAc,CAAC5G,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAACwG,WAAW,EAAE;QACvB,IAAI9B,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACiG,cAAc,CAAC,IAAI,CAACnL,MAAM,CAACyJ,SAAS,CAAC;QAC5D,CAAC,MACI;UACD5M,WAAW,CAAC,IAAI,CAACqI,MAAM,EAAE,IAAI,CAAClF,MAAM,CAACyJ,SAAS,CAAC;QACnD;QACAxM,aAAa,CAAC,IAAI,CAACiI,MAAM,EAAE,IAAI,CAACwG,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgB7O,WAAW,CAAC,IAAI,CAACqI,MAAM,EAAE,IAAI,CAAClF,MAAM,CAACyJ,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC2C,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMN,cAAc,GAAG,IAAI,CAACO,0BAA0B,CAAC,CAAC;QACxD,IAAIP,cAAc,IACdlC,OAAO,CAACkC,cAAc,CAAC5F,YAAY,CAAC,KAChC0D,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAC9B,CAAC4F,cAAc,CAAClM,OAAO,CAACiK,YAAY,IACpCiC,cAAc,CAAC5G,MAAM,IACrB,IAAI,CAAChC,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC4I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACzG,cAAc,GAAG3H,SAAS,CAAC,CAAC;UACjC,IAAI,CAAC6O,oBAAoB,GAAG7O,SAAS,CAAC,CAAC;UACvCL,oBAAoB,CAAC,IAAI,CAACkP,oBAAoB,EAAE,IAAI,CAACpH,MAAM,EAAE4G,cAAc,CAAC5G,MAAM,CAAC;UACnFrI,WAAW,CAAC,IAAI,CAACuI,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACR,cAAc,GAAG,IAAI,CAAC1G,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;MACY,IAAI5J,WAAW,CAACiH,KAAK,EAAE;QACnBhE,OAAO,CAACE,sBAAsB,EAAE;MACpC;IACJ;IACA2N,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAClM,MAAM,IACZ9B,QAAQ,CAAC,IAAI,CAAC8B,MAAM,CAACb,YAAY,CAAC,IAClChB,cAAc,CAAC,IAAI,CAAC6B,MAAM,CAACb,YAAY,CAAC,EAAE;QAC1C,OAAO6F,SAAS;MACpB;MACA,IAAI,IAAI,CAAChF,MAAM,CAACqM,YAAY,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAACrM,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAACkM,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAG,YAAYA,CAAA,EAAG;MACX,OAAO5C,OAAO,CAAC,CAAC,IAAI,CAACxE,cAAc,IAC/B,IAAI,CAACsG,WAAW,IAChB,IAAI,CAAC9L,OAAO,CAACmG,UAAU,KACvB,IAAI,CAAC/F,MAAM,CAAC;IACpB;IACA4C,cAAcA,CAAA,EAAG;MACb,MAAMoJ,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMC,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACjL,iBAAiB,IAAI,IAAI,CAACf,MAAM,EAAEe,iBAAiB,EAAE;QAC1DiL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAID,QAAQ,KACP,IAAI,CAAC/K,uBAAuB,IAAI,IAAI,CAACC,gBAAgB,CAAC,EAAE;QACzD+K,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACpJ,wBAAwB,KAAKlH,SAAS,CAACyM,SAAS,EAAE;QACvD6D,OAAO,GAAG,KAAK;MACnB;MACA,IAAIA,OAAO,EACP;MACJ,MAAM;QAAEnM,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACmB,eAAe,GAAG6I,OAAO,CAAE,IAAI,CAACzJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACY,eAAe,IACtE,IAAI,CAACkF,gBAAgB,IACrB,IAAI,CAACwG,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAAC1L,eAAe,EAAE;QACvB,IAAI,CAAC2K,WAAW,GAAG,IAAI,CAACtG,cAAc,GAAGD,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAACnF,MAAM,IAAI,EAAEA,MAAM,IAAIC,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACYpD,WAAW,CAAC,IAAI,CAAC0M,eAAe,EAAE,IAAI,CAACvJ,MAAM,CAACyJ,SAAS,CAAC;MACxD;AACZ;AACA;MACY,MAAMiD,cAAc,GAAG,IAAI,CAAC/K,SAAS,CAACC,CAAC;MACvC,MAAM+K,cAAc,GAAG,IAAI,CAAChL,SAAS,CAACE,CAAC;MACvC;AACZ;AACA;AACA;MACY3E,eAAe,CAAC,IAAI,CAACqM,eAAe,EAAE,IAAI,CAAC5H,SAAS,EAAE,IAAI,CAACyB,IAAI,EAAE8I,QAAQ,CAAC;MAC1E;AACZ;AACA;AACA;MACY,IAAIF,IAAI,CAAChM,MAAM,IACX,CAACgM,IAAI,CAAC9G,MAAM,KACX,IAAI,CAACvD,SAAS,CAACC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,EAAE;QACpDmK,IAAI,CAAC9G,MAAM,GAAG8G,IAAI,CAAChM,MAAM,CAACyJ,SAAS;QACnCuC,IAAI,CAACO,oBAAoB,GAAG9O,SAAS,CAAC,CAAC;MAC3C;MACA,MAAM;QAAEyH;MAAO,CAAC,GAAG8G,IAAI;MACvB,IAAI,CAAC9G,MAAM,EAAE;QACT;AAChB;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAAC0H,mBAAmB,EAAE;UAC1B,IAAI,CAACC,sBAAsB,CAAC,CAAC;UAC7B,IAAI,CAACvC,cAAc,CAAC,CAAC;QACzB;QACA;MACJ;MACA,IAAI,CAAC,IAAI,CAACd,eAAe,IAAI,CAAC,IAAI,CAACoD,mBAAmB,EAAE;QACpD,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACjC,CAAC,MACI;QACD/P,iBAAiB,CAAC,IAAI,CAAC8P,mBAAmB,CAAChL,CAAC,EAAE,IAAI,CAAC4H,eAAe,CAAC5H,CAAC,CAAC;QACrE9E,iBAAiB,CAAC,IAAI,CAAC8P,mBAAmB,CAAC/K,CAAC,EAAE,IAAI,CAAC2H,eAAe,CAAC3H,CAAC,CAAC;MACzE;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYvE,YAAY,CAAC,IAAI,CAACkM,eAAe,EAAE,IAAI,CAACD,eAAe,EAAErE,MAAM,EAAE,IAAI,CAAC5F,YAAY,CAAC;MACnF,IAAI,IAAI,CAACqC,SAAS,CAACC,CAAC,KAAK8K,cAAc,IACnC,IAAI,CAAC/K,SAAS,CAACE,CAAC,KAAK8K,cAAc,IACnC,CAAC9O,eAAe,CAAC,IAAI,CAAC2L,eAAe,CAAC5H,CAAC,EAAE,IAAI,CAACgL,mBAAmB,CAAChL,CAAC,CAAC,IACpE,CAAC/D,eAAe,CAAC,IAAI,CAAC2L,eAAe,CAAC3H,CAAC,EAAE,IAAI,CAAC+K,mBAAmB,CAAC/K,CAAC,CAAC,EAAE;QACtE,IAAI,CAACmB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACsH,cAAc,CAAC,CAAC;QACrB,IAAI,CAACvG,eAAe,CAAC,kBAAkB,EAAEmB,MAAM,CAAC;MACpD;MACA;AACZ;AACA;MACY,IAAI3J,WAAW,CAACiH,KAAK,EAAE;QACnBhE,OAAO,CAACG,qBAAqB,EAAE;MACnC;IACJ;IACAmO,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC7J,SAAS,GAAG,KAAK;MACtB;IACJ;IACA8J,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC9J,SAAS,GAAG,IAAI;MACrB;IACJ;IACAqH,cAAcA,CAAC0C,SAAS,GAAG,IAAI,EAAE;MAC7B,IAAI,CAACpN,OAAO,CAACT,aAAa,EAAEmL,cAAc,CAAC,CAAC;MAC5C,IAAI0C,SAAS,EAAE;QACX,MAAMjG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7BD,KAAK,IAAIA,KAAK,CAACuD,cAAc,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACpE,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAC7B,QAAQ,EAAE;QAClD,IAAI,CAAC6B,YAAY,GAAGf,SAAS;MACjC;IACJ;IACA0H,sBAAsBA,CAAA,EAAG;MACrB,IAAI,CAACD,mBAAmB,GAAGlP,WAAW,CAAC,CAAC;MACxC,IAAI,CAAC8L,eAAe,GAAG9L,WAAW,CAAC,CAAC;MACpC,IAAI,CAACuP,4BAA4B,GAAGvP,WAAW,CAAC,CAAC;IACrD;IACA+I,kBAAkBA,CAAC5B,KAAK,EAAEiB,4BAA4B,GAAG,KAAK,EAAE;MAC5D,MAAMoD,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAMgE,oBAAoB,GAAGhE,QAAQ,GAAGA,QAAQ,CAAC5J,YAAY,GAAG,CAAC,CAAC;MAClE,MAAM6N,WAAW,GAAG;QAAE,GAAG,IAAI,CAAC7N;MAAa,CAAC;MAC5C,MAAMoM,WAAW,GAAGhO,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC,IAAI,CAACoO,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAAClM,OAAO,CAACmG,UAAU,EAAE;QACzC,IAAI,CAACX,cAAc,GAAG,IAAI,CAACkH,oBAAoB,GAAGnH,SAAS;MAC/D;MACA,IAAI,CAACiH,8BAA8B,GAAG,CAACtG,4BAA4B;MACnE,MAAMsH,cAAc,GAAG3P,SAAS,CAAC,CAAC;MAClC,MAAM4P,cAAc,GAAGnE,QAAQ,GAAGA,QAAQ,CAAC0B,MAAM,GAAGzF,SAAS;MAC7D,MAAMmI,YAAY,GAAG,IAAI,CAACtN,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC4K,MAAM,GAAGzF,SAAS;MACjE,MAAMoI,uBAAuB,GAAGF,cAAc,KAAKC,YAAY;MAC/D,MAAMvG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMwG,YAAY,GAAG,CAACzG,KAAK,IAAIA,KAAK,CAAC0G,OAAO,CAAClK,MAAM,IAAI,CAAC;MACxD,MAAMmK,sBAAsB,GAAG9D,OAAO,CAAC2D,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAAC5N,OAAO,CAACgM,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAACxI,IAAI,CAAC4H,IAAI,CAAC2C,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAACzK,iBAAiB,GAAG,CAAC;MAC1B,IAAI0K,kBAAkB;MACtB,IAAI,CAACC,cAAc,GAAIC,MAAM,IAAK;QAC9B,MAAMC,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACtC,WAAW,CAAC9J,CAAC,EAAEiD,KAAK,CAACjD,CAAC,EAAEmM,QAAQ,CAAC;QAC9CC,YAAY,CAACtC,WAAW,CAAC7J,CAAC,EAAEgD,KAAK,CAAChD,CAAC,EAAEkM,QAAQ,CAAC;QAC9C,IAAI,CAACtC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAACtG,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAACtM,MAAM,IACX,IAAI,CAAC8L,cAAc,IACnB,IAAI,CAACA,cAAc,CAAC9L,MAAM,EAAE;UAC5B5C,oBAAoB,CAACgQ,cAAc,EAAE,IAAI,CAACpN,MAAM,CAACyJ,SAAS,EAAE,IAAI,CAACqC,cAAc,CAAC9L,MAAM,CAACyJ,SAAS,CAAC;UACjGwE,MAAM,CAAC,IAAI,CAAC7I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,EAAEc,cAAc,EAAEW,QAAQ,CAAC;UAChF;AACpB;AACA;AACA;UACoB,IAAIH,kBAAkB,IAClB7P,SAAS,CAAC,IAAI,CAACqH,cAAc,EAAEwI,kBAAkB,CAAC,EAAE;YACpD,IAAI,CAAC1M,iBAAiB,GAAG,KAAK;UAClC;UACA,IAAI,CAAC0M,kBAAkB,EACnBA,kBAAkB,GAAGnQ,SAAS,CAAC,CAAC;UACpCZ,WAAW,CAAC+Q,kBAAkB,EAAE,IAAI,CAACxI,cAAc,CAAC;QACxD;QACA,IAAImI,uBAAuB,EAAE;UACzB,IAAI,CAACW,eAAe,GAAGf,WAAW;UAClCvQ,SAAS,CAACuQ,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAAC5N,YAAY,EAAEyO,QAAQ,EAAEL,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAAC7N,IAAI,CAACoJ,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACuB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACpH,iBAAiB,GAAG6K,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,IAAI,CAACjO,OAAO,CAACmG,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAS,cAAcA,CAAC5G,OAAO,EAAE;MACpB,IAAI,CAACmE,eAAe,CAAC,gBAAgB,CAAC;MACtC,IAAI,CAACkC,gBAAgB,EAAEkI,IAAI,CAAC,CAAC;MAC7B,IAAI,CAACjI,YAAY,EAAED,gBAAgB,EAAEkI,IAAI,CAAC,CAAC;MAC3C,IAAI,IAAI,CAAC1B,gBAAgB,EAAE;QACvB9Q,WAAW,CAAC,IAAI,CAAC8Q,gBAAgB,CAAC;QAClC,IAAI,CAACA,gBAAgB,GAAGtH,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACsH,gBAAgB,GAAGzQ,KAAK,CAACmG,MAAM,CAAC,MAAM;QACvC5D,qBAAqB,CAACkG,sBAAsB,GAAG,IAAI;QACnDxI,gBAAgB,CAAC+D,MAAM,EAAE;QACzB,IAAI,CAAC9D,WAAW,KAAK,IAAI,CAACA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC+J,gBAAgB,GAAG1J,kBAAkB,CAAC,IAAI,CAACL,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;UACpE,GAAG0D,OAAO;UACVwO,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAE,IAAI;UACZC,QAAQ,EAAGR,MAAM,IAAK;YAClB,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC;YAC3BlO,OAAO,CAAC0O,QAAQ,IAAI1O,OAAO,CAAC0O,QAAQ,CAACR,MAAM,CAAC;UAChD,CAAC;UACDS,MAAM,EAAEA,CAAA,KAAM;YACVtS,gBAAgB,CAAC+D,MAAM,EAAE;UAC7B,CAAC;UACDqG,UAAU,EAAEA,CAAA,KAAM;YACdpK,gBAAgB,CAAC+D,MAAM,EAAE;YACzBJ,OAAO,CAACyG,UAAU,IAAIzG,OAAO,CAACyG,UAAU,CAAC,CAAC;YAC1C,IAAI,CAACmI,iBAAiB,CAAC,CAAC;UAC5B;QACJ,CAAC,CAAC;QACF,IAAI,IAAI,CAACtI,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAACwG,gBAAgB,GAAGtH,SAAS;MACrC,CAAC,CAAC;IACN;IACAqJ,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACtI,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAGd,SAAS;QAC9C,IAAI,CAACe,YAAY,CAACuI,eAAe,GAAGtJ,SAAS;MACjD;MACA,MAAM4B,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAAC2H,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAACxI,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACiI,eAAe,GAChB/I,SAAS;MACrB,IAAI,CAACpB,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAW,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACvB,IAAI,CAAC4H,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC9O,eAAe,CAAC;QAC3D,IAAI,CAACkH,gBAAgB,CAACkI,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACK,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAM3C,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEM,oBAAoB;QAAErH,MAAM;QAAElF,MAAM;QAAEV;MAAa,CAAC,GAAG0M,IAAI;MACjE,IAAI,CAACO,oBAAoB,IAAI,CAACrH,MAAM,IAAI,CAAClF,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAKgM,IAAI,IACb,IAAI,CAAChM,MAAM,IACXA,MAAM,IACN4O,yBAAyB,CAAC,IAAI,CAAChP,OAAO,CAACiP,aAAa,EAAE,IAAI,CAAC7O,MAAM,CAACyJ,SAAS,EAAEzJ,MAAM,CAACyJ,SAAS,CAAC,EAAE;QAChGvE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIzH,SAAS,CAAC,CAAC;QACnC,MAAMqR,OAAO,GAAG3R,UAAU,CAAC,IAAI,CAAC6C,MAAM,CAACyJ,SAAS,CAAC7H,CAAC,CAAC;QACnDsD,MAAM,CAACtD,CAAC,CAACmN,GAAG,GAAG/C,IAAI,CAAC9G,MAAM,CAACtD,CAAC,CAACmN,GAAG;QAChC7J,MAAM,CAACtD,CAAC,CAACoN,GAAG,GAAG9J,MAAM,CAACtD,CAAC,CAACmN,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAG9R,UAAU,CAAC,IAAI,CAAC6C,MAAM,CAACyJ,SAAS,CAAC5H,CAAC,CAAC;QACnDqD,MAAM,CAACrD,CAAC,CAACkN,GAAG,GAAG/C,IAAI,CAAC9G,MAAM,CAACrD,CAAC,CAACkN,GAAG;QAChC7J,MAAM,CAACrD,CAAC,CAACmN,GAAG,GAAG9J,MAAM,CAACrD,CAAC,CAACkN,GAAG,GAAGE,OAAO;MACzC;MACApS,WAAW,CAAC0P,oBAAoB,EAAErH,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYlI,YAAY,CAACuP,oBAAoB,EAAEjN,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYhC,YAAY,CAAC,IAAI,CAAC2P,4BAA4B,EAAE,IAAI,CAAC1D,eAAe,EAAEgD,oBAAoB,EAAEjN,YAAY,CAAC;IAC7G;IACAqF,kBAAkBA,CAAC1E,QAAQ,EAAE0H,IAAI,EAAE;MAC/B,IAAI,CAAC,IAAI,CAACxE,WAAW,CAACQ,GAAG,CAAC1D,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACkD,WAAW,CAACS,GAAG,CAAC3D,QAAQ,EAAE,IAAIjC,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAM+I,KAAK,GAAG,IAAI,CAAC5D,WAAW,CAACU,GAAG,CAAC5D,QAAQ,CAAC;MAC5C8G,KAAK,CAACjD,GAAG,CAAC6D,IAAI,CAAC;MACf,MAAMuH,MAAM,GAAGvH,IAAI,CAAC/H,OAAO,CAACuP,sBAAsB;MAClDxH,IAAI,CAACyH,OAAO,CAAC;QACT9J,UAAU,EAAE4J,MAAM,GAAGA,MAAM,CAAC5J,UAAU,GAAGH,SAAS;QAClDkK,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,2BAA2B,GAC7DJ,MAAM,CAACI,2BAA2B,CAAC3H,IAAI,CAAC,GACxCxC;MACV,CAAC,CAAC;IACN;IACAuB,MAAMA,CAAA,EAAG;MACL,MAAMK,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,OAAOD,KAAK,GAAGA,KAAK,CAACiF,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,MAAM;QAAEhM;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,OAAOK,QAAQ,GAAG,IAAI,CAAC+G,QAAQ,CAAC,CAAC,EAAEgF,IAAI,IAAI,IAAI,GAAG,IAAI;IAC1D;IACAuD,WAAWA,CAAA,EAAG;MACV,MAAM;QAAEtP;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,OAAOK,QAAQ,GAAG,IAAI,CAAC+G,QAAQ,CAAC,CAAC,EAAEwI,QAAQ,GAAGrK,SAAS;IAC3D;IACA6B,QAAQA,CAAA,EAAG;MACP,MAAM;QAAE/G;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,IAAIK,QAAQ,EACR,OAAO,IAAI,CAACN,IAAI,CAACwD,WAAW,CAACU,GAAG,CAAC5D,QAAQ,CAAC;IAClD;IACAmP,OAAOA,CAAC;MAAE3N,UAAU;MAAE6D,UAAU;MAAE+J;IAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7D,MAAMtI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EACLA,KAAK,CAACqI,OAAO,CAAC,IAAI,EAAEC,qBAAqB,CAAC;MAC9C,IAAI5N,UAAU,EAAE;QACZ,IAAI,CAAC+H,eAAe,GAAGrE,SAAS;QAChC,IAAI,CAAC1D,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI6D,UAAU,EACV,IAAI,CAACqG,UAAU,CAAC;QAAErG;MAAW,CAAC,CAAC;IACvC;IACAmK,QAAQA,CAAA,EAAG;MACP,MAAM1I,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACP,OAAOA,KAAK,CAAC0I,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAlI,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAEpI;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,IAAI,CAACT,aAAa,EACd;MACJ;MACA,IAAIuQ,sBAAsB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,MAAM;QAAEpQ;MAAa,CAAC,GAAGH,aAAa;MACtC,IAAIG,YAAY,CAACqQ,CAAC,IACdrQ,YAAY,CAACsQ,MAAM,IACnBtQ,YAAY,CAACuQ,OAAO,IACpBvQ,YAAY,CAACwQ,OAAO,IACpBxQ,YAAY,CAACyQ,OAAO,IACpBzQ,YAAY,CAAC0Q,KAAK,IAClB1Q,YAAY,CAAC2Q,KAAK,EAAE;QACpBP,sBAAsB,GAAG,IAAI;MACjC;MACA;MACA,IAAI,CAACA,sBAAsB,EACvB;MACJ,MAAMQ,WAAW,GAAG,CAAC,CAAC;MACtB,IAAI5Q,YAAY,CAACqQ,CAAC,EAAE;QAChB1Q,wBAAwB,CAAC,GAAG,EAAEE,aAAa,EAAE+Q,WAAW,EAAE,IAAI,CAAChC,eAAe,CAAC;MACnF;MACA;MACA,KAAK,IAAI5K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1E,aAAa,CAAC2E,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3CrE,wBAAwB,CAAC,SAASL,aAAa,CAAC0E,CAAC,CAAC,EAAE,EAAEnE,aAAa,EAAE+Q,WAAW,EAAE,IAAI,CAAChC,eAAe,CAAC;QACvGjP,wBAAwB,CAAC,OAAOL,aAAa,CAAC0E,CAAC,CAAC,EAAE,EAAEnE,aAAa,EAAE+Q,WAAW,EAAE,IAAI,CAAChC,eAAe,CAAC;MACzG;MACA;MACA;MACA/O,aAAa,CAACuJ,MAAM,CAAC,CAAC;MACtB;MACA,KAAK,MAAMxJ,GAAG,IAAIgR,WAAW,EAAE;QAC3B/Q,aAAa,CAACI,cAAc,CAACL,GAAG,EAAEgR,WAAW,CAAChR,GAAG,CAAC,CAAC;QACnD,IAAI,IAAI,CAACgP,eAAe,EAAE;UACtB,IAAI,CAACA,eAAe,CAAChP,GAAG,CAAC,GAAGgR,WAAW,CAAChR,GAAG,CAAC;QAChD;MACJ;MACA;MACA;MACAC,aAAa,CAACmL,cAAc,CAAC,CAAC;IAClC;IACA6F,mBAAmBA,CAACC,SAAS,EAAE;MAC3B,IAAI,CAAC,IAAI,CAAC/L,QAAQ,IAAI,IAAI,CAAC7C,KAAK,EAC5B,OAAO2D,SAAS;MACpB,IAAI,CAAC,IAAI,CAAClC,SAAS,EAAE;QACjB,OAAOpE,gBAAgB;MAC3B;MACA,MAAMwR,MAAM,GAAG;QACXvR,UAAU,EAAE;MAChB,CAAC;MACD,MAAM2I,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,IAAI,CAAC/F,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB4O,MAAM,CAACC,OAAO,GAAG,EAAE;QACnBD,MAAM,CAACE,aAAa,GAChB5T,kBAAkB,CAACyT,SAAS,EAAEG,aAAa,CAAC,IAAI,EAAE;QACtDF,MAAM,CAACG,SAAS,GAAG/I,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAACnI,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAO+Q,MAAM;MACjB;MACA,MAAMrE,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACzC,eAAe,IAAI,CAAC,IAAI,CAACxJ,MAAM,IAAI,CAACgM,IAAI,CAAC9G,MAAM,EAAE;QACvD,MAAMuL,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC7Q,OAAO,CAACK,QAAQ,EAAE;UACvBwQ,WAAW,CAACH,OAAO,GACf,IAAI,CAAChR,YAAY,CAACgR,OAAO,KAAKnL,SAAS,GACjC,IAAI,CAAC7F,YAAY,CAACgR,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrB5T,kBAAkB,CAACyT,SAAS,EAAEG,aAAa,CAAC,IAAI,EAAE;QAC1D;QACA,IAAI,IAAI,CAACvN,YAAY,IAAI,CAAC5E,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;UACvDmR,WAAW,CAACD,SAAS,GAAG/I,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAACzE,YAAY,GAAG,KAAK;QAC7B;QACA,OAAOyN,WAAW;MACtB;MACA,MAAMC,cAAc,GAAG1E,IAAI,CAACkC,eAAe,IAAIlC,IAAI,CAAC1M,YAAY;MAChE,IAAI,CAACqP,uBAAuB,CAAC,CAAC;MAC9B0B,MAAM,CAACG,SAAS,GAAGtS,wBAAwB,CAAC,IAAI,CAAC+O,4BAA4B,EAAE,IAAI,CAACtL,SAAS,EAAE+O,cAAc,CAAC;MAC9G,IAAIjJ,iBAAiB,EAAE;QACnB4I,MAAM,CAACG,SAAS,GAAG/I,iBAAiB,CAACiJ,cAAc,EAAEL,MAAM,CAACG,SAAS,CAAC;MAC1E;MACA,MAAM;QAAE5O,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC2H,eAAe;MACrC6G,MAAM,CAACM,eAAe,GAAG,GAAG/O,CAAC,CAACgP,MAAM,GAAG,GAAG,KAAK/O,CAAC,CAAC+O,MAAM,GAAG,GAAG,KAAK;MAClE,IAAI5E,IAAI,CAACkC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgBmC,MAAM,CAACC,OAAO,GACVtE,IAAI,KAAK,IAAI,GACP0E,cAAc,CAACJ,OAAO,IACpB,IAAI,CAAChR,YAAY,CAACgR,OAAO,IACzB,CAAC,GACH,IAAI,CAAC7B,eAAe,GAChB,IAAI,CAACnP,YAAY,CAACgR,OAAO,GACzBI,cAAc,CAACG,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBR,MAAM,CAACC,OAAO,GACVtE,IAAI,KAAK,IAAI,GACP0E,cAAc,CAACJ,OAAO,KAAKnL,SAAS,GAChCuL,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACG,WAAW,KAAK1L,SAAS,GACpCuL,cAAc,CAACG,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAM3R,GAAG,IAAIjB,eAAe,EAAE;QAC/B,IAAIyS,cAAc,CAACxR,GAAG,CAAC,KAAKiG,SAAS,EACjC;QACJ,MAAM;UAAE2L,OAAO;UAAEC,OAAO;UAAEC;QAAc,CAAC,GAAG/S,eAAe,CAACiB,GAAG,CAAC;QAChE;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAM+R,SAAS,GAAGZ,MAAM,CAACG,SAAS,KAAK,MAAM,GACvCE,cAAc,CAACxR,GAAG,CAAC,GACnB4R,OAAO,CAACJ,cAAc,CAACxR,GAAG,CAAC,EAAE8M,IAAI,CAAC;QACxC,IAAI+E,OAAO,EAAE;UACT,MAAMG,GAAG,GAAGH,OAAO,CAACxN,MAAM;UAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,GAAG,EAAE5N,CAAC,EAAE,EAAE;YAC1B+M,MAAM,CAACU,OAAO,CAACzN,CAAC,CAAC,CAAC,GAAG2N,SAAS;UAClC;QACJ,CAAC,MACI;UACD;UACA;UACA;UACA,IAAID,aAAa,EAAE;YACf,IAAI,CAACpR,OAAO,CAACT,aAAa,CAACgS,WAAW,CAACC,IAAI,CAAClS,GAAG,CAAC,GAAG+R,SAAS;UAChE,CAAC,MACI;YACDZ,MAAM,CAACnR,GAAG,CAAC,GAAG+R,SAAS;UAC3B;QACJ;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACrR,OAAO,CAACK,QAAQ,EAAE;QACvBoQ,MAAM,CAACE,aAAa,GAChBvE,IAAI,KAAK,IAAI,GACPrP,kBAAkB,CAACyT,SAAS,EAAEG,aAAa,CAAC,IAAI,EAAE,GAClD,MAAM;MACpB;MACA,OAAOF,MAAM;IACjB;IACAxH,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC7C,UAAU,GAAG,IAAI,CAACkD,QAAQ,GAAG/D,SAAS;IAC/C;IACA;IACAkM,SAASA,CAAA,EAAG;MACR,IAAI,CAAC1R,IAAI,CAAClB,KAAK,CAACgE,OAAO,CAAEkF,IAAI,IAAKA,IAAI,CAAC1B,gBAAgB,EAAEkI,IAAI,CAAC,CAAC,CAAC;MAChE,IAAI,CAACxO,IAAI,CAAClB,KAAK,CAACgE,OAAO,CAACuF,iBAAiB,CAAC;MAC1C,IAAI,CAACrI,IAAI,CAACwD,WAAW,CAAC+D,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAASiB,YAAYA,CAACR,IAAI,EAAE;EACxBA,IAAI,CAACQ,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAACT,IAAI,EAAE;EAC9B,MAAMuB,QAAQ,GAAGvB,IAAI,CAAC3B,UAAU,EAAEkD,QAAQ,IAAIvB,IAAI,CAACuB,QAAQ;EAC3D,IAAIvB,IAAI,CAACjB,MAAM,CAAC,CAAC,IACbiB,IAAI,CAAC3H,MAAM,IACXkJ,QAAQ,IACRvB,IAAI,CAACxD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEsF,SAAS,EAAEzJ,MAAM;MAAEoJ,WAAW,EAAEkI;IAAe,CAAC,GAAG3J,IAAI,CAAC3H,MAAM;IACtE,MAAM;MAAE6O;IAAc,CAAC,GAAGlH,IAAI,CAAC/H,OAAO;IACtC,MAAMsM,QAAQ,GAAGhD,QAAQ,CAAC0B,MAAM,KAAKjD,IAAI,CAAC3H,MAAM,CAAC4K,MAAM;IACvD;IACA;IACA,IAAIiE,aAAa,KAAK,MAAM,EAAE;MAC1B1Q,QAAQ,CAAEoT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGtF,QAAQ,GACvBhD,QAAQ,CAACE,WAAW,CAACmI,IAAI,CAAC,GAC1BrI,QAAQ,CAACO,SAAS,CAAC8H,IAAI,CAAC;QAC9B,MAAMhO,MAAM,GAAGpG,UAAU,CAACqU,YAAY,CAAC;QACvCA,YAAY,CAACzC,GAAG,GAAG/O,MAAM,CAACuR,IAAI,CAAC,CAACxC,GAAG;QACnCyC,YAAY,CAACxC,GAAG,GAAGwC,YAAY,CAACzC,GAAG,GAAGxL,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAIqL,yBAAyB,CAACC,aAAa,EAAE3F,QAAQ,CAACO,SAAS,EAAEzJ,MAAM,CAAC,EAAE;MAC3E7B,QAAQ,CAAEoT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGtF,QAAQ,GACvBhD,QAAQ,CAACE,WAAW,CAACmI,IAAI,CAAC,GAC1BrI,QAAQ,CAACO,SAAS,CAAC8H,IAAI,CAAC;QAC9B,MAAMhO,MAAM,GAAGpG,UAAU,CAAC6C,MAAM,CAACuR,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACxC,GAAG,GAAGwC,YAAY,CAACzC,GAAG,GAAGxL,MAAM;QAC5C;AAChB;AACA;QACgB,IAAIoE,IAAI,CAACvC,cAAc,IAAI,CAACuC,IAAI,CAAC1B,gBAAgB,EAAE;UAC/C0B,IAAI,CAACzG,iBAAiB,GAAG,IAAI;UAC7ByG,IAAI,CAACvC,cAAc,CAACmM,IAAI,CAAC,CAACvC,GAAG,GACzBrH,IAAI,CAACvC,cAAc,CAACmM,IAAI,CAAC,CAACxC,GAAG,GAAGxL,MAAM;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,MAAMkO,WAAW,GAAG/T,WAAW,CAAC,CAAC;IACjCJ,YAAY,CAACmU,WAAW,EAAEzR,MAAM,EAAEkJ,QAAQ,CAACO,SAAS,CAAC;IACrD,MAAMiI,WAAW,GAAGhU,WAAW,CAAC,CAAC;IACjC,IAAIwO,QAAQ,EAAE;MACV5O,YAAY,CAACoU,WAAW,EAAE/J,IAAI,CAACwD,cAAc,CAACmG,cAAc,EAAE,IAAI,CAAC,EAAEpI,QAAQ,CAACE,WAAW,CAAC;IAC9F,CAAC,MACI;MACD9L,YAAY,CAACoU,WAAW,EAAE1R,MAAM,EAAEkJ,QAAQ,CAACO,SAAS,CAAC;IACzD;IACA,MAAM3E,gBAAgB,GAAG,CAAClH,WAAW,CAAC6T,WAAW,CAAC;IAClD,IAAI1M,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAC4C,IAAI,CAAC3B,UAAU,EAAE;MAClB,MAAM8F,cAAc,GAAGnE,IAAI,CAAC0E,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAIP,cAAc,IAAI,CAACA,cAAc,CAAC9F,UAAU,EAAE;QAC9C,MAAM;UAAEkD,QAAQ,EAAEyI,cAAc;UAAE3R,MAAM,EAAE4R;QAAa,CAAC,GAAG9F,cAAc;QACzE,IAAI6F,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAGpU,SAAS,CAAC,CAAC;UACpCL,oBAAoB,CAACyU,gBAAgB,EAAE3I,QAAQ,CAACO,SAAS,EAAEkI,cAAc,CAAClI,SAAS,CAAC;UACpF,MAAM2D,cAAc,GAAG3P,SAAS,CAAC,CAAC;UAClCL,oBAAoB,CAACgQ,cAAc,EAAEpN,MAAM,EAAE4R,YAAY,CAACnI,SAAS,CAAC;UACpE,IAAI,CAAC9L,gBAAgB,CAACkU,gBAAgB,EAAEzE,cAAc,CAAC,EAAE;YACrDrI,wBAAwB,GAAG,IAAI;UACnC;UACA,IAAI+G,cAAc,CAAClM,OAAO,CAACmG,UAAU,EAAE;YACnC4B,IAAI,CAACvC,cAAc,GAAGgI,cAAc;YACpCzF,IAAI,CAAC2E,oBAAoB,GAAGuF,gBAAgB;YAC5ClK,IAAI,CAACmE,cAAc,GAAGA,cAAc;UACxC;QACJ;MACJ;IACJ;IACAnE,IAAI,CAAC5D,eAAe,CAAC,WAAW,EAAE;MAC9B/D,MAAM;MACNkJ,QAAQ;MACRrE,KAAK,EAAE6M,WAAW;MAClBD,WAAW;MACX3M,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAI4C,IAAI,CAACjB,MAAM,CAAC,CAAC,EAAE;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAGgB,IAAI,CAAC/H,OAAO;IACvC+G,cAAc,IAAIA,cAAc,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIgB,IAAI,CAAC/H,OAAO,CAAC0F,UAAU,GAAGH,SAAS;AACvC;AACA,SAASzC,mBAAmBA,CAACiF,IAAI,EAAE;EAC/B;AACJ;AACA;EACI,IAAIpM,WAAW,CAACiH,KAAK,EAAE;IACnBhE,OAAO,CAACC,KAAK,EAAE;EACnB;EACA,IAAI,CAACkJ,IAAI,CAACxH,MAAM,EACZ;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,CAACwH,IAAI,CAAC6E,YAAY,CAAC,CAAC,EAAE;IACtB7E,IAAI,CAACzG,iBAAiB,GAAGyG,IAAI,CAACxH,MAAM,CAACe,iBAAiB;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACIyG,IAAI,CAACxG,uBAAuB,KAAKwG,IAAI,CAACxG,uBAAuB,GAAGyI,OAAO,CAACjC,IAAI,CAACzG,iBAAiB,IAC1FyG,IAAI,CAACxH,MAAM,CAACe,iBAAiB,IAC7ByG,IAAI,CAACxH,MAAM,CAACgB,uBAAuB,CAAC,CAAC;EACzCwG,IAAI,CAACvG,gBAAgB,KAAKuG,IAAI,CAACvG,gBAAgB,GAAGuG,IAAI,CAACxH,MAAM,CAACiB,gBAAgB,CAAC;AACnF;AACA,SAASyB,eAAeA,CAAC8E,IAAI,EAAE;EAC3BA,IAAI,CAACzG,iBAAiB,GAClByG,IAAI,CAACxG,uBAAuB,GACxBwG,IAAI,CAACvG,gBAAgB,GACjB,KAAK;AACrB;AACA,SAASyH,aAAaA,CAAClB,IAAI,EAAE;EACzBA,IAAI,CAACkB,aAAa,CAAC,CAAC;AACxB;AACA,SAASb,iBAAiBA,CAACL,IAAI,EAAE;EAC7BA,IAAI,CAACK,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASC,kBAAkBA,CAACN,IAAI,EAAE;EAC9BA,IAAI,CAAC1G,aAAa,GAAG,KAAK;AAC9B;AACA,SAASiH,mBAAmBA,CAACP,IAAI,EAAE;EAC/B,MAAM;IAAExI;EAAc,CAAC,GAAGwI,IAAI,CAAC/H,OAAO;EACtC,IAAIT,aAAa,IAAIA,aAAa,CAACwG,QAAQ,CAAC,CAAC,CAACmM,qBAAqB,EAAE;IACjE3S,aAAa,CAAC+E,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACAyD,IAAI,CAAClH,cAAc,CAAC,CAAC;AACzB;AACA,SAASiE,eAAeA,CAACiD,IAAI,EAAE;EAC3BA,IAAI,CAACjD,eAAe,CAAC,CAAC;EACtBiD,IAAI,CAAC+D,WAAW,GAAG/D,IAAI,CAACvC,cAAc,GAAGuC,IAAI,CAACzC,MAAM,GAAGC,SAAS;EAChEwC,IAAI,CAACzG,iBAAiB,GAAG,IAAI;AACjC;AACA,SAASyB,kBAAkBA,CAACgF,IAAI,EAAE;EAC9BA,IAAI,CAAChF,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAAC+E,IAAI,EAAE;EAC1BA,IAAI,CAAC/E,cAAc,CAAC,CAAC;AACzB;AACA,SAAS2E,oBAAoBA,CAACI,IAAI,EAAE;EAChCA,IAAI,CAACJ,oBAAoB,CAAC,CAAC;AAC/B;AACA,SAASuB,mBAAmBA,CAAC/B,KAAK,EAAE;EAChCA,KAAK,CAACgL,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAAS/D,YAAYA,CAACgE,MAAM,EAAEnN,KAAK,EAAEoN,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAG/V,SAAS,CAAC0I,KAAK,CAACqN,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EACnDD,MAAM,CAACG,KAAK,GAAGhW,SAAS,CAAC0I,KAAK,CAACsN,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EAC3CD,MAAM,CAACpB,MAAM,GAAG/L,KAAK,CAAC+L,MAAM;EAC5BoB,MAAM,CAACI,WAAW,GAAGvN,KAAK,CAACuN,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAACjD,GAAG,GAAG5S,SAAS,CAACmW,IAAI,CAACvD,GAAG,EAAEwD,EAAE,CAACxD,GAAG,EAAEkD,CAAC,CAAC;EAC3CD,MAAM,CAAChD,GAAG,GAAG7S,SAAS,CAACmW,IAAI,CAACtD,GAAG,EAAEuD,EAAE,CAACvD,GAAG,EAAEiD,CAAC,CAAC;AAC/C;AACA,SAAShE,MAAMA,CAAC+D,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAACpQ,CAAC,EAAE0Q,IAAI,CAAC1Q,CAAC,EAAE2Q,EAAE,CAAC3Q,CAAC,EAAEqQ,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAACnQ,CAAC,EAAEyQ,IAAI,CAACzQ,CAAC,EAAE0Q,EAAE,CAAC1Q,CAAC,EAAEoQ,CAAC,CAAC;AACtC;AACA,SAAStE,mBAAmBA,CAAChG,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAACuG,eAAe,IAAIvG,IAAI,CAACuG,eAAe,CAAC2C,WAAW,KAAK1L,SAAS;AAClF;AACA,MAAMK,uBAAuB,GAAG;EAC5BgN,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK,OAAOC,SAAS,KAAK,WAAW,IAClEA,SAAS,CAACC,SAAS,IACnBD,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,GAAGN,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAC/EO,IAAI,CAACC,KAAK,GACV5W,IAAI;AACV,SAAS6W,SAASA,CAAC5B,IAAI,EAAE;EACrB;EACAA,IAAI,CAACxC,GAAG,GAAGiE,UAAU,CAACzB,IAAI,CAACxC,GAAG,CAAC;EAC/BwC,IAAI,CAACvC,GAAG,GAAGgE,UAAU,CAACzB,IAAI,CAACvC,GAAG,CAAC;AACnC;AACA,SAASrE,QAAQA,CAACE,GAAG,EAAE;EACnBsI,SAAS,CAACtI,GAAG,CAACjJ,CAAC,CAAC;EAChBuR,SAAS,CAACtI,GAAG,CAAChJ,CAAC,CAAC;AACpB;AACA,SAAS+M,yBAAyBA,CAACC,aAAa,EAAE3F,QAAQ,EAAElJ,MAAM,EAAE;EAChE,OAAQ6O,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACtR,MAAM,CAACO,WAAW,CAACoL,QAAQ,CAAC,EAAEpL,WAAW,CAACkC,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AACA,SAASiL,sBAAsBA,CAACtD,IAAI,EAAE;EAClC,OAAOA,IAAI,KAAKA,IAAI,CAAChI,IAAI,IAAIgI,IAAI,CAACmC,MAAM,EAAEG,OAAO;AACrD;AAEA,SAASpH,eAAe,EAAEzC,oBAAoB,EAAEiS,OAAO,EAAErE,YAAY,EAAEC,MAAM,EAAEvL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}