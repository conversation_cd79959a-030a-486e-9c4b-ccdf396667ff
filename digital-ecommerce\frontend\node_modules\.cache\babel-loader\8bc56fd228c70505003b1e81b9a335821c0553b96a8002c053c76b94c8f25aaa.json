{"ast": null, "code": "import { isDragging } from './is-active.mjs';\nfunction setDragLock(axis) {\n  if (axis === \"x\" || axis === \"y\") {\n    if (isDragging[axis]) {\n      return null;\n    } else {\n      isDragging[axis] = true;\n      return () => {\n        isDragging[axis] = false;\n      };\n    }\n  } else {\n    if (isDragging.x || isDragging.y) {\n      return null;\n    } else {\n      isDragging.x = isDragging.y = true;\n      return () => {\n        isDragging.x = isDragging.y = false;\n      };\n    }\n  }\n}\nexport { setDragLock };", "map": {"version": 3, "names": ["isDragging", "setDragLock", "axis", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs"], "sourcesContent": ["import { isDragging } from './is-active.mjs';\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (isDragging[axis]) {\n            return null;\n        }\n        else {\n            isDragging[axis] = true;\n            return () => {\n                isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (isDragging.x || isDragging.y) {\n            return null;\n        }\n        else {\n            isDragging.x = isDragging.y = true;\n            return () => {\n                isDragging.x = isDragging.y = false;\n            };\n        }\n    }\n}\n\nexport { setDragLock };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAE5C,SAASC,WAAWA,CAACC,IAAI,EAAE;EACvB,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;IAC9B,IAAIF,UAAU,CAACE,IAAI,CAAC,EAAE;MAClB,OAAO,IAAI;IACf,CAAC,MACI;MACDF,UAAU,CAACE,IAAI,CAAC,GAAG,IAAI;MACvB,OAAO,MAAM;QACTF,UAAU,CAACE,IAAI,CAAC,GAAG,KAAK;MAC5B,CAAC;IACL;EACJ,CAAC,MACI;IACD,IAAIF,UAAU,CAACG,CAAC,IAAIH,UAAU,CAACI,CAAC,EAAE;MAC9B,OAAO,IAAI;IACf,CAAC,MACI;MACDJ,UAAU,CAACG,CAAC,GAAGH,UAAU,CAACI,CAAC,GAAG,IAAI;MAClC,OAAO,MAAM;QACTJ,UAAU,CAACG,CAAC,GAAGH,UAAU,CAACI,CAAC,GAAG,KAAK;MACvC,CAAC;IACL;EACJ;AACJ;AAEA,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}