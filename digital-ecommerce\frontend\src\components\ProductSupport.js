import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DocumentTextIcon,
  PlayIcon,
  WrenchScrewdriverIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const ProductSupport = ({ product }) => {
  const [activeTab, setActiveTab] = useState('guides');
  const [expandedSection, setExpandedSection] = useState(null);

  const supportTabs = [
    { id: 'guides', name: 'Installation Guides', icon: DocumentTextIcon },
    { id: 'videos', name: 'Video Tutorials', icon: PlayIcon },
    { id: 'troubleshooting', name: 'Troubleshooting', icon: WrenchScrewdriverIcon },
    { id: 'warranty', name: 'Warranty & Returns', icon: ShieldCheckIcon }
  ];

  const installationGuides = [
    {
      id: 1,
      title: 'Quick Start Guide',
      description: 'Get up and running in minutes with our step-by-step installation guide.',
      downloadUrl: '#',
      fileSize: '2.3 MB',
      format: 'PDF'
    },
    {
      id: 2,
      title: 'Detailed Installation Manual',
      description: 'Comprehensive installation instructions with diagrams and troubleshooting tips.',
      downloadUrl: '#',
      fileSize: '8.7 MB',
      format: 'PDF'
    },
    {
      id: 3,
      title: 'Compatibility Guide',
      description: 'Check compatibility with other components and system requirements.',
      downloadUrl: '#',
      fileSize: '1.5 MB',
      format: 'PDF'
    }
  ];

  const videoTutorials = [
    {
      id: 1,
      title: 'Unboxing and First Look',
      duration: '5:32',
      thumbnail: 'https://images.unsplash.com/photo-1611532736597-de2d4265fba3?w=300',
      description: 'See what\'s included in the box and get familiar with the product.'
    },
    {
      id: 2,
      title: 'Step-by-Step Installation',
      duration: '12:45',
      thumbnail: 'https://images.unsplash.com/photo-1581092921461-eab62e97a780?w=300',
      description: 'Follow along as we install this component in a real PC build.'
    },
    {
      id: 3,
      title: 'Performance Testing & Optimization',
      duration: '8:20',
      thumbnail: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=300',
      description: 'Learn how to optimize settings for best performance.'
    }
  ];

  const troubleshootingGuides = [
    {
      id: 1,
      category: 'Installation Issues',
      items: [
        {
          problem: 'Component not detected by system',
          solution: 'Check all connections are secure. Ensure power cables are properly connected. Try reseating the component.',
          severity: 'medium'
        },
        {
          problem: 'Compatibility error during installation',
          solution: 'Verify system requirements. Check BIOS/UEFI settings. Update motherboard BIOS if necessary.',
          severity: 'high'
        }
      ]
    },
    {
      id: 2,
      category: 'Performance Issues',
      items: [
        {
          problem: 'Lower than expected performance',
          solution: 'Update drivers to latest version. Check thermal throttling. Verify power supply capacity.',
          severity: 'medium'
        },
        {
          problem: 'System instability or crashes',
          solution: 'Run memory test. Check temperatures. Verify power supply stability. Update system drivers.',
          severity: 'high'
        }
      ]
    }
  ];

  const warrantyInfo = {
    period: '3 years',
    coverage: 'Manufacturing defects and component failures',
    returnWindow: '30 days',
    conditions: [
      'Original purchase receipt required',
      'Product must be in original condition',
      'No physical damage or modification',
      'All original accessories included'
    ],
    process: [
      'Contact customer support',
      'Provide order number and issue description',
      'Receive RMA number if approved',
      'Ship product with RMA number',
      'Receive replacement or refund'
    ]
  };

  const contactOptions = [
    {
      method: 'Live Chat',
      icon: ChatBubbleLeftRightIcon,
      availability: '24/7',
      responseTime: '< 2 minutes',
      description: 'Get instant help from our support team'
    },
    {
      method: 'Phone Support',
      icon: PhoneIcon,
      availability: 'Mon-Fri 9AM-8PM EST',
      responseTime: 'Immediate',
      description: '1-800-PC-GAMING'
    },
    {
      method: 'Email Support',
      icon: EnvelopeIcon,
      availability: '24/7',
      responseTime: '< 24 hours',
      description: '<EMAIL>'
    }
  ];

  const toggleSection = (sectionId) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-light-orange-500 text-white p-6">
        <h2 className="text-2xl font-bold mb-2">Product Support</h2>
        <p className="opacity-90">Get help with installation, troubleshooting, and more</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {supportTabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-light-orange-500 text-light-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'guides' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Installation Guides & Documentation</h3>
            {installationGuides.map((guide) => (
              <div key={guide.id} className="border border-gray-200 rounded-lg p-4 hover:border-light-orange-300 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{guide.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{guide.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{guide.format}</span>
                      <span>•</span>
                      <span>{guide.fileSize}</span>
                    </div>
                  </div>
                  <button className="ml-4 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors text-sm">
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'videos' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Video Tutorials</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {videoTutorials.map((video) => (
                <div key={video.id} className="border border-gray-200 rounded-lg overflow-hidden hover:border-light-orange-300 transition-colors">
                  <div className="relative">
                    <img src={video.thumbnail} alt={video.title} className="w-full h-32 object-cover" />
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                      <PlayIcon className="w-12 h-12 text-white" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                  </div>
                  <div className="p-4">
                    <h4 className="font-medium text-gray-900">{video.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{video.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'troubleshooting' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Troubleshooting Guide</h3>
            {troubleshootingGuides.map((category) => (
              <div key={category.id} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => toggleSection(category.id)}
                  className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                >
                  <h4 className="font-medium text-gray-900">{category.category}</h4>
                  {expandedSection === category.id ? (
                    <ChevronDownIcon className="w-5 h-5 text-gray-500" />
                  ) : (
                    <ChevronRightIcon className="w-5 h-5 text-gray-500" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSection === category.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="border-t border-gray-200"
                    >
                      <div className="p-4 space-y-4">
                        {category.items.map((item, index) => (
                          <div key={index} className={`border rounded-lg p-4 ${getSeverityColor(item.severity)}`}>
                            <div className="flex items-start space-x-3">
                              <ExclamationTriangleIcon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                              <div className="flex-1">
                                <h5 className="font-medium mb-2">{item.problem}</h5>
                                <div className="flex items-start space-x-2">
                                  <CheckCircleIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
                                  <p className="text-sm">{item.solution}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'warranty' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Warranty & Returns</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Warranty Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Warranty Period:</span>
                    <span className="font-medium">{warrantyInfo.period}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Return Window:</span>
                    <span className="font-medium">{warrantyInfo.returnWindow}</span>
                  </div>
                  <div className="pt-2">
                    <span className="text-gray-600">Coverage:</span>
                    <p className="text-gray-900 mt-1">{warrantyInfo.coverage}</p>
                  </div>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Return Conditions</h4>
                <ul className="space-y-2 text-sm">
                  {warrantyInfo.conditions.map((condition, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircleIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{condition}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Return Process</h4>
              <div className="space-y-3">
                {warrantyInfo.process.map((step, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-light-orange-500 text-white rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0">
                      {index + 1}
                    </div>
                    <span className="text-gray-700">{step}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Contact Support Section */}
      <div className="border-t border-gray-200 bg-gray-50 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Need More Help?</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {contactOptions.map((option, index) => {
            const Icon = option.icon;
            return (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 text-center hover:border-light-orange-300 transition-colors">
                <Icon className="w-8 h-8 text-light-orange-500 mx-auto mb-2" />
                <h4 className="font-medium text-gray-900 mb-1">{option.method}</h4>
                <p className="text-sm text-gray-600 mb-2">{option.description}</p>
                <div className="text-xs text-gray-500">
                  <div>{option.availability}</div>
                  <div>Response: {option.responseTime}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProductSupport;
