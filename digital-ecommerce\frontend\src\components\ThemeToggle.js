import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ className = '', showLabel = false }) => {
  const { theme, toggleTheme, isSystemTheme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Gaming-inspired SVG icons
  const SunIcon = () => (
    <motion.svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      initial={{ rotate: 0, scale: 1 }}
      animate={{ 
        rotate: theme === 'light' ? 0 : -90,
        scale: theme === 'light' ? 1 : 0.8
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Gaming-style sun with RGB glow effect */}
      <circle
        cx="12"
        cy="12"
        r="4"
        fill="currentColor"
        className="drop-shadow-[0_0_8px_rgba(255,179,102,0.6)]"
      />
      {/* Animated rays */}
      {[0, 45, 90, 135, 180, 225, 270, 315].map((angle, index) => (
        <motion.line
          key={angle}
          x1="12"
          y1="2"
          x2="12"
          y2="4"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          transform={`rotate(${angle} 12 12)`}
          initial={{ opacity: 0.6, scale: 1 }}
          animate={{ 
            opacity: theme === 'light' ? [0.6, 1, 0.6] : 0.3,
            scale: theme === 'light' ? [1, 1.2, 1] : 0.8
          }}
          transition={{ 
            duration: 2,
            repeat: theme === 'light' ? Infinity : 0,
            delay: index * 0.1
          }}
        />
      ))}
    </motion.svg>
  );

  const MoonIcon = () => (
    <motion.svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      initial={{ rotate: 0, scale: 1 }}
      animate={{ 
        rotate: theme === 'dark' ? 0 : 90,
        scale: theme === 'dark' ? 1 : 0.8
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Gaming-style moon with cyber glow */}
      <motion.path
        d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
        fill="currentColor"
        className="drop-shadow-[0_0_8px_rgba(0,212,255,0.6)]"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
      />
      {/* Gaming stars */}
      {[
        { x: 6, y: 6, delay: 0 },
        { x: 8, y: 4, delay: 0.2 },
        { x: 4, y: 8, delay: 0.4 }
      ].map((star, index) => (
        <motion.circle
          key={index}
          cx={star.x}
          cy={star.y}
          r="1"
          fill="currentColor"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ 
            opacity: theme === 'dark' ? [0, 1, 0] : 0,
            scale: theme === 'dark' ? [0, 1, 0] : 0
          }}
          transition={{ 
            duration: 1.5,
            repeat: theme === 'dark' ? Infinity : 0,
            delay: star.delay
          }}
        />
      ))}
    </motion.svg>
  );

  const SystemIcon = () => (
    <motion.svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      initial={{ scale: 1 }}
      animate={{ scale: isSystemTheme ? 1 : 0.8 }}
      transition={{ duration: 0.2 }}
    >
      {/* Gaming monitor/system icon */}
      <rect
        x="3"
        y="4"
        width="18"
        height="12"
        rx="2"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        className="drop-shadow-[0_0_6px_rgba(0,255,136,0.4)]"
      />
      <path
        d="M8 20h8"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M12 16v4"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
      {/* RGB indicator dots */}
      <circle cx="6" cy="7" r="1" fill="#ff6b6b" />
      <circle cx="9" cy="7" r="1" fill="#4ecdc4" />
      <circle cx="12" cy="7" r="1" fill="#45b7d1" />
    </motion.svg>
  );

  const handleToggle = () => {
    toggleTheme();
    
    // Gaming feedback effect
    if (navigator.vibrate) {
      navigator.vibrate(50); // Haptic feedback for mobile
    }
  };

  const getThemeLabel = () => {
    if (isSystemTheme) return 'System';
    return theme === 'light' ? 'Light' : 'Dark';
  };

  const getTooltipText = () => {
    const nextTheme = theme === 'light' ? 'dark' : 'light';
    return `Switch to ${nextTheme} mode`;
  };

  return (
    <div className="relative">
      {/* Main Toggle Button */}
      <motion.button
        onClick={handleToggle}
        onMouseEnter={() => {
          setIsHovered(true);
          setShowTooltip(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
          setShowTooltip(false);
        }}
        onFocus={() => setShowTooltip(true)}
        onBlur={() => setShowTooltip(false)}
        className={`
          relative flex items-center justify-center
          w-10 h-10 rounded-lg
          bg-gray-100 dark:bg-gray-800
          border border-gray-200 dark:border-gray-700
          hover:border-orange-300 dark:hover:border-orange-500
          focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2
          dark:focus:ring-offset-gray-900
          transition-all duration-300 ease-out
          group gaming-glow
          ${className}
        `}
        style={{
          backgroundColor: 'var(--bg-elevated)',
          borderColor: 'var(--border-primary)',
          color: 'var(--text-primary)'
        }}
        whileHover={{ 
          scale: 1.05,
          boxShadow: 'var(--rgb-glow-strong)'
        }}
        whileTap={{ 
          scale: 0.95,
          transition: { duration: 0.1 }
        }}
        aria-label={`Toggle theme. Current: ${getThemeLabel()}`}
        aria-pressed={theme === 'dark'}
        role="switch"
      >
        {/* Background gradient effect */}
        <motion.div
          className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: theme === 'dark' 
              ? 'linear-gradient(135deg, rgba(0,212,255,0.1), rgba(168,85,247,0.1))'
              : 'linear-gradient(135deg, rgba(255,179,102,0.1), rgba(0,255,136,0.1))'
          }}
        />

        {/* Icon Container */}
        <div className="relative z-10 flex items-center justify-center">
          <AnimatePresence mode="wait">
            {isSystemTheme ? (
              <motion.div
                key="system"
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                <SystemIcon />
              </motion.div>
            ) : theme === 'light' ? (
              <motion.div
                key="sun"
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                <SunIcon />
              </motion.div>
            ) : (
              <motion.div
                key="moon"
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                <MoonIcon />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Gaming RGB border effect */}
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-transparent"
          style={{
            background: isHovered 
              ? 'linear-gradient(45deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary), var(--accent-primary)) border-box'
              : 'none',
            mask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
            maskComposite: 'exclude'
          }}
          animate={{
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.3 }}
        />
      </motion.button>

      {/* Label (optional) */}
      {showLabel && (
        <motion.span
          className="ml-2 text-sm font-medium"
          style={{ color: 'var(--text-secondary)' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          {getThemeLabel()}
        </motion.span>
      )}

      {/* Gaming-style Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          >
            <div
              className="px-3 py-2 text-xs font-medium rounded-lg shadow-lg border"
              style={{
                backgroundColor: 'var(--bg-elevated)',
                borderColor: 'var(--border-secondary)',
                color: 'var(--text-primary)',
                boxShadow: 'var(--shadow-lg), var(--rgb-glow)'
              }}
            >
              {getTooltipText()}
              
              {/* Gaming arrow */}
              <div
                className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
                style={{
                  borderLeft: '4px solid transparent',
                  borderRight: '4px solid transparent',
                  borderTop: '4px solid var(--border-secondary)'
                }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Keyboard shortcut hint */}
      <div className="sr-only">
        Press Ctrl+Shift+T to toggle theme
      </div>
    </div>
  );
};

export default ThemeToggle;
