{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\AdminLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, HomeIcon, ShoppingBagIcon, TagIcon, PhotoIcon, ChartBarIcon, CogIcon, ArrowRightOnRectangleIcon, UserIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    admin,\n    adminLogout,\n    hasPermission\n  } = useAdmin();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    adminLogout();\n    navigate('/admin/login');\n  };\n  const navigationItems = [{\n    name: 'Dashboard',\n    href: '/admin/dashboard',\n    icon: HomeIcon,\n    permission: null\n  }, {\n    name: 'Products',\n    href: '/admin/products',\n    icon: ShoppingBagIcon,\n    permission: 'products'\n  }, {\n    name: 'Categories',\n    href: '/admin/categories',\n    icon: TagIcon,\n    permission: 'categories'\n  }, {\n    name: 'Inventory',\n    href: '/admin/inventory',\n    icon: ClipboardDocumentListIcon,\n    permission: 'inventory'\n  }, {\n    name: 'Media',\n    href: '/admin/media',\n    icon: PhotoIcon,\n    permission: 'media'\n  }, {\n    name: 'Analytics',\n    href: '/admin/analytics',\n    icon: ChartBarIcon,\n    permission: 'analytics'\n  }, {\n    name: 'Settings',\n    href: '/admin/settings',\n    icon: CogIcon,\n    permission: 'settings'\n  }];\n  const filteredNavItems = navigationItems.filter(item => !item.permission || hasPermission(item.permission));\n  const isActive = path => location.pathname === path;\n  const Sidebar = ({\n    mobile = false\n  }) => {\n    var _admin$role;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full border-r shadow-lg theme-transition\",\n      style: {\n        backgroundColor: 'var(--bg-primary)',\n        borderRightColor: 'var(--border-primary)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-gradient-to-r from-light-orange-50 to-white\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/dashboard\",\n          className: \"flex items-center space-x-3 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n              className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold text-gray-900 group-hover:text-light-orange-700 transition-colors\",\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 9\n        }, this), mobile && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(false),\n          className: \"p-2 rounded-md text-gray-400 hover:text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-4 py-6 space-y-1\",\n        children: filteredNavItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          onClick: mobile ? () => setSidebarOpen(false) : undefined,\n          className: `group flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? 'bg-light-orange-100 text-light-orange-800 shadow-sm border-l-4 border-light-orange-500' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'}`,\n          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n            className: `w-5 h-5 transition-colors ${isActive(item.href) ? 'text-light-orange-600' : 'text-gray-500 group-hover:text-gray-700'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, item.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-200 bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 rounded-lg bg-white shadow-sm border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-md\",\n            children: /*#__PURE__*/_jsxDEV(UserIcon, {\n              className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-semibold text-gray-900 truncate\",\n              children: [admin === null || admin === void 0 ? void 0 : admin.firstName, \" \", admin === null || admin === void 0 ? void 0 : admin.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 truncate capitalize\",\n              children: admin === null || admin === void 0 ? void 0 : (_admin$role = admin.role) === null || _admin$role === void 0 ? void 0 : _admin$role.replace('_', ' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"w-full mt-3 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 border border-red-200 hover:border-red-300\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Sign Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n      children: /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: sidebarOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-40 lg:hidden\",\n          onClick: () => setSidebarOpen(false),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-black opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            x: -256\n          },\n          animate: {\n            x: 0\n          },\n          exit: {\n            x: -256\n          },\n          transition: {\n            type: \"spring\",\n            damping: 30,\n            stiffness: 300\n          },\n          className: \"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Sidebar, {\n            mobile: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 bg-white border-b border-gray-200 shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSidebarOpen(true),\n            className: \"lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Admin Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 hidden sm:block\",\n              children: [\"Welcome back, \", admin === null || admin === void 0 ? void 0 : admin.firstName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-2 px-3 py-1 bg-light-orange-50 rounded-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-700\",\n              children: \"Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"p-4 sm:p-6 lg:p-8\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLayout, \"Si/4Yvm9v53R5DJqReN9JpziUeI=\", false, function () {\n  return [useAdmin, useLocation, useNavigate];\n});\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "HomeIcon", "ShoppingBagIcon", "TagIcon", "PhotoIcon", "ChartBarIcon", "CogIcon", "ArrowRightOnRectangleIcon", "UserIcon", "ClipboardDocumentListIcon", "useAdmin", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminLayout", "children", "_s", "sidebarOpen", "setSidebarOpen", "admin", "adminLogout", "hasPermission", "location", "navigate", "handleLogout", "navigationItems", "name", "href", "icon", "permission", "filteredNavItems", "filter", "item", "isActive", "path", "pathname", "Sidebar", "mobile", "_admin$role", "className", "style", "backgroundColor", "borderRightColor", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "undefined", "firstName", "lastName", "role", "replace", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AdminLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  ShoppingBagIcon,\n  TagIcon,\n  PhotoIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  UserIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\n\nconst AdminLayout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { admin, adminLogout, hasPermission } = useAdmin();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    adminLogout();\n    navigate('/admin/login');\n  };\n\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      href: '/admin/dashboard',\n      icon: HomeIcon,\n      permission: null\n    },\n    {\n      name: 'Products',\n      href: '/admin/products',\n      icon: ShoppingBagIcon,\n      permission: 'products'\n    },\n    {\n      name: 'Categories',\n      href: '/admin/categories',\n      icon: TagIcon,\n      permission: 'categories'\n    },\n    {\n      name: 'Inventory',\n      href: '/admin/inventory',\n      icon: ClipboardDocumentListIcon,\n      permission: 'inventory'\n    },\n    {\n      name: 'Media',\n      href: '/admin/media',\n      icon: PhotoIcon,\n      permission: 'media'\n    },\n    {\n      name: 'Analytics',\n      href: '/admin/analytics',\n      icon: ChartBarIcon,\n      permission: 'analytics'\n    },\n    {\n      name: 'Settings',\n      href: '/admin/settings',\n      icon: CogIcon,\n      permission: 'settings'\n    }\n  ];\n\n  const filteredNavItems = navigationItems.filter(item => \n    !item.permission || hasPermission(item.permission)\n  );\n\n  const isActive = (path) => location.pathname === path;\n\n  const Sidebar = ({ mobile = false }) => (\n    <div className=\"flex flex-col h-full border-r shadow-lg theme-transition\"\n         style={{\n           backgroundColor: 'var(--bg-primary)',\n           borderRightColor: 'var(--border-primary)'\n         }}>\n      {/* Logo */}\n      <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-gradient-to-r from-light-orange-50 to-white\">\n        <Link to=\"/admin/dashboard\" className=\"flex items-center space-x-3 group\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow\">\n            <ShoppingBagIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className=\"text-xl font-bold text-gray-900 group-hover:text-light-orange-700 transition-colors\">\n            Admin Panel\n          </span>\n        </Link>\n        {mobile && (\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-600\"\n          >\n            <XMarkIcon className=\"w-6 h-6\" />\n          </button>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-1\">\n        {filteredNavItems.map((item) => (\n          <Link\n            key={item.name}\n            to={item.href}\n            onClick={mobile ? () => setSidebarOpen(false) : undefined}\n            className={`group flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${\n              isActive(item.href)\n                ? 'bg-light-orange-100 text-light-orange-800 shadow-sm border-l-4 border-light-orange-500'\n                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'\n            }`}\n          >\n            <item.icon className={`w-5 h-5 transition-colors ${\n              isActive(item.href) ? 'text-light-orange-600' : 'text-gray-500 group-hover:text-gray-700'\n            }`} />\n            <span className=\"font-medium\">{item.name}</span>\n          </Link>\n        ))}\n      </nav>\n\n      {/* User Info & Logout */}\n      <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n        <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white shadow-sm border border-gray-100\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-md\">\n            <UserIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-gray-900 truncate\">\n              {admin?.firstName} {admin?.lastName}\n            </p>\n            <p className=\"text-xs text-gray-600 truncate capitalize\">\n              {admin?.role?.replace('_', ' ')}\n            </p>\n          </div>\n        </div>\n        <button\n          onClick={handleLogout}\n          className=\"w-full mt-3 flex items-center justify-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 border border-red-200 hover:border-red-300\"\n        >\n          <ArrowRightOnRectangleIcon className=\"w-4 h-4\" />\n          <span>Sign Out</span>\n        </button>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Sidebar */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 lg:hidden\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <div className=\"absolute inset-0 bg-black opacity-50\" />\n            </motion.div>\n            <motion.div\n              initial={{ x: -256 }}\n              animate={{ x: 0 }}\n              exit={{ x: -256 }}\n              transition={{ type: \"spring\", damping: 30, stiffness: 300 }}\n              className=\"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\"\n            >\n              <Sidebar mobile />\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Main Content */}\n      <div className=\"lg:pl-64\">\n        {/* Top Header */}\n        <header className=\"sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Bars3Icon className=\"w-6 h-6\" />\n            </button>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Admin Dashboard\n              </h1>\n              <p className=\"text-sm text-gray-600 hidden sm:block\">\n                Welcome back, {admin?.firstName}\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"hidden md:flex items-center space-x-2 px-3 py-1 bg-light-orange-50 rounded-full\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n              <span className=\"text-sm text-gray-700\">Online</span>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,OAAO,EACPC,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,yBAAyB,EACzBC,QAAQ,EACRC,yBAAyB,QACpB,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAE2B,KAAK;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EACxD,MAAMa,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9B,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBJ,WAAW,CAAC,CAAC;IACbG,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE5B,QAAQ;IACd6B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE3B,eAAe;IACrB4B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE1B,OAAO;IACb2B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAEpB,yBAAyB;IAC/BqB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEzB,SAAS;IACf0B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAExB,YAAY;IAClByB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAEvB,OAAO;IACbwB,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGL,eAAe,CAACM,MAAM,CAACC,IAAI,IAClD,CAACA,IAAI,CAACH,UAAU,IAAIR,aAAa,CAACW,IAAI,CAACH,UAAU,CACnD,CAAC;EAED,MAAMI,QAAQ,GAAIC,IAAI,IAAKZ,QAAQ,CAACa,QAAQ,KAAKD,IAAI;EAErD,MAAME,OAAO,GAAGA,CAAC;IAAEC,MAAM,GAAG;EAAM,CAAC;IAAA,IAAAC,WAAA;IAAA,oBACjC3B,OAAA;MAAK4B,SAAS,EAAC,0DAA0D;MACpEC,KAAK,EAAE;QACLC,eAAe,EAAE,mBAAmB;QACpCC,gBAAgB,EAAE;MACpB,CAAE;MAAA3B,QAAA,gBAELJ,OAAA;QAAK4B,SAAS,EAAC,qHAAqH;QAAAxB,QAAA,gBAClIJ,OAAA,CAAClB,IAAI;UAACkD,EAAE,EAAC,kBAAkB;UAACJ,SAAS,EAAC,mCAAmC;UAAAxB,QAAA,gBACvEJ,OAAA;YAAK4B,SAAS,EAAC,kKAAkK;YAAAxB,QAAA,eAC/KJ,OAAA,CAACV,eAAe;cAACsC,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNpC,OAAA;YAAM4B,SAAS,EAAC,qFAAqF;YAAAxB,QAAA,EAAC;UAEtG;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACNV,MAAM,iBACL1B,OAAA;UACEqC,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC,KAAK,CAAE;UACrCqB,SAAS,EAAC,kDAAkD;UAAAxB,QAAA,eAE5DJ,OAAA,CAACZ,SAAS;YAACwC,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpC,OAAA;QAAK4B,SAAS,EAAC,4BAA4B;QAAAxB,QAAA,EACxCe,gBAAgB,CAACmB,GAAG,CAAEjB,IAAI,iBACzBrB,OAAA,CAAClB,IAAI;UAEHkD,EAAE,EAAEX,IAAI,CAACL,IAAK;UACdqB,OAAO,EAAEX,MAAM,GAAG,MAAMnB,cAAc,CAAC,KAAK,CAAC,GAAGgC,SAAU;UAC1DX,SAAS,EAAE,0GACTN,QAAQ,CAACD,IAAI,CAACL,IAAI,CAAC,GACf,wFAAwF,GACxF,oEAAoE,EACvE;UAAAZ,QAAA,gBAEHJ,OAAA,CAACqB,IAAI,CAACJ,IAAI;YAACW,SAAS,EAAE,6BACpBN,QAAQ,CAACD,IAAI,CAACL,IAAI,CAAC,GAAG,uBAAuB,GAAG,yCAAyC;UACxF;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACNpC,OAAA;YAAM4B,SAAS,EAAC,aAAa;YAAAxB,QAAA,EAAEiB,IAAI,CAACN;UAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAZ3Cf,IAAI,CAACN,IAAI;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNpC,OAAA;QAAK4B,SAAS,EAAC,yCAAyC;QAAAxB,QAAA,gBACtDJ,OAAA;UAAK4B,SAAS,EAAC,sFAAsF;UAAAxB,QAAA,gBACnGJ,OAAA;YAAK4B,SAAS,EAAC,8HAA8H;YAAAxB,QAAA,eAC3IJ,OAAA,CAACJ,QAAQ;cAACgC,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNpC,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAxB,QAAA,gBAC7BJ,OAAA;cAAG4B,SAAS,EAAC,8CAA8C;cAAAxB,QAAA,GACxDI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC,SAAS,EAAC,GAAC,EAAChC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiC,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACJpC,OAAA;cAAG4B,SAAS,EAAC,2CAA2C;cAAAxB,QAAA,EACrDI,KAAK,aAALA,KAAK,wBAAAmB,WAAA,GAALnB,KAAK,CAAEkC,IAAI,cAAAf,WAAA,uBAAXA,WAAA,CAAagB,OAAO,CAAC,GAAG,EAAE,GAAG;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpC,OAAA;UACEqC,OAAO,EAAExB,YAAa;UACtBe,SAAS,EAAC,wNAAwN;UAAAxB,QAAA,gBAElOJ,OAAA,CAACL,yBAAyB;YAACiC,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDpC,OAAA;YAAAI,QAAA,EAAM;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,oBACEpC,OAAA;IAAK4B,SAAS,EAAC,yBAAyB;IAAAxB,QAAA,gBAEtCJ,OAAA;MAAK4B,SAAS,EAAC,0DAA0D;MAAAxB,QAAA,eACvEJ,OAAA,CAACyB,OAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNpC,OAAA,CAACd,eAAe;MAAAkB,QAAA,EACbE,WAAW,iBACVN,OAAA,CAAAE,SAAA;QAAAE,QAAA,gBACEJ,OAAA,CAACf,MAAM,CAAC2D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,IAAI,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACrBlB,SAAS,EAAC,8BAA8B;UACxCS,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC,KAAK,CAAE;UAAAH,QAAA,eAErCJ,OAAA;YAAK4B,SAAS,EAAC;UAAsC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACbpC,OAAA,CAACf,MAAM,CAAC2D,GAAG;UACTC,OAAO,EAAE;YAAEI,CAAC,EAAE,CAAC;UAAI,CAAE;UACrBF,OAAO,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAClBD,IAAI,EAAE;YAAEC,CAAC,EAAE,CAAC;UAAI,CAAE;UAClBC,UAAU,EAAE;YAAEC,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC5DzB,SAAS,EAAC,4CAA4C;UAAAxB,QAAA,eAEtDJ,OAAA,CAACyB,OAAO;YAACC,MAAM;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,eACb;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBpC,OAAA;MAAK4B,SAAS,EAAC,UAAU;MAAAxB,QAAA,gBAEvBJ,OAAA;QAAQ4B,SAAS,EAAC,2HAA2H;QAAAxB,QAAA,gBAC3IJ,OAAA;UAAK4B,SAAS,EAAC,6BAA6B;UAAAxB,QAAA,gBAC1CJ,OAAA;YACEqC,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC,IAAI,CAAE;YACpCqB,SAAS,EAAC,gGAAgG;YAAAxB,QAAA,eAE1GJ,OAAA,CAACb,SAAS;cAACyC,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACTpC,OAAA;YAAAI,QAAA,gBACEJ,OAAA;cAAI4B,SAAS,EAAC,kCAAkC;cAAAxB,QAAA,EAAC;YAEjD;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpC,OAAA;cAAG4B,SAAS,EAAC,uCAAuC;cAAAxB,QAAA,GAAC,gBACrC,EAACI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC,SAAS;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpC,OAAA;UAAK4B,SAAS,EAAC,6BAA6B;UAAAxB,QAAA,eAC1CJ,OAAA;YAAK4B,SAAS,EAAC,iFAAiF;YAAAxB,QAAA,gBAC9FJ,OAAA;cAAK4B,SAAS,EAAC;YAAmC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDpC,OAAA;cAAM4B,SAAS,EAAC,uBAAuB;cAAAxB,QAAA,EAAC;YAAM;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTpC,OAAA;QAAM4B,SAAS,EAAC,mBAAmB;QAAAxB,QAAA,EAChCA;MAAQ;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA3MIF,WAAW;EAAA,QAE+BL,QAAQ,EACrCf,WAAW,EACXC,WAAW;AAAA;AAAAsE,EAAA,GAJxBnD,WAAW;AA6MjB,eAAeA,WAAW;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}