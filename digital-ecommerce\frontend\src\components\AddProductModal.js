import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  PhotoIcon,
  PlusIcon,
  TrashIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline';
import { categories } from '../data/products';

const AddProductModal = ({ isOpen, onClose, onSubmit }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    shortDescription: '',
    price: '',
    discountPrice: '',
    currency: 'USD',
    category: '',
    subcategory: '',
    type: 'physical',
    stockCount: '',
    sku: '',
    tags: [],
    keywords: '',
    isActive: true,
    isFeatured: false,
    specifications: {},
    images: [],
    // PC Gaming specific fields
    componentType: '',
    pcType: '',
    compatibility: {},
    performance: {},
    features: [],
    warranty: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [newTag, setNewTag] = useState('');

  // Auto-generate SKU when product name changes
  useEffect(() => {
    if (formData.name && !formData.sku) {
      const sku = formData.name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .substring(0, 8) + '-' + Date.now().toString().slice(-4);
      setFormData(prev => ({ ...prev, sku }));
    }
  }, [formData.name]);

  const steps = [
    { id: 1, name: 'Basic Info', description: 'Product name, description, and category' },
    { id: 2, name: 'Pricing', description: 'Price, discounts, and currency' },
    { id: 3, name: 'Images', description: 'Product photos and media' },
    { id: 4, name: 'Details', description: 'Stock, SKU, and specifications' },
    { id: 5, name: 'Settings', description: 'Tags, keywords, and publication' }
  ];

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = 'Product name is required';
        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';
        if (!formData.description.trim()) newErrors.description = 'Description is required';
        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';
        if (!formData.category) newErrors.category = 'Category is required';
        break;
      case 2:
        if (!formData.price) newErrors.price = 'Price is required';
        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';
        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {
          newErrors.discountPrice = 'Discount price must be a positive number';
        }
        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {
          newErrors.discountPrice = 'Discount price must be less than regular price';
        }
        break;
      case 3:
        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';
        break;
      case 4:
        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {
          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';
        }
        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrev = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (files) => {
    const newImages = Array.from(files).map(file => ({
      id: Date.now() + Math.random(),
      file,
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size
    }));

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...newImages]
    }));
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files);
    }
  };

  const removeImage = (imageId) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img.id !== imageId)
    }));
  };

  const moveImage = (fromIndex, toIndex) => {
    const newImages = [...formData.images];
    const [removed] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, removed);
    setFormData(prev => ({ ...prev, images: newImages }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      // Reset form only if submission was successful
      setFormData({
        name: '',
        description: '',
        shortDescription: '',
        price: '',
        discountPrice: '',
        currency: 'USD',
        category: '',
        subcategory: '',
        type: 'physical',
        stockCount: '',
        sku: '',
        tags: [],
        keywords: '',
        isActive: true,
        isFeatured: false,
        specifications: {},
        images: []
      });
      setCurrentStep(1);
      setErrors({});
    } catch (error) {
      // Error handling is now done in the parent component
      console.error('Error creating product:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedCategory = categories.find(cat => cat.id === formData.category);

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter product name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Short Description
              </label>
              <input
                type="text"
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                placeholder="Brief product description"
                maxLength={150}
              />
              <p className="mt-1 text-xs text-gray-500">{formData.shortDescription.length}/150 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Detailed product description"
                maxLength={2000}
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
              <p className="mt-1 text-xs text-gray-500">{formData.description.length}/2000 characters</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                    errors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subcategory
                </label>
                <select
                  value={formData.subcategory}
                  onChange={(e) => handleInputChange('subcategory', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                  disabled={!selectedCategory?.subcategories}
                >
                  <option value="">Select a subcategory</option>
                  {selectedCategory?.subcategories?.map(sub => (
                    <option key={sub} value={sub}>
                      {sub.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Type
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="physical"
                    checked={formData.type === 'physical'}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="mr-2 text-light-orange-600 focus:ring-light-orange-500"
                  />
                  Physical Product
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="digital"
                    checked={formData.type === 'digital'}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="mr-2 text-light-orange-600 focus:ring-light-orange-500"
                  />
                  Digital Product
                </label>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price * ({formData.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                    errors.price ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="0.00"
                />
                {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  value={formData.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                  <option value="CAD">CAD (C$)</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Discount Price ({formData.currency})
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.discountPrice}
                onChange={(e) => handleInputChange('discountPrice', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                  errors.discountPrice ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="0.00 (optional)"
              />
              {errors.discountPrice && <p className="mt-1 text-sm text-red-600">{errors.discountPrice}</p>}
              <p className="mt-1 text-xs text-gray-500">Leave empty if no discount</p>
            </div>

            {formData.price && formData.discountPrice && (
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-800">Discount Amount:</span>
                  <span className="text-sm font-bold text-green-800">
                    {formData.currency} {(parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm font-medium text-green-800">Discount Percentage:</span>
                  <span className="text-sm font-bold text-green-800">
                    {(((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price)) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Images *
              </label>
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  dragActive
                    ? 'border-light-orange-500 bg-light-orange-50'
                    : errors.images
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-light-orange-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Drop images here or click to upload
                    </span>
                    <span className="mt-1 block text-xs text-gray-500">
                      PNG, JPG, GIF up to 10MB each
                    </span>
                  </label>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                  />
                </div>
              </div>
              {errors.images && <p className="mt-1 text-sm text-red-600">{errors.images}</p>}
            </div>

            {formData.images.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Uploaded Images ({formData.images.length})
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={image.url}
                        alt={image.name}
                        className="w-full h-32 object-cover rounded-lg border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <button
                          onClick={() => removeImage(image.id)}
                          className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                      {index === 0 && (
                        <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                          Main
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  The first image will be used as the main product image. Drag to reorder.
                </p>
              </div>
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SKU (Stock Keeping Unit) *
                </label>
                <input
                  type="text"
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value.toUpperCase())}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                    errors.sku ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="AUTO-GENERATED"
                />
                {errors.sku && <p className="mt-1 text-sm text-red-600">{errors.sku}</p>}
                <p className="mt-1 text-xs text-gray-500">Unique identifier for this product</p>
              </div>

              {formData.type === 'physical' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stock Quantity *
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.stockCount}
                    onChange={(e) => handleInputChange('stockCount', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${
                      errors.stockCount ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="0"
                  />
                  {errors.stockCount && <p className="mt-1 text-sm text-red-600">{errors.stockCount}</p>}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Specifications
              </label>
              <div className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Specification name (e.g., Weight)"
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                  />
                  <input
                    type="text"
                    placeholder="Value (e.g., 1.5 kg)"
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                  />
                </div>
                <button
                  type="button"
                  className="flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Add Specification</span>
                </button>
              </div>
            </div>

            {/* PC Gaming Specific Fields */}
            {formData.category === 'pc-gaming' && (
              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900">PC Gaming Details</h3>

                {formData.subcategory === 'pc-component' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Component Type *
                    </label>
                    <select
                      value={formData.componentType}
                      onChange={(e) => handleInputChange('componentType', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                    >
                      <option value="">Select Component Type</option>
                      <option value="cpu">CPU/Processor</option>
                      <option value="gpu">Graphics Card</option>
                      <option value="motherboard">Motherboard</option>
                      <option value="memory">Memory/RAM</option>
                      <option value="storage">Storage</option>
                      <option value="psu">Power Supply</option>
                      <option value="case">PC Case</option>
                      <option value="cooling">Cooling</option>
                    </select>
                  </div>
                )}

                {formData.subcategory === 'pre-built-pc' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      PC Type *
                    </label>
                    <select
                      value={formData.pcType}
                      onChange={(e) => handleInputChange('pcType', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                    >
                      <option value="">Select PC Type</option>
                      <option value="entry-gaming">Entry Gaming ($800-1200)</option>
                      <option value="mid-range-gaming">Mid-Range Gaming ($1200-2000)</option>
                      <option value="high-end-gaming">High-End Gaming ($2000+)</option>
                      <option value="workstation">Workstation</option>
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Warranty Period
                  </label>
                  <input
                    type="text"
                    value={formData.warranty}
                    onChange={(e) => handleInputChange('warranty', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                    placeholder="e.g., 3 years manufacturer warranty"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800"
                  >
                    {tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-2 text-light-orange-600 hover:text-light-orange-800"
                    >
                      <XMarkIcon className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                  placeholder="Add a tag"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600"
                >
                  Add
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Keywords (for search)
              </label>
              <textarea
                value={formData.keywords}
                onChange={(e) => handleInputChange('keywords', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500"
                placeholder="Enter keywords separated by commas"
              />
              <p className="mt-1 text-xs text-gray-500">Help customers find this product</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  className="mr-3 text-light-orange-600 focus:ring-light-orange-500"
                />
                <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                  Publish product (make it visible to customers)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isFeatured"
                  checked={formData.isFeatured}
                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                  className="mr-3 text-light-orange-600 focus:ring-light-orange-500"
                />
                <label htmlFor="isFeatured" className="text-sm font-medium text-gray-700">
                  Feature this product (show in featured sections)
                </label>
              </div>
            </div>

            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Product Summary</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>Name:</strong> {formData.name || 'Not set'}</p>
                <p><strong>Price:</strong> {formData.currency} {formData.price || '0.00'}</p>
                <p><strong>Category:</strong> {categories.find(c => c.id === formData.category)?.name || 'Not set'}</p>
                <p><strong>Type:</strong> {formData.type}</p>
                <p><strong>Images:</strong> {formData.images.length} uploaded</p>
                <p><strong>Status:</strong> {formData.isActive ? 'Active' : 'Draft'}</p>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Step content for step {currentStep}</div>;
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Add New Product</h2>
              <p className="text-sm text-gray-600 mt-1">
                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Progress Steps */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep > step.id
                      ? 'bg-green-500 text-white'
                      : currentStep === step.id
                        ? 'bg-light-orange-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                  }`}>
                    {currentStep > step.id ? '✓' : step.id}
                  </div>
                  <span className={`ml-2 text-sm font-medium ${
                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {renderStepContent()}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
            <button
              onClick={handlePrev}
              disabled={currentStep === 1}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              
              {currentStep < steps.length ? (
                <button
                  onClick={handleNext}
                  className="px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600"
                >
                  Next
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Creating...' : 'Create Product'}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AddProductModal;
