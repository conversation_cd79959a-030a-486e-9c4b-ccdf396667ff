import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import { useAdmin } from '../contexts/AdminContext';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';
import AdminLayout from '../components/AdminLayout';
import AddProductModal from '../components/AddProductModal';
import ConfirmationModal from '../components/ConfirmationModal';

const AdminProductsPage = () => {
  const { hasPermission } = useAdmin();
  const { products, categories, addProduct, deleteProduct } = useProducts();
  const { showSuccess, showError } = useToast();
  const [viewMode, setViewMode] = useState('grid');
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesType = selectedType === 'all' || product.type === selectedType;
      
      return matchesSearch && matchesCategory && matchesType;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return a.price - b.price;
        case 'stock':
          return (b.stockCount || 0) - (a.stockCount || 0);
        case 'category':
          return a.category.localeCompare(b.category);
        default:
          return 0;
      }
    });

    return filtered;
  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);

  const handleSelectProduct = (productId) => {
    setSelectedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  const handleDeleteProduct = (product) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  };

  const handleBulkDelete = () => {
    if (selectedProducts.length > 0) {
      setShowBulkDeleteConfirm(true);
    }
  };

  const confirmDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteProduct(productToDelete.id);
      if (result.success) {
        showSuccess('Product Deleted', `${productToDelete.name} has been successfully deleted.`);
        setShowDeleteConfirm(false);
        setProductToDelete(null);
      } else {
        showError('Delete Failed', result.error || 'Failed to delete product. Please try again.');
      }
    } catch (error) {
      showError('Delete Failed', 'An unexpected error occurred while deleting the product.');
    } finally {
      setIsDeleting(false);
    }
  };

  const confirmBulkDelete = async () => {
    if (selectedProducts.length === 0) return;

    setIsDeleting(true);
    try {
      const deletePromises = selectedProducts.map(productId => deleteProduct(productId));
      const results = await Promise.all(deletePromises);

      const successCount = results.filter(result => result.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        showSuccess('Products Deleted', `${successCount} product(s) successfully deleted.`);
      }
      if (failCount > 0) {
        showError('Some Deletions Failed', `${failCount} product(s) could not be deleted.`);
      }

      setSelectedProducts([]);
      setShowBulkDeleteConfirm(false);
    } catch (error) {
      showError('Delete Failed', 'An unexpected error occurred while deleting products.');
    } finally {
      setIsDeleting(false);
    }
  };

  const ProductCard = ({ product }) => (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${
        selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''
      }`}
    >
      <div className="relative">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-48 object-cover rounded-lg"
        />
        <div className="absolute top-2 left-2">
          <input
            type="checkbox"
            checked={selectedProducts.includes(product.id)}
            onChange={() => handleSelectProduct(product.id)}
            className="w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500"
          />
        </div>
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            product.inStock 
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {product.inStock ? 'In Stock' : 'Out of Stock'}
          </span>
        </div>
      </div>

      <div className="mt-4">
        <h3 className="font-semibold truncate text-gray-900">
          {product.name}
        </h3>
        <p className="text-sm mt-1 text-gray-600">
          {product.category}
        </p>
        <div className="flex items-center justify-between mt-3">
          <span className="text-lg font-bold text-light-orange-600">
            ${product.price}
          </span>
          {product.stockCount && (
            <span className="text-sm text-gray-500">
              Stock: {product.stockCount}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <button
            className="p-2 rounded-lg transition-colors hover:bg-gray-100"
            title="View Product"
          >
            <EyeIcon className="w-4 h-4 text-gray-500" />
          </button>
          {hasPermission('products') && (
            <button
              className="p-2 rounded-lg transition-colors hover:bg-gray-100"
              title="Edit Product"
            >
              <PencilIcon className="w-4 h-4 text-blue-500" />
            </button>
          )}
          {hasPermission('products') && (
            <button
              onClick={() => handleDeleteProduct(product)}
              className="p-2 rounded-lg transition-colors hover:bg-red-50"
              title="Delete Product"
              disabled={isDeleting}
            >
              <TrashIcon className="w-4 h-4 text-red-500" />
            </button>
          )}
        </div>
        <span className={`text-xs px-2 py-1 rounded-full ${
          product.type === 'digital' 
            ? 'bg-blue-100 text-blue-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {product.type}
        </span>
      </div>
    </motion.div>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Products
            </h1>
            <p className="mt-2 text-gray-600">
              Manage your product catalog
            </p>
          </div>
          {hasPermission('products') && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowAddProductModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              <span>Add Product</span>
            </motion.button>
          )}
        </div>

        {/* Toolbar */}
        <div className="p-6 rounded-xl shadow-lg bg-white">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              {/* Filters */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100"
              >
                <FunnelIcon className="w-5 h-5" />
                <span>Filters</span>
              </button>

              {/* View Mode */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-white shadow-sm' 
                      : 'hover:bg-gray-200'
                  }`}
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white shadow-sm' 
                      : 'hover:bg-gray-200'
                  }`}
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-gray-200"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>

                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900"
                  >
                    <option value="all">All Types</option>
                    <option value="physical">Physical</option>
                    <option value="digital">Digital</option>
                  </select>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900"
                  >
                    <option value="name">Sort by Name</option>
                    <option value="price">Sort by Price</option>
                    <option value="stock">Sort by Stock</option>
                    <option value="category">Sort by Category</option>
                  </select>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Results Info */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Showing {filteredProducts.length} of {products.length} products
          </p>
          {selectedProducts.length > 0 && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {selectedProducts.length} selected
              </span>
              <button
                onClick={handleBulkDelete}
                disabled={isDeleting}
                className="px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isDeleting ? 'Deleting...' : 'Delete Selected'}
              </button>
            </div>
          )}
        </div>

        {/* Products Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Add Product Modal */}
      <AddProductModal
        isOpen={showAddProductModal}
        onClose={() => setShowAddProductModal(false)}
        onSubmit={async (productData) => {
          const result = await addProduct(productData);
          if (result.success) {
            setShowAddProductModal(false);
            showSuccess('Product Added', `${productData.name} has been successfully added to your catalog.`);
          } else {
            showError('Add Product Failed', result.error || 'Failed to add product. Please try again.');
          }
        }}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false);
          setProductToDelete(null);
        }}
        onConfirm={confirmDeleteProduct}
        title="Delete Product"
        message={`Are you sure you want to delete "${productToDelete?.name}"? This action cannot be undone.`}
        confirmText={isDeleting ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        type="danger"
      />

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showBulkDeleteConfirm}
        onClose={() => setShowBulkDeleteConfirm(false)}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Products"
        message={`Are you sure you want to delete ${selectedProducts.length} selected product(s)? This action cannot be undone.`}
        confirmText={isDeleting ? "Deleting..." : "Delete All"}
        cancelText="Cancel"
        type="danger"
      />
    </AdminLayout>
  );
};

export default AdminProductsPage;
