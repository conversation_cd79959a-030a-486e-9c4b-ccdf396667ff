{"ast": null, "code": "const generateLinearEasing = (easing, duration,\n// as milliseconds\nresolution = 10 // as milliseconds\n) => {\n  let points = \"\";\n  const numPoints = Math.max(Math.round(duration / resolution), 2);\n  for (let i = 0; i < numPoints; i++) {\n    points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n  }\n  return `linear(${points.substring(0, points.length - 2)})`;\n};\nexport { generateLinearEasing };", "map": {"version": 3, "names": ["generateLinearEasing", "easing", "duration", "resolution", "points", "numPoints", "Math", "max", "round", "i", "substring", "length"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs"], "sourcesContent": ["const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,QAAQ;AAAE;AAChDC,UAAU,GAAG,EAAE,CAAC;AAAA,KACX;EACD,IAAIC,MAAM,GAAG,EAAE;EACf,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACN,QAAQ,GAAGC,UAAU,CAAC,EAAE,CAAC,CAAC;EAChE,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;IAChCL,MAAM,IAAIE,IAAI,CAACE,KAAK,CAACP,MAAM,CAACQ,CAAC,IAAIJ,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI;EAC5E;EACA,OAAO,UAAUD,MAAM,CAACM,SAAS,CAAC,CAAC,EAAEN,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC,GAAG;AAC9D,CAAC;AAED,SAASX,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}