{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\PcBuilder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { CpuChipIcon, ComputerDesktopIcon, CircleStackIcon, BoltIcon, CubeIcon, FanIcon, ExclamationTriangleIcon, CheckCircleIcon, ShoppingCartIcon, BookmarkIcon } from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PcBuilder = () => {\n  _s();\n  var _componentCategories$;\n  const {\n    getPcGamingProducts,\n    checkCompatibility,\n    calculateBundlePrice\n  } = useProducts();\n  const {\n    showSuccess,\n    showError,\n    showWarning\n  } = useToast();\n  const [selectedComponents, setSelectedComponents] = useState({\n    cpu: null,\n    gpu: null,\n    motherboard: null,\n    memory: null,\n    storage: null,\n    psu: null,\n    case: null,\n    cooling: null\n  });\n  const [activeCategory, setActiveCategory] = useState('cpu');\n  const [compatibilityCheck, setCompatibilityCheck] = useState({\n    compatible: true,\n    issues: []\n  });\n  const [totalPrice, setTotalPrice] = useState(0);\n  const [buildName, setBuildName] = useState('My Gaming PC');\n  const componentCategories = [{\n    id: 'cpu',\n    name: 'Processor',\n    icon: CpuChipIcon,\n    required: true\n  }, {\n    id: 'gpu',\n    name: 'Graphics Card',\n    icon: ComputerDesktopIcon,\n    required: true\n  }, {\n    id: 'motherboard',\n    name: 'Motherboard',\n    icon: CircleStackIcon,\n    required: true\n  }, {\n    id: 'memory',\n    name: 'Memory (RAM)',\n    icon: CircleStackIcon,\n    required: true\n  }, {\n    id: 'storage',\n    name: 'Storage',\n    icon: CircleStackIcon,\n    required: true\n  }, {\n    id: 'psu',\n    name: 'Power Supply',\n    icon: BoltIcon,\n    required: true\n  }, {\n    id: 'case',\n    name: 'PC Case',\n    icon: CubeIcon,\n    required: false\n  }, {\n    id: 'cooling',\n    name: 'Cooling',\n    icon: FanIcon,\n    required: false\n  }];\n\n  // Get components for the active category\n  const availableComponents = getPcGamingProducts('pc-component', activeCategory);\n  useEffect(() => {\n    // Calculate total price\n    const components = Object.values(selectedComponents).filter(Boolean);\n    const price = components.reduce((sum, component) => sum + component.price, 0);\n    setTotalPrice(price);\n\n    // Check compatibility\n    if (components.length > 1) {\n      const compatibility = checkCompatibility(components);\n      setCompatibilityCheck(compatibility);\n    }\n  }, [selectedComponents, checkCompatibility]);\n  const handleComponentSelect = component => {\n    setSelectedComponents(prev => ({\n      ...prev,\n      [activeCategory]: component\n    }));\n    showSuccess('Component Added', `${component.name} added to your build.`);\n\n    // Auto-advance to next category\n    const currentIndex = componentCategories.findIndex(cat => cat.id === activeCategory);\n    if (currentIndex < componentCategories.length - 1) {\n      setActiveCategory(componentCategories[currentIndex + 1].id);\n    }\n  };\n  const removeComponent = categoryId => {\n    setSelectedComponents(prev => ({\n      ...prev,\n      [categoryId]: null\n    }));\n    showWarning('Component Removed', 'Component removed from your build.');\n  };\n  const saveBuild = () => {\n    const components = Object.values(selectedComponents).filter(Boolean);\n    if (components.length === 0) {\n      showError('Empty Build', 'Please add some components to save your build.');\n      return;\n    }\n\n    // Save to localStorage (in a real app, this would be saved to a backend)\n    const builds = JSON.parse(localStorage.getItem('savedBuilds') || '[]');\n    const newBuild = {\n      id: Date.now(),\n      name: buildName,\n      components: selectedComponents,\n      totalPrice,\n      createdAt: new Date().toISOString()\n    };\n    builds.push(newBuild);\n    localStorage.setItem('savedBuilds', JSON.stringify(builds));\n    showSuccess('Build Saved', `${buildName} has been saved to your builds.`);\n  };\n  const addToCart = () => {\n    const components = Object.values(selectedComponents).filter(Boolean);\n    if (components.length === 0) {\n      showError('Empty Build', 'Please add some components to add to cart.');\n      return;\n    }\n\n    // In a real app, this would add all components to the cart\n    showSuccess('Added to Cart', `All ${components.length} components added to cart.`);\n  };\n  const ComponentCard = ({\n    component\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    whileHover: {\n      scale: 1.02\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: () => handleComponentSelect(component),\n    className: \"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:border-light-orange-300 hover:shadow-md transition-all\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-semibold text-gray-900 text-sm line-clamp-2\",\n        children: component.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-lg font-bold text-light-orange-600 ml-2\",\n        children: [\"$\", component.price]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `w-3 h-3 ${i < Math.floor(component.rating) ? 'text-yellow-400' : 'text-gray-300'}`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-600 ml-1\",\n          children: [\"(\", component.reviews, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xs text-gray-600 line-clamp-2 mb-3\",\n      children: component.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), component.specifications && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: Object.entries(component.specifications).slice(0, 2).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-500\",\n          children: [key, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700 font-medium\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 15\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-2\",\n          children: \"PC Builder Tool\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Build your perfect gaming PC with compatibility checking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg p-6 sticky top-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Your Build\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: buildName,\n                onChange: e => setBuildName(e.target.value),\n                className: \"text-sm border border-gray-300 rounded px-2 py-1 w-32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center space-x-2 p-3 rounded-lg mb-4 ${compatibilityCheck.compatible ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`,\n              children: [compatibilityCheck.compatible ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: compatibilityCheck.compatible ? 'All Compatible' : 'Compatibility Issues'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), !compatibilityCheck.compatible && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: compatibilityCheck.issues.map((issue, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-red-600 bg-red-50 p-2 rounded mb-2\",\n                children: issue\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 mb-6\",\n              children: componentCategories.map(category => {\n                const component = selectedComponents[category.id];\n                const Icon = category.icon;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => setActiveCategory(category.id),\n                  className: `flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${activeCategory === category.id ? 'border-light-orange-300 bg-light-orange-50' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      className: \"w-5 h-5 text-gray-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [category.name, category.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-red-500 ml-1\",\n                          children: \"*\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 244,\n                          columnNumber: 51\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 27\n                      }, this), component ? /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-600 truncate max-w-32\",\n                        children: component.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Not selected\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: component && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [\"$\", component.price]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          removeComponent(category.id);\n                        },\n                        className: \"text-red-500 hover:text-red-700\",\n                        children: \"\\xD7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this)]\n                }, category.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t border-gray-200 pt-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-gray-900\",\n                  children: \"Total:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold text-light-orange-600\",\n                  children: [\"$\", totalPrice.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: addToCart,\n                disabled: totalPrice === 0,\n                className: \"w-full bg-light-orange-500 text-white py-3 rounded-lg hover:bg-light-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add All to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: saveBuild,\n                disabled: totalPrice === 0,\n                className: \"w-full border border-light-orange-500 text-light-orange-600 py-3 rounded-lg hover:bg-light-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(BookmarkIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Save Build\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-6\",\n              children: [\"Select \", (_componentCategories$ = componentCategories.find(cat => cat.id === activeCategory)) === null || _componentCategories$ === void 0 ? void 0 : _componentCategories$.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                children: availableComponents.map(component => /*#__PURE__*/_jsxDEV(ComponentCard, {\n                  component: component\n                }, component.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), availableComponents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(CpuChipIcon, {\n                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"No components available for this category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(PcBuilder, \"waagmKOjqWsUG4dTmTZVcv7Fn1w=\", false, function () {\n  return [useProducts, useToast];\n});\n_c = PcBuilder;\nexport default PcBuilder;\nvar _c;\n$RefreshReg$(_c, \"PcBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "CpuChipIcon", "ComputerDesktopIcon", "CircleStackIcon", "BoltIcon", "CubeIcon", "FanIcon", "ExclamationTriangleIcon", "CheckCircleIcon", "ShoppingCartIcon", "BookmarkIcon", "useProducts", "useToast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PcBuilder", "_s", "_componentCategories$", "getPcGamingProducts", "checkCompatibility", "calculateBundlePrice", "showSuccess", "showError", "showWarning", "selectedComponents", "setSelectedComponents", "cpu", "gpu", "motherboard", "memory", "storage", "psu", "case", "cooling", "activeCategory", "setActiveCategory", "compatibilityCheck", "setCompatibilityCheck", "compatible", "issues", "totalPrice", "setTotalPrice", "buildName", "setBuildName", "componentCategories", "id", "name", "icon", "required", "availableComponents", "components", "Object", "values", "filter", "Boolean", "price", "reduce", "sum", "component", "length", "compatibility", "handleComponentSelect", "prev", "currentIndex", "findIndex", "cat", "removeComponent", "categoryId", "saveBuild", "builds", "JSON", "parse", "localStorage", "getItem", "newBuild", "Date", "now", "createdAt", "toISOString", "push", "setItem", "stringify", "addToCart", "ComponentCard", "div", "whileHover", "scale", "whileTap", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "Math", "floor", "rating", "fill", "viewBox", "d", "reviews", "description", "specifications", "entries", "slice", "key", "value", "type", "onChange", "e", "target", "issue", "index", "category", "Icon", "stopPropagation", "toLocaleString", "disabled", "find", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/PcBuilder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  CpuChipIcon,\n  ComputerDesktopIcon,\n  CircleStackIcon,\n  BoltIcon,\n  CubeIcon,\n  FanIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  ShoppingCartIcon,\n  BookmarkIcon\n} from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\n\nconst PcBuilder = () => {\n  const { getPcGamingProducts, checkCompatibility, calculateBundlePrice } = useProducts();\n  const { showSuccess, showError, showWarning } = useToast();\n  \n  const [selectedComponents, setSelectedComponents] = useState({\n    cpu: null,\n    gpu: null,\n    motherboard: null,\n    memory: null,\n    storage: null,\n    psu: null,\n    case: null,\n    cooling: null\n  });\n  \n  const [activeCategory, setActiveCategory] = useState('cpu');\n  const [compatibilityCheck, setCompatibilityCheck] = useState({ compatible: true, issues: [] });\n  const [totalPrice, setTotalPrice] = useState(0);\n  const [buildName, setBuildName] = useState('My Gaming PC');\n\n  const componentCategories = [\n    { id: 'cpu', name: 'Processor', icon: CpuChipIcon, required: true },\n    { id: 'gpu', name: 'Graphics Card', icon: ComputerDesktopIcon, required: true },\n    { id: 'motherboard', name: 'Motherboard', icon: CircleStackIcon, required: true },\n    { id: 'memory', name: 'Memory (RAM)', icon: CircleStackIcon, required: true },\n    { id: 'storage', name: 'Storage', icon: CircleStackIcon, required: true },\n    { id: 'psu', name: 'Power Supply', icon: BoltIcon, required: true },\n    { id: 'case', name: 'PC Case', icon: CubeIcon, required: false },\n    { id: 'cooling', name: 'Cooling', icon: FanIcon, required: false }\n  ];\n\n  // Get components for the active category\n  const availableComponents = getPcGamingProducts('pc-component', activeCategory);\n\n  useEffect(() => {\n    // Calculate total price\n    const components = Object.values(selectedComponents).filter(Boolean);\n    const price = components.reduce((sum, component) => sum + component.price, 0);\n    setTotalPrice(price);\n\n    // Check compatibility\n    if (components.length > 1) {\n      const compatibility = checkCompatibility(components);\n      setCompatibilityCheck(compatibility);\n    }\n  }, [selectedComponents, checkCompatibility]);\n\n  const handleComponentSelect = (component) => {\n    setSelectedComponents(prev => ({\n      ...prev,\n      [activeCategory]: component\n    }));\n    \n    showSuccess('Component Added', `${component.name} added to your build.`);\n    \n    // Auto-advance to next category\n    const currentIndex = componentCategories.findIndex(cat => cat.id === activeCategory);\n    if (currentIndex < componentCategories.length - 1) {\n      setActiveCategory(componentCategories[currentIndex + 1].id);\n    }\n  };\n\n  const removeComponent = (categoryId) => {\n    setSelectedComponents(prev => ({\n      ...prev,\n      [categoryId]: null\n    }));\n    showWarning('Component Removed', 'Component removed from your build.');\n  };\n\n  const saveBuild = () => {\n    const components = Object.values(selectedComponents).filter(Boolean);\n    if (components.length === 0) {\n      showError('Empty Build', 'Please add some components to save your build.');\n      return;\n    }\n\n    // Save to localStorage (in a real app, this would be saved to a backend)\n    const builds = JSON.parse(localStorage.getItem('savedBuilds') || '[]');\n    const newBuild = {\n      id: Date.now(),\n      name: buildName,\n      components: selectedComponents,\n      totalPrice,\n      createdAt: new Date().toISOString()\n    };\n    \n    builds.push(newBuild);\n    localStorage.setItem('savedBuilds', JSON.stringify(builds));\n    \n    showSuccess('Build Saved', `${buildName} has been saved to your builds.`);\n  };\n\n  const addToCart = () => {\n    const components = Object.values(selectedComponents).filter(Boolean);\n    if (components.length === 0) {\n      showError('Empty Build', 'Please add some components to add to cart.');\n      return;\n    }\n\n    // In a real app, this would add all components to the cart\n    showSuccess('Added to Cart', `All ${components.length} components added to cart.`);\n  };\n\n  const ComponentCard = ({ component }) => (\n    <motion.div\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => handleComponentSelect(component)}\n      className=\"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:border-light-orange-300 hover:shadow-md transition-all\"\n    >\n      <div className=\"flex items-start justify-between mb-3\">\n        <h3 className=\"font-semibold text-gray-900 text-sm line-clamp-2\">\n          {component.name}\n        </h3>\n        <span className=\"text-lg font-bold text-light-orange-600 ml-2\">\n          ${component.price}\n        </span>\n      </div>\n      \n      <div className=\"flex items-center space-x-2 mb-2\">\n        <div className=\"flex items-center\">\n          {[...Array(5)].map((_, i) => (\n            <svg\n              key={i}\n              className={`w-3 h-3 ${\n                i < Math.floor(component.rating) ? 'text-yellow-400' : 'text-gray-300'\n              }`}\n              fill=\"currentColor\"\n              viewBox=\"0 0 20 20\"\n            >\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n          ))}\n          <span className=\"text-xs text-gray-600 ml-1\">({component.reviews})</span>\n        </div>\n      </div>\n\n      <p className=\"text-xs text-gray-600 line-clamp-2 mb-3\">\n        {component.description}\n      </p>\n\n      {component.specifications && (\n        <div className=\"space-y-1\">\n          {Object.entries(component.specifications).slice(0, 2).map(([key, value]) => (\n            <div key={key} className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">{key}:</span>\n              <span className=\"text-gray-700 font-medium\">{value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">PC Builder Tool</h1>\n          <p className=\"text-gray-600\">Build your perfect gaming PC with compatibility checking</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Build Summary */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-xl shadow-lg p-6 sticky top-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-xl font-bold text-gray-900\">Your Build</h2>\n                <input\n                  type=\"text\"\n                  value={buildName}\n                  onChange={(e) => setBuildName(e.target.value)}\n                  className=\"text-sm border border-gray-300 rounded px-2 py-1 w-32\"\n                />\n              </div>\n\n              {/* Compatibility Status */}\n              <div className={`flex items-center space-x-2 p-3 rounded-lg mb-4 ${\n                compatibilityCheck.compatible \n                  ? 'bg-green-50 text-green-800' \n                  : 'bg-red-50 text-red-800'\n              }`}>\n                {compatibilityCheck.compatible ? (\n                  <CheckCircleIcon className=\"w-5 h-5\" />\n                ) : (\n                  <ExclamationTriangleIcon className=\"w-5 h-5\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {compatibilityCheck.compatible ? 'All Compatible' : 'Compatibility Issues'}\n                </span>\n              </div>\n\n              {/* Compatibility Issues */}\n              {!compatibilityCheck.compatible && (\n                <div className=\"mb-4\">\n                  {compatibilityCheck.issues.map((issue, index) => (\n                    <div key={index} className=\"text-sm text-red-600 bg-red-50 p-2 rounded mb-2\">\n                      {issue}\n                    </div>\n                  ))}\n                </div>\n              )}\n\n              {/* Component List */}\n              <div className=\"space-y-3 mb-6\">\n                {componentCategories.map(category => {\n                  const component = selectedComponents[category.id];\n                  const Icon = category.icon;\n                  \n                  return (\n                    <div\n                      key={category.id}\n                      onClick={() => setActiveCategory(category.id)}\n                      className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${\n                        activeCategory === category.id\n                          ? 'border-light-orange-300 bg-light-orange-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <Icon className=\"w-5 h-5 text-gray-600\" />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {category.name}\n                            {category.required && <span className=\"text-red-500 ml-1\">*</span>}\n                          </p>\n                          {component ? (\n                            <p className=\"text-xs text-gray-600 truncate max-w-32\">\n                              {component.name}\n                            </p>\n                          ) : (\n                            <p className=\"text-xs text-gray-500\">Not selected</p>\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-2\">\n                        {component && (\n                          <>\n                            <span className=\"text-sm font-medium text-gray-900\">\n                              ${component.price}\n                            </span>\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                removeComponent(category.id);\n                              }}\n                              className=\"text-red-500 hover:text-red-700\"\n                            >\n                              ×\n                            </button>\n                          </>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n\n              {/* Total Price */}\n              <div className=\"border-t border-gray-200 pt-4 mb-6\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-bold text-gray-900\">Total:</span>\n                  <span className=\"text-2xl font-bold text-light-orange-600\">\n                    ${totalPrice.toLocaleString()}\n                  </span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"space-y-3\">\n                <button\n                  onClick={addToCart}\n                  disabled={totalPrice === 0}\n                  className=\"w-full bg-light-orange-500 text-white py-3 rounded-lg hover:bg-light-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\"\n                >\n                  <ShoppingCartIcon className=\"w-5 h-5\" />\n                  <span>Add All to Cart</span>\n                </button>\n                \n                <button\n                  onClick={saveBuild}\n                  disabled={totalPrice === 0}\n                  className=\"w-full border border-light-orange-500 text-light-orange-600 py-3 rounded-lg hover:bg-light-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\"\n                >\n                  <BookmarkIcon className=\"w-5 h-5\" />\n                  <span>Save Build</span>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Component Selection */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                Select {componentCategories.find(cat => cat.id === activeCategory)?.name}\n              </h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <AnimatePresence>\n                  {availableComponents.map(component => (\n                    <ComponentCard key={component.id} component={component} />\n                  ))}\n                </AnimatePresence>\n              </div>\n\n              {availableComponents.length === 0 && (\n                <div className=\"text-center py-8\">\n                  <CpuChipIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600\">No components available for this category</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PcBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,uBAAuB,EACvBC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,QACP,6BAA6B;AACpC,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtB,MAAM;IAAEC,mBAAmB;IAAEC,kBAAkB;IAAEC;EAAqB,CAAC,GAAGX,WAAW,CAAC,CAAC;EACvF,MAAM;IAAEY,WAAW;IAAEC,SAAS;IAAEC;EAAY,CAAC,GAAGb,QAAQ,CAAC,CAAC;EAE1D,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC;IAC3D+B,GAAG,EAAE,IAAI;IACTC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1C,QAAQ,CAAC;IAAE2C,UAAU,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAG,CAAC,CAAC;EAC9F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,cAAc,CAAC;EAE1D,MAAMiD,mBAAmB,GAAG,CAC1B;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEhD,WAAW;IAAEiD,QAAQ,EAAE;EAAK,CAAC,EACnE;IAAEH,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE/C,mBAAmB;IAAEgD,QAAQ,EAAE;EAAK,CAAC,EAC/E;IAAEH,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE9C,eAAe;IAAE+C,QAAQ,EAAE;EAAK,CAAC,EACjF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE9C,eAAe;IAAE+C,QAAQ,EAAE;EAAK,CAAC,EAC7E;IAAEH,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE9C,eAAe;IAAE+C,QAAQ,EAAE;EAAK,CAAC,EACzE;IAAEH,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE7C,QAAQ;IAAE8C,QAAQ,EAAE;EAAK,CAAC,EACnE;IAAEH,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE5C,QAAQ;IAAE6C,QAAQ,EAAE;EAAM,CAAC,EAChE;IAAEH,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE3C,OAAO;IAAE4C,QAAQ,EAAE;EAAM,CAAC,CACnE;;EAED;EACA,MAAMC,mBAAmB,GAAG/B,mBAAmB,CAAC,cAAc,EAAEgB,cAAc,CAAC;EAE/EtC,SAAS,CAAC,MAAM;IACd;IACA,MAAMsD,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC5B,kBAAkB,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC;IACpE,MAAMC,KAAK,GAAGL,UAAU,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACH,KAAK,EAAE,CAAC,CAAC;IAC7Ed,aAAa,CAACc,KAAK,CAAC;;IAEpB;IACA,IAAIL,UAAU,CAACS,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMC,aAAa,GAAGzC,kBAAkB,CAAC+B,UAAU,CAAC;MACpDb,qBAAqB,CAACuB,aAAa,CAAC;IACtC;EACF,CAAC,EAAE,CAACpC,kBAAkB,EAAEL,kBAAkB,CAAC,CAAC;EAE5C,MAAM0C,qBAAqB,GAAIH,SAAS,IAAK;IAC3CjC,qBAAqB,CAACqC,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAAC5B,cAAc,GAAGwB;IACpB,CAAC,CAAC,CAAC;IAEHrC,WAAW,CAAC,iBAAiB,EAAE,GAAGqC,SAAS,CAACZ,IAAI,uBAAuB,CAAC;;IAExE;IACA,MAAMiB,YAAY,GAAGnB,mBAAmB,CAACoB,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACpB,EAAE,KAAKX,cAAc,CAAC;IACpF,IAAI6B,YAAY,GAAGnB,mBAAmB,CAACe,MAAM,GAAG,CAAC,EAAE;MACjDxB,iBAAiB,CAACS,mBAAmB,CAACmB,YAAY,GAAG,CAAC,CAAC,CAAClB,EAAE,CAAC;IAC7D;EACF,CAAC;EAED,MAAMqB,eAAe,GAAIC,UAAU,IAAK;IACtC1C,qBAAqB,CAACqC,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACK,UAAU,GAAG;IAChB,CAAC,CAAC,CAAC;IACH5C,WAAW,CAAC,mBAAmB,EAAE,oCAAoC,CAAC;EACxE,CAAC;EAED,MAAM6C,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMlB,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC5B,kBAAkB,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC;IACpE,IAAIJ,UAAU,CAACS,MAAM,KAAK,CAAC,EAAE;MAC3BrC,SAAS,CAAC,aAAa,EAAE,gDAAgD,CAAC;MAC1E;IACF;;IAEA;IACA,MAAM+C,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;IACtE,MAAMC,QAAQ,GAAG;MACf7B,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;MACd9B,IAAI,EAAEJ,SAAS;MACfQ,UAAU,EAAE1B,kBAAkB;MAC9BgB,UAAU;MACVqC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;IACpC,CAAC;IAEDT,MAAM,CAACU,IAAI,CAACL,QAAQ,CAAC;IACrBF,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAEV,IAAI,CAACW,SAAS,CAACZ,MAAM,CAAC,CAAC;IAE3DhD,WAAW,CAAC,aAAa,EAAE,GAAGqB,SAAS,iCAAiC,CAAC;EAC3E,CAAC;EAED,MAAMwC,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMhC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC5B,kBAAkB,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC;IACpE,IAAIJ,UAAU,CAACS,MAAM,KAAK,CAAC,EAAE;MAC3BrC,SAAS,CAAC,aAAa,EAAE,4CAA4C,CAAC;MACtE;IACF;;IAEA;IACAD,WAAW,CAAC,eAAe,EAAE,OAAO6B,UAAU,CAACS,MAAM,4BAA4B,CAAC;EACpF,CAAC;EAED,MAAMwB,aAAa,GAAGA,CAAC;IAAEzB;EAAU,CAAC,kBAClC9C,OAAA,CAACf,MAAM,CAACuF,GAAG;IACTC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEA,CAAA,KAAM3B,qBAAqB,CAACH,SAAS,CAAE;IAChD+B,SAAS,EAAC,4HAA4H;IAAAC,QAAA,gBAEtI9E,OAAA;MAAK6E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD9E,OAAA;QAAI6E,SAAS,EAAC,kDAAkD;QAAAC,QAAA,EAC7DhC,SAAS,CAACZ;MAAI;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACLlF,OAAA;QAAM6E,SAAS,EAAC,8CAA8C;QAAAC,QAAA,GAAC,GAC5D,EAAChC,SAAS,CAACH,KAAK;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENlF,OAAA;MAAK6E,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/C9E,OAAA;QAAK6E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/B,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBtF,OAAA;UAEE6E,SAAS,EAAE,WACTS,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC1C,SAAS,CAAC2C,MAAM,CAAC,GAAG,iBAAiB,GAAG,eAAe,EACrE;UACHC,IAAI,EAAC,cAAc;UACnBC,OAAO,EAAC,WAAW;UAAAb,QAAA,eAEnB9E,OAAA;YAAM4F,CAAC,EAAC;UAA0V;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAPhWI,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQH,CACN,CAAC,eACFlF,OAAA;UAAM6E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,GAAC,EAAChC,SAAS,CAAC+C,OAAO,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlF,OAAA;MAAG6E,SAAS,EAAC,yCAAyC;MAAAC,QAAA,EACnDhC,SAAS,CAACgD;IAAW;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,EAEHpC,SAAS,CAACiD,cAAc,iBACvB/F,OAAA;MAAK6E,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBvC,MAAM,CAACyD,OAAO,CAAClD,SAAS,CAACiD,cAAc,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAC,CAAC,CAACc,GAAG,EAAEC,KAAK,CAAC,kBACrEnG,OAAA;QAAe6E,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBACrD9E,OAAA;UAAM6E,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAEoB,GAAG,EAAC,GAAC;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7ClF,OAAA;UAAM6E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEqB;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFlDgB,GAAG;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGR,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CACb;EAED,oBACElF,OAAA;IAAK6E,SAAS,EAAC,8DAA8D;IAAAC,QAAA,eAC3E9E,OAAA;MAAK6E,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9E,OAAA;QAAK6E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9E,OAAA;UAAI6E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ElF,OAAA;UAAG6E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eAENlF,OAAA;QAAK6E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD9E,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9E,OAAA;YAAK6E,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D9E,OAAA;cAAK6E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9E,OAAA;gBAAI6E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DlF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACXD,KAAK,EAAErE,SAAU;gBACjBuE,QAAQ,EAAGC,CAAC,IAAKvE,YAAY,CAACuE,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;gBAC9CtB,SAAS,EAAC;cAAuD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlF,OAAA;cAAK6E,SAAS,EAAE,mDACdrD,kBAAkB,CAACE,UAAU,GACzB,4BAA4B,GAC5B,wBAAwB,EAC3B;cAAAoD,QAAA,GACAtD,kBAAkB,CAACE,UAAU,gBAC5B1B,OAAA,CAACN,eAAe;gBAACmF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEvClF,OAAA,CAACP,uBAAuB;gBAACoF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC/C,eACDlF,OAAA;gBAAM6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAClCtD,kBAAkB,CAACE,UAAU,GAAG,gBAAgB,GAAG;cAAsB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGL,CAAC1D,kBAAkB,CAACE,UAAU,iBAC7B1B,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClBtD,kBAAkB,CAACG,MAAM,CAACyD,GAAG,CAAC,CAACoB,KAAK,EAAEC,KAAK,kBAC1CzG,OAAA;gBAAiB6E,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,EACzE0B;cAAK,GADEC,KAAK;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAGDlF,OAAA;cAAK6E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B9C,mBAAmB,CAACoD,GAAG,CAACsB,QAAQ,IAAI;gBACnC,MAAM5D,SAAS,GAAGlC,kBAAkB,CAAC8F,QAAQ,CAACzE,EAAE,CAAC;gBACjD,MAAM0E,IAAI,GAAGD,QAAQ,CAACvE,IAAI;gBAE1B,oBACEnC,OAAA;kBAEE4E,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAACmF,QAAQ,CAACzE,EAAE,CAAE;kBAC9C4C,SAAS,EAAE,4FACTvD,cAAc,KAAKoF,QAAQ,CAACzE,EAAE,GAC1B,4CAA4C,GAC5C,uCAAuC,EAC1C;kBAAA6C,QAAA,gBAEH9E,OAAA;oBAAK6E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C9E,OAAA,CAAC2G,IAAI;sBAAC9B,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1ClF,OAAA;sBAAA8E,QAAA,gBACE9E,OAAA;wBAAG6E,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAC7C4B,QAAQ,CAACxE,IAAI,EACbwE,QAAQ,CAACtE,QAAQ,iBAAIpC,OAAA;0BAAM6E,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAAC;wBAAC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC,EACHpC,SAAS,gBACR9C,OAAA;wBAAG6E,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,EACnDhC,SAAS,CAACZ;sBAAI;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,gBAEJlF,OAAA;wBAAG6E,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CACrD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlF,OAAA;oBAAK6E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EACzChC,SAAS,iBACR9C,OAAA,CAAAE,SAAA;sBAAA4E,QAAA,gBACE9E,OAAA;wBAAM6E,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAC,GACjD,EAAChC,SAAS,CAACH,KAAK;sBAAA;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACPlF,OAAA;wBACE4E,OAAO,EAAG0B,CAAC,IAAK;0BACdA,CAAC,CAACM,eAAe,CAAC,CAAC;0BACnBtD,eAAe,CAACoD,QAAQ,CAACzE,EAAE,CAAC;wBAC9B,CAAE;wBACF4C,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAC5C;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA1CDwB,QAAQ,CAACzE,EAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2Cb,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlF,OAAA;cAAK6E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjD9E,OAAA;gBAAK6E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD9E,OAAA;kBAAM6E,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DlF,OAAA;kBAAM6E,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,GACxD,EAAClD,UAAU,CAACiF,cAAc,CAAC,CAAC;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9E,OAAA;gBACE4E,OAAO,EAAEN,SAAU;gBACnBwC,QAAQ,EAAElF,UAAU,KAAK,CAAE;gBAC3BiD,SAAS,EAAC,8LAA8L;gBAAAC,QAAA,gBAExM9E,OAAA,CAACL,gBAAgB;kBAACkF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxClF,OAAA;kBAAA8E,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAETlF,OAAA;gBACE4E,OAAO,EAAEpB,SAAU;gBACnBsD,QAAQ,EAAElF,UAAU,KAAK,CAAE;gBAC3BiD,SAAS,EAAC,mNAAmN;gBAAAC,QAAA,gBAE7N9E,OAAA,CAACJ,YAAY;kBAACiF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpClF,OAAA;kBAAA8E,QAAA,EAAM;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9E,OAAA;YAAK6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9E,OAAA;cAAI6E,SAAS,EAAC,sCAAsC;cAAAC,QAAA,GAAC,SAC5C,GAAAzE,qBAAA,GAAC2B,mBAAmB,CAAC+E,IAAI,CAAC1D,GAAG,IAAIA,GAAG,CAACpB,EAAE,KAAKX,cAAc,CAAC,cAAAjB,qBAAA,uBAA1DA,qBAAA,CAA4D6B,IAAI;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAELlF,OAAA;cAAK6E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpD9E,OAAA,CAACd,eAAe;gBAAA4F,QAAA,EACbzC,mBAAmB,CAAC+C,GAAG,CAACtC,SAAS,iBAChC9C,OAAA,CAACuE,aAAa;kBAAoBzB,SAAS,EAAEA;gBAAU,GAAnCA,SAAS,CAACb,EAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAyB,CAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EAEL7C,mBAAmB,CAACU,MAAM,KAAK,CAAC,iBAC/B/C,OAAA;cAAK6E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9E,OAAA,CAACb,WAAW;gBAAC0F,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChElF,OAAA;gBAAG6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAyC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAjUID,SAAS;EAAA,QAC6DN,WAAW,EACrCC,QAAQ;AAAA;AAAAkH,EAAA,GAFpD7G,SAAS;AAmUf,eAAeA,SAAS;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}