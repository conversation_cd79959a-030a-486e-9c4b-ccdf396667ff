{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\nimport ConfirmationModal from '../components/ConfirmationModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProductsPage = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAdmin();\n  const {\n    products,\n    categories,\n    addProduct,\n    deleteProduct\n  } = useProducts();\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      var _product$description;\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.toLowerCase().includes(searchQuery.toLowerCase()));\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n    return filtered;\n  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);\n  const handleSelectProduct = productId => {\n    setSelectedProducts(prev => prev.includes(productId) ? prev.filter(id => id !== productId) : [...prev, productId]);\n  };\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n  const handleDeleteProduct = product => {\n    setProductToDelete(product);\n    setShowDeleteConfirm(true);\n  };\n  const handleBulkDelete = () => {\n    if (selectedProducts.length > 0) {\n      setShowBulkDeleteConfirm(true);\n    }\n  };\n  const confirmDeleteProduct = async () => {\n    if (!productToDelete) return;\n    setIsDeleting(true);\n    try {\n      const result = await deleteProduct(productToDelete.id);\n      if (result.success) {\n        showSuccess('Product Deleted', `${productToDelete.name} has been successfully deleted.`);\n        setShowDeleteConfirm(false);\n        setProductToDelete(null);\n      } else {\n        showError('Delete Failed', result.error || 'Failed to delete product. Please try again.');\n      }\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting the product.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n  const confirmBulkDelete = async () => {\n    if (selectedProducts.length === 0) return;\n    setIsDeleting(true);\n    try {\n      const deletePromises = selectedProducts.map(productId => deleteProduct(productId));\n      const results = await Promise.all(deletePromises);\n      const successCount = results.filter(result => result.success).length;\n      const failCount = results.length - successCount;\n      if (successCount > 0) {\n        showSuccess('Products Deleted', `${successCount} product(s) successfully deleted.`);\n      }\n      if (failCount > 0) {\n        showError('Some Deletions Failed', `${failCount} product(s) could not be deleted.`);\n      }\n      setSelectedProducts([]);\n      setShowBulkDeleteConfirm(false);\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting products.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n  const ProductCard = ({\n    product\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    layout: true,\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.9\n    },\n    className: `p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: product.image,\n        alt: product.name,\n        className: \"w-full h-48 object-cover rounded-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 left-2\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: selectedProducts.includes(product.id),\n          onChange: () => handleSelectProduct(product.id),\n          className: \"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 text-xs font-medium rounded-full ${product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n          children: product.inStock ? 'In Stock' : 'Out of Stock'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-semibold truncate text-gray-900\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm mt-1 text-gray-600\",\n        children: product.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold text-light-orange-600\",\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), product.stockCount && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Stock: \", product.stockCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          title: \"View Product\",\n          children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          title: \"Edit Product\",\n          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n            className: \"w-4 h-4 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleDeleteProduct(product),\n          className: \"p-2 rounded-lg transition-colors hover:bg-red-50\",\n          title: \"Delete Product\",\n          disabled: isDeleting,\n          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full ${product.type === 'digital' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n        children: product.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Manage your product catalog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setShowAddProductModal(true),\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 rounded-xl shadow-lg bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search products...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('grid'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(Squares2X2Icon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('list'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(ListBulletIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showFilters && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto'\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mt-4 pt-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedCategory,\n                onChange: e => setSelectedCategory(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedType,\n                onChange: e => setSelectedType(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"physical\",\n                  children: \"Physical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"digital\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"name\",\n                  children: \"Sort by Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"price\",\n                  children: \"Sort by Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"stock\",\n                  children: \"Sort by Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"category\",\n                  children: \"Sort by Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: [\"Showing \", filteredProducts.length, \" of \", products.length, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), selectedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: [selectedProducts.length, \" selected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBulkDelete,\n            disabled: isDeleting,\n            className: \"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: isDeleting ? 'Deleting...' : 'Delete Selected'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddProductModal, {\n      isOpen: showAddProductModal,\n      onClose: () => setShowAddProductModal(false),\n      onSubmit: async productData => {\n        const result = await addProduct(productData);\n        if (result.success) {\n          setShowAddProductModal(false);\n          showSuccess('Product Added', `${productData.name} has been successfully added to your catalog.`);\n        } else {\n          showError('Add Product Failed', result.error || 'Failed to add product. Please try again.');\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: showDeleteConfirm,\n      onClose: () => {\n        setShowDeleteConfirm(false);\n        setProductToDelete(null);\n      },\n      onConfirm: confirmDeleteProduct,\n      title: \"Delete Product\",\n      message: `Are you sure you want to delete \"${productToDelete === null || productToDelete === void 0 ? void 0 : productToDelete.name}\"? This action cannot be undone.`,\n      confirmText: isDeleting ? \"Deleting...\" : \"Delete\",\n      cancelText: \"Cancel\",\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: showBulkDeleteConfirm,\n      onClose: () => setShowBulkDeleteConfirm(false),\n      onConfirm: confirmBulkDelete,\n      title: \"Delete Multiple Products\",\n      message: `Are you sure you want to delete ${selectedProducts.length} selected product(s)? This action cannot be undone.`,\n      confirmText: isDeleting ? \"Deleting...\" : \"Delete All\",\n      cancelText: \"Cancel\",\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminProductsPage, \"lLQFEqq53p3M05Av7ktyftITeW4=\", false, function () {\n  return [useAdmin, useProducts, useToast];\n});\n_c = AdminProductsPage;\nexport default AdminProductsPage;\nvar _c;\n$RefreshReg$(_c, \"AdminProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "useAdmin", "useProducts", "useToast", "AdminLayout", "AddProductModal", "ConfirmationModal", "jsxDEV", "_jsxDEV", "AdminProductsPage", "_s", "hasPermission", "products", "categories", "addProduct", "deleteProduct", "showSuccess", "showError", "viewMode", "setViewMode", "showAddProductModal", "setShowAddProductModal", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedType", "setSelectedType", "sortBy", "setSortBy", "showFilters", "setShowFilters", "selectedProducts", "setSelectedProducts", "showDeleteConfirm", "setShowDeleteConfirm", "productToDelete", "setProductToDelete", "showBulkDeleteConfirm", "setShowBulkDeleteConfirm", "isDeleting", "setIsDeleting", "filteredProducts", "filtered", "filter", "product", "_product$description", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesType", "type", "sort", "a", "b", "localeCompare", "price", "stockCount", "handleSelectProduct", "productId", "prev", "id", "handleSelectAll", "length", "map", "p", "handleDeleteProduct", "handleBulkDelete", "confirmDeleteProduct", "result", "success", "error", "confirmBulkDelete", "deletePromises", "results", "Promise", "all", "successCount", "failCount", "ProductCard", "div", "layout", "initial", "opacity", "scale", "animate", "exit", "className", "children", "src", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checked", "onChange", "inStock", "title", "onClick", "disabled", "button", "whileHover", "whileTap", "placeholder", "value", "e", "target", "height", "isOpen", "onClose", "onSubmit", "productData", "onConfirm", "message", "confirmText", "cancelText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\nimport ConfirmationModal from '../components/ConfirmationModal';\n\nconst AdminProductsPage = () => {\n  const { hasPermission } = useAdmin();\n  const { products, categories, addProduct, deleteProduct } = useProducts();\n  const { showSuccess, showError } = useToast();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      \n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n\n    return filtered;\n  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);\n\n  const handleSelectProduct = (productId) => {\n    setSelectedProducts(prev => \n      prev.includes(productId) \n        ? prev.filter(id => id !== productId)\n        : [...prev, productId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n\n  const handleDeleteProduct = (product) => {\n    setProductToDelete(product);\n    setShowDeleteConfirm(true);\n  };\n\n  const handleBulkDelete = () => {\n    if (selectedProducts.length > 0) {\n      setShowBulkDeleteConfirm(true);\n    }\n  };\n\n  const confirmDeleteProduct = async () => {\n    if (!productToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      const result = await deleteProduct(productToDelete.id);\n      if (result.success) {\n        showSuccess('Product Deleted', `${productToDelete.name} has been successfully deleted.`);\n        setShowDeleteConfirm(false);\n        setProductToDelete(null);\n      } else {\n        showError('Delete Failed', result.error || 'Failed to delete product. Please try again.');\n      }\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting the product.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const confirmBulkDelete = async () => {\n    if (selectedProducts.length === 0) return;\n\n    setIsDeleting(true);\n    try {\n      const deletePromises = selectedProducts.map(productId => deleteProduct(productId));\n      const results = await Promise.all(deletePromises);\n\n      const successCount = results.filter(result => result.success).length;\n      const failCount = results.length - successCount;\n\n      if (successCount > 0) {\n        showSuccess('Products Deleted', `${successCount} product(s) successfully deleted.`);\n      }\n      if (failCount > 0) {\n        showError('Some Deletions Failed', `${failCount} product(s) could not be deleted.`);\n      }\n\n      setSelectedProducts([]);\n      setShowBulkDeleteConfirm(false);\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting products.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const ProductCard = ({ product }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${\n        selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''\n      }`}\n    >\n      <div className=\"relative\">\n        <img\n          src={product.image}\n          alt={product.name}\n          className=\"w-full h-48 object-cover rounded-lg\"\n        />\n        <div className=\"absolute top-2 left-2\">\n          <input\n            type=\"checkbox\"\n            checked={selectedProducts.includes(product.id)}\n            onChange={() => handleSelectProduct(product.id)}\n            className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n          />\n        </div>\n        <div className=\"absolute top-2 right-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n            product.inStock \n              ? 'bg-green-100 text-green-800'\n              : 'bg-red-100 text-red-800'\n          }`}>\n            {product.inStock ? 'In Stock' : 'Out of Stock'}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"mt-4\">\n        <h3 className=\"font-semibold truncate text-gray-900\">\n          {product.name}\n        </h3>\n        <p className=\"text-sm mt-1 text-gray-600\">\n          {product.category}\n        </p>\n        <div className=\"flex items-center justify-between mt-3\">\n          <span className=\"text-lg font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.stockCount && (\n            <span className=\"text-sm text-gray-500\">\n              Stock: {product.stockCount}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <button\n            className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n            title=\"View Product\"\n          >\n            <EyeIcon className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          {hasPermission('products') && (\n            <button\n              className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n              title=\"Edit Product\"\n            >\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n          )}\n          {hasPermission('products') && (\n            <button\n              onClick={() => handleDeleteProduct(product)}\n              className=\"p-2 rounded-lg transition-colors hover:bg-red-50\"\n              title=\"Delete Product\"\n              disabled={isDeleting}\n            >\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          )}\n        </div>\n        <span className={`text-xs px-2 py-1 rounded-full ${\n          product.type === 'digital' \n            ? 'bg-blue-100 text-blue-800'\n            : 'bg-gray-100 text-gray-800'\n        }`}>\n          {product.type}\n        </span>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Products\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Manage your product catalog\n            </p>\n          </div>\n          {hasPermission('products') && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddProductModal(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5\" />\n              <span>Add Product</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Toolbar */}\n        <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"\n              />\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Filters */}\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\"\n              >\n                <FunnelIcon className=\"w-5 h-5\" />\n                <span>Filters</span>\n              </button>\n\n              {/* View Mode */}\n              <div className=\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'grid' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <Squares2X2Icon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'list' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <ListBulletIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters Panel */}\n          <AnimatePresence>\n            {showFilters && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-4 pt-4 border-t border-gray-200\"\n              >\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Categories</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n\n                  <select\n                    value={selectedType}\n                    onChange={(e) => setSelectedType(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Types</option>\n                    <option value=\"physical\">Physical</option>\n                    <option value=\"digital\">Digital</option>\n                  </select>\n\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"name\">Sort by Name</option>\n                    <option value=\"price\">Sort by Price</option>\n                    <option value=\"stock\">Sort by Stock</option>\n                    <option value=\"category\">Sort by Category</option>\n                  </select>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Results Info */}\n        <div className=\"flex items-center justify-between\">\n          <p className=\"text-sm text-gray-600\">\n            Showing {filteredProducts.length} of {products.length} products\n          </p>\n          {selectedProducts.length > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedProducts.length} selected\n              </span>\n              <button\n                onClick={handleBulkDelete}\n                disabled={isDeleting}\n                className=\"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isDeleting ? 'Deleting...' : 'Delete Selected'}\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Products Display */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          <AnimatePresence>\n            {filteredProducts.map(product => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            showSuccess('Product Added', `${productData.name} has been successfully added to your catalog.`);\n          } else {\n            showError('Add Product Failed', result.error || 'Failed to add product. Please try again.');\n          }\n        }}\n      />\n\n      {/* Delete Confirmation Modal */}\n      <ConfirmationModal\n        isOpen={showDeleteConfirm}\n        onClose={() => {\n          setShowDeleteConfirm(false);\n          setProductToDelete(null);\n        }}\n        onConfirm={confirmDeleteProduct}\n        title=\"Delete Product\"\n        message={`Are you sure you want to delete \"${productToDelete?.name}\"? This action cannot be undone.`}\n        confirmText={isDeleting ? \"Deleting...\" : \"Delete\"}\n        cancelText=\"Cancel\"\n        type=\"danger\"\n      />\n\n      {/* Bulk Delete Confirmation Modal */}\n      <ConfirmationModal\n        isOpen={showBulkDeleteConfirm}\n        onClose={() => setShowBulkDeleteConfirm(false)}\n        onConfirm={confirmBulkDelete}\n        title=\"Delete Multiple Products\"\n        message={`Are you sure you want to delete ${selectedProducts.length} selected product(s)? This action cannot be undone.`}\n        confirmText={isDeleting ? \"Deleting...\" : \"Delete All\"}\n        cancelText=\"Cancel\"\n        type=\"danger\"\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,UAAU,EACVC,cAAc,EACdC,cAAc,QACT,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAc,CAAC,GAAGV,QAAQ,CAAC,CAAC;EACpC,MAAM;IAAEW,QAAQ;IAAEC,UAAU;IAAEC,UAAU;IAAEC;EAAc,CAAC,GAAGb,WAAW,CAAC,CAAC;EACzE,MAAM;IAAEc,WAAW;IAAEC;EAAU,CAAC,GAAGd,QAAQ,CAAC,CAAC;EAC7C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMqD,gBAAgB,GAAGpD,OAAO,CAAC,MAAM;IACrC,IAAIqD,QAAQ,GAAG/B,QAAQ,CAACgC,MAAM,CAACC,OAAO,IAAI;MAAA,IAAAC,oBAAA;MACxC,MAAMC,aAAa,GAAGF,OAAO,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,WAAW,CAAC2B,WAAW,CAAC,CAAC,CAAC,MAAAH,oBAAA,GAC/DD,OAAO,CAACM,WAAW,cAAAL,oBAAA,uBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,WAAW,CAAC2B,WAAW,CAAC,CAAC,CAAC;MAC3F,MAAMG,eAAe,GAAG5B,gBAAgB,KAAK,KAAK,IAAIqB,OAAO,CAACQ,QAAQ,KAAK7B,gBAAgB;MAC3F,MAAM8B,WAAW,GAAG5B,YAAY,KAAK,KAAK,IAAImB,OAAO,CAACU,IAAI,KAAK7B,YAAY;MAE3E,OAAOqB,aAAa,IAAIK,eAAe,IAAIE,WAAW;IACxD,CAAC,CAAC;;IAEF;IACAX,QAAQ,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,QAAQ9B,MAAM;QACZ,KAAK,MAAM;UACT,OAAO6B,CAAC,CAACT,IAAI,CAACW,aAAa,CAACD,CAAC,CAACV,IAAI,CAAC;QACrC,KAAK,OAAO;UACV,OAAOS,CAAC,CAACG,KAAK,GAAGF,CAAC,CAACE,KAAK;QAC1B,KAAK,OAAO;UACV,OAAO,CAACF,CAAC,CAACG,UAAU,IAAI,CAAC,KAAKJ,CAAC,CAACI,UAAU,IAAI,CAAC,CAAC;QAClD,KAAK,UAAU;UACb,OAAOJ,CAAC,CAACJ,QAAQ,CAACM,aAAa,CAACD,CAAC,CAACL,QAAQ,CAAC;QAC7C;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOV,QAAQ;EACjB,CAAC,EAAE,CAAC/B,QAAQ,EAAEU,WAAW,EAAEE,gBAAgB,EAAEE,YAAY,EAAEE,MAAM,CAAC,CAAC;EAEnE,MAAMkC,mBAAmB,GAAIC,SAAS,IAAK;IACzC9B,mBAAmB,CAAC+B,IAAI,IACtBA,IAAI,CAACd,QAAQ,CAACa,SAAS,CAAC,GACpBC,IAAI,CAACpB,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKF,SAAS,CAAC,GACnC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIlC,gBAAgB,CAACmC,MAAM,KAAKzB,gBAAgB,CAACyB,MAAM,EAAE;MACvDlC,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,MAAM;MACLA,mBAAmB,CAACS,gBAAgB,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,mBAAmB,GAAIzB,OAAO,IAAK;IACvCR,kBAAkB,CAACQ,OAAO,CAAC;IAC3BV,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvC,gBAAgB,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC/B5B,wBAAwB,CAAC,IAAI,CAAC;IAChC;EACF,CAAC;EAED,MAAMiC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACpC,eAAe,EAAE;IAEtBK,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMgC,MAAM,GAAG,MAAM1D,aAAa,CAACqB,eAAe,CAAC6B,EAAE,CAAC;MACtD,IAAIQ,MAAM,CAACC,OAAO,EAAE;QAClB1D,WAAW,CAAC,iBAAiB,EAAE,GAAGoB,eAAe,CAACY,IAAI,iCAAiC,CAAC;QACxFb,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLpB,SAAS,CAAC,eAAe,EAAEwD,MAAM,CAACE,KAAK,IAAI,6CAA6C,CAAC;MAC3F;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd1D,SAAS,CAAC,eAAe,EAAE,0DAA0D,CAAC;IACxF,CAAC,SAAS;MACRwB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI5C,gBAAgB,CAACmC,MAAM,KAAK,CAAC,EAAE;IAEnC1B,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMoC,cAAc,GAAG7C,gBAAgB,CAACoC,GAAG,CAACL,SAAS,IAAIhD,aAAa,CAACgD,SAAS,CAAC,CAAC;MAClF,MAAMe,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACH,cAAc,CAAC;MAEjD,MAAMI,YAAY,GAAGH,OAAO,CAAClC,MAAM,CAAC6B,MAAM,IAAIA,MAAM,CAACC,OAAO,CAAC,CAACP,MAAM;MACpE,MAAMe,SAAS,GAAGJ,OAAO,CAACX,MAAM,GAAGc,YAAY;MAE/C,IAAIA,YAAY,GAAG,CAAC,EAAE;QACpBjE,WAAW,CAAC,kBAAkB,EAAE,GAAGiE,YAAY,mCAAmC,CAAC;MACrF;MACA,IAAIC,SAAS,GAAG,CAAC,EAAE;QACjBjE,SAAS,CAAC,uBAAuB,EAAE,GAAGiE,SAAS,mCAAmC,CAAC;MACrF;MAEAjD,mBAAmB,CAAC,EAAE,CAAC;MACvBM,wBAAwB,CAAC,KAAK,CAAC;IACjC,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACd1D,SAAS,CAAC,eAAe,EAAE,uDAAuD,CAAC;IACrF,CAAC,SAAS;MACRwB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM0C,WAAW,GAAGA,CAAC;IAAEtC;EAAQ,CAAC,kBAC9BrC,OAAA,CAACjB,MAAM,CAAC6F,GAAG;IACTC,MAAM;IACNC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACpCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACjCG,SAAS,EAAE,iFACT3D,gBAAgB,CAACkB,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAC,GAAG,8BAA8B,GAAG,EAAE,EAC1E;IAAA2B,QAAA,gBAEHpF,OAAA;MAAKmF,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBpF,OAAA;QACEqF,GAAG,EAAEhD,OAAO,CAACiD,KAAM;QACnBC,GAAG,EAAElD,OAAO,CAACG,IAAK;QAClB2C,SAAS,EAAC;MAAqC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACF3F,OAAA;QAAKmF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCpF,OAAA;UACE+C,IAAI,EAAC,UAAU;UACf6C,OAAO,EAAEpE,gBAAgB,CAACkB,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAE;UAC/CoC,QAAQ,EAAEA,CAAA,KAAMvC,mBAAmB,CAACjB,OAAO,CAACoB,EAAE,CAAE;UAChD0B,SAAS,EAAC;QAA4F;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3F,OAAA;QAAKmF,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCpF,OAAA;UAAMmF,SAAS,EAAE,8CACf9C,OAAO,CAACyD,OAAO,GACX,6BAA6B,GAC7B,yBAAyB,EAC5B;UAAAV,QAAA,EACA/C,OAAO,CAACyD,OAAO,GAAG,UAAU,GAAG;QAAc;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3F,OAAA;MAAKmF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBpF,OAAA;QAAImF,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EACjD/C,OAAO,CAACG;MAAI;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACL3F,OAAA;QAAGmF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACtC/C,OAAO,CAACQ;MAAQ;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACJ3F,OAAA;QAAKmF,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDpF,OAAA;UAAMmF,SAAS,EAAC,yCAAyC;UAAAC,QAAA,GAAC,GACvD,EAAC/C,OAAO,CAACe,KAAK;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EACNtD,OAAO,CAACgB,UAAU,iBACjBrD,OAAA;UAAMmF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,SAC/B,EAAC/C,OAAO,CAACgB,UAAU;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3F,OAAA;MAAKmF,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnFpF,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpF,OAAA;UACEmF,SAAS,EAAC,oDAAoD;UAC9DY,KAAK,EAAC,cAAc;UAAAX,QAAA,eAEpBpF,OAAA,CAACZ,OAAO;YAAC+F,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRxF,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UACEmF,SAAS,EAAC,oDAAoD;UAC9DY,KAAK,EAAC,cAAc;UAAAX,QAAA,eAEpBpF,OAAA,CAACd,UAAU;YAACiG,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACT,EACAxF,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UACEgG,OAAO,EAAEA,CAAA,KAAMlC,mBAAmB,CAACzB,OAAO,CAAE;UAC5C8C,SAAS,EAAC,kDAAkD;UAC5DY,KAAK,EAAC,gBAAgB;UACtBE,QAAQ,EAAEjE,UAAW;UAAAoD,QAAA,eAErBpF,OAAA,CAACb,SAAS;YAACgG,SAAS,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN3F,OAAA;QAAMmF,SAAS,EAAE,kCACf9C,OAAO,CAACU,IAAI,KAAK,SAAS,GACtB,2BAA2B,GAC3B,2BAA2B,EAC9B;QAAAqC,QAAA,EACA/C,OAAO,CAACU;MAAI;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,oBACE3F,OAAA,CAACJ,WAAW;IAAAwF,QAAA,gBACVpF,OAAA;MAAKmF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBpF,OAAA;QAAKmF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3F,OAAA;YAAGmF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLxF,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA,CAACjB,MAAM,CAACmH,MAAM;UACZC,UAAU,EAAE;YAAEnB,KAAK,EAAE;UAAK,CAAE;UAC5BoB,QAAQ,EAAE;YAAEpB,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEA,CAAA,KAAMnF,sBAAsB,CAAC,IAAI,CAAE;UAC5CsE,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIpF,OAAA,CAACf,QAAQ;YAACkG,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC3F,OAAA;YAAAoF,QAAA,EAAM;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3F,OAAA;QAAKmF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpF,OAAA;UAAKmF,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAElGpF,OAAA;YAAKmF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpF,OAAA,CAACX,mBAAmB;cAAC8F,SAAS,EAAC;YAA0E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5G3F,OAAA;cACE+C,IAAI,EAAC,MAAM;cACXsD,WAAW,EAAC,oBAAoB;cAChCC,KAAK,EAAExF,WAAY;cACnB+E,QAAQ,EAAGU,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;cAChDnB,SAAS,EAAC;YAAgK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN3F,OAAA;YAAKmF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1CpF,OAAA;cACEgG,OAAO,EAAEA,CAAA,KAAMzE,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C6D,SAAS,EAAC,sFAAsF;cAAAC,QAAA,gBAEhGpF,OAAA,CAACV,UAAU;gBAAC6F,SAAS,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC3F,OAAA;gBAAAoF,QAAA,EAAM;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGT3F,OAAA;cAAKmF,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrEpF,OAAA;gBACEgG,OAAO,EAAEA,CAAA,KAAMrF,WAAW,CAAC,MAAM,CAAE;gBACnCwE,SAAS,EAAE,oCACTzE,QAAQ,KAAK,MAAM,GACf,oBAAoB,GACpB,mBAAmB,EACtB;gBAAA0E,QAAA,eAEHpF,OAAA,CAACT,cAAc;kBAAC4F,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACT3F,OAAA;gBACEgG,OAAO,EAAEA,CAAA,KAAMrF,WAAW,CAAC,MAAM,CAAE;gBACnCwE,SAAS,EAAE,oCACTzE,QAAQ,KAAK,MAAM,GACf,oBAAoB,GACpB,mBAAmB,EACtB;gBAAA0E,QAAA,eAEHpF,OAAA,CAACR,cAAc;kBAAC2F,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3F,OAAA,CAAChB,eAAe;UAAAoG,QAAA,EACb9D,WAAW,iBACVtB,OAAA,CAACjB,MAAM,CAAC6F,GAAG;YACTE,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAE,CAAE;YACnCxB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAO,CAAE;YACxCvB,IAAI,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAE,CAAE;YAChCtB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eAE9CpF,OAAA;cAAKmF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDpF,OAAA;gBACEsG,KAAK,EAAEtF,gBAAiB;gBACxB6E,QAAQ,EAAGU,CAAC,IAAKtF,mBAAmB,CAACsF,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACrDnB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9EpF,OAAA;kBAAQsG,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CtF,UAAU,CAACuD,GAAG,CAACf,QAAQ,iBACtB7C,OAAA;kBAA0BsG,KAAK,EAAEzD,QAAQ,CAACY,EAAG;kBAAA2B,QAAA,EAC1CvC,QAAQ,CAACL;gBAAI,GADHK,QAAQ,CAACY,EAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAET3F,OAAA;gBACEsG,KAAK,EAAEpF,YAAa;gBACpB2E,QAAQ,EAAGU,CAAC,IAAKpF,eAAe,CAACoF,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACjDnB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9EpF,OAAA;kBAAQsG,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA;kBAAQsG,KAAK,EAAC,UAAU;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C3F,OAAA;kBAAQsG,KAAK,EAAC,SAAS;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAET3F,OAAA;gBACEsG,KAAK,EAAElF,MAAO;gBACdyE,QAAQ,EAAGU,CAAC,IAAKlF,SAAS,CAACkF,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBAC3CnB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9EpF,OAAA;kBAAQsG,KAAK,EAAC,MAAM;kBAAAlB,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3F,OAAA;kBAAQsG,KAAK,EAAC,UAAU;kBAAAlB,QAAA,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGN3F,OAAA;QAAKmF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpF,OAAA;UAAGmF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,UAC3B,EAAClD,gBAAgB,CAACyB,MAAM,EAAC,MAAI,EAACvD,QAAQ,CAACuD,MAAM,EAAC,WACxD;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACHnE,gBAAgB,CAACmC,MAAM,GAAG,CAAC,iBAC1B3D,OAAA;UAAKmF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpF,OAAA;YAAMmF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpC5D,gBAAgB,CAACmC,MAAM,EAAC,WAC3B;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP3F,OAAA;YACEgG,OAAO,EAAEjC,gBAAiB;YAC1BkC,QAAQ,EAAEjE,UAAW;YACrBmD,SAAS,EAAC,uIAAuI;YAAAC,QAAA,EAEhJpD,UAAU,GAAG,aAAa,GAAG;UAAiB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3F,OAAA;QAAKmF,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClFpF,OAAA,CAAChB,eAAe;UAAAoG,QAAA,EACblD,gBAAgB,CAAC0B,GAAG,CAACvB,OAAO,iBAC3BrC,OAAA,CAAC2E,WAAW;YAAkBtC,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAACoB,EAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3F,OAAA,CAACH,eAAe;MACd6G,MAAM,EAAE9F,mBAAoB;MAC5B+F,OAAO,EAAEA,CAAA,KAAM9F,sBAAsB,CAAC,KAAK,CAAE;MAC7C+F,QAAQ,EAAE,MAAOC,WAAW,IAAK;QAC/B,MAAM5C,MAAM,GAAG,MAAM3D,UAAU,CAACuG,WAAW,CAAC;QAC5C,IAAI5C,MAAM,CAACC,OAAO,EAAE;UAClBrD,sBAAsB,CAAC,KAAK,CAAC;UAC7BL,WAAW,CAAC,eAAe,EAAE,GAAGqG,WAAW,CAACrE,IAAI,+CAA+C,CAAC;QAClG,CAAC,MAAM;UACL/B,SAAS,CAAC,oBAAoB,EAAEwD,MAAM,CAACE,KAAK,IAAI,0CAA0C,CAAC;QAC7F;MACF;IAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF3F,OAAA,CAACF,iBAAiB;MAChB4G,MAAM,EAAEhF,iBAAkB;MAC1BiF,OAAO,EAAEA,CAAA,KAAM;QACbhF,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFiF,SAAS,EAAE9C,oBAAqB;MAChC+B,KAAK,EAAC,gBAAgB;MACtBgB,OAAO,EAAE,oCAAoCnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEY,IAAI,kCAAmC;MACrGwE,WAAW,EAAEhF,UAAU,GAAG,aAAa,GAAG,QAAS;MACnDiF,UAAU,EAAC,QAAQ;MACnBlE,IAAI,EAAC;IAAQ;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGF3F,OAAA,CAACF,iBAAiB;MAChB4G,MAAM,EAAE5E,qBAAsB;MAC9B6E,OAAO,EAAEA,CAAA,KAAM5E,wBAAwB,CAAC,KAAK,CAAE;MAC/C+E,SAAS,EAAE1C,iBAAkB;MAC7B2B,KAAK,EAAC,0BAA0B;MAChCgB,OAAO,EAAE,mCAAmCvF,gBAAgB,CAACmC,MAAM,qDAAsD;MACzHqD,WAAW,EAAEhF,UAAU,GAAG,aAAa,GAAG,YAAa;MACvDiF,UAAU,EAAC,QAAQ;MACnBlE,IAAI,EAAC;IAAQ;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAACzF,EAAA,CA7ZID,iBAAiB;EAAA,QACKR,QAAQ,EAC0BC,WAAW,EACpCC,QAAQ;AAAA;AAAAuH,EAAA,GAHvCjH,iBAAiB;AA+ZvB,eAAeA,iBAAiB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}