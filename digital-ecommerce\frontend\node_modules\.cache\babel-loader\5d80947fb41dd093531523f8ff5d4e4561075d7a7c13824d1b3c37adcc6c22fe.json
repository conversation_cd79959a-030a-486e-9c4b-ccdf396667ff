{"ast": null, "code": "import { createProjectionNode } from './create-projection-node.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nconst DocumentProjectionNode = createProjectionNode({\n  attachResizeListener: (ref, notify) => addDomEvent(ref, \"resize\", notify),\n  measureScroll: () => ({\n    x: document.documentElement.scrollLeft || document.body.scrollLeft,\n    y: document.documentElement.scrollTop || document.body.scrollTop\n  }),\n  checkIsScrollRoot: () => true\n});\nexport { DocumentProjectionNode };", "map": {"version": 3, "names": ["createProjectionNode", "addDomEvent", "DocumentProjectionNode", "attachResizeListener", "ref", "notify", "measureScroll", "x", "document", "documentElement", "scrollLeft", "body", "y", "scrollTop", "checkIsScrollRoot"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs"], "sourcesContent": ["import { createProjectionNode } from './create-projection-node.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\n\nconst DocumentProjectionNode = createProjectionNode({\n    attachResizeListener: (ref, notify) => addDomEvent(ref, \"resize\", notify),\n    measureScroll: () => ({\n        x: document.documentElement.scrollLeft || document.body.scrollLeft,\n        y: document.documentElement.scrollTop || document.body.scrollTop,\n    }),\n    checkIsScrollRoot: () => true,\n});\n\nexport { DocumentProjectionNode };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,MAAMC,sBAAsB,GAAGF,oBAAoB,CAAC;EAChDG,oBAAoB,EAAEA,CAACC,GAAG,EAAEC,MAAM,KAAKJ,WAAW,CAACG,GAAG,EAAE,QAAQ,EAAEC,MAAM,CAAC;EACzEC,aAAa,EAAEA,CAAA,MAAO;IAClBC,CAAC,EAAEC,QAAQ,CAACC,eAAe,CAACC,UAAU,IAAIF,QAAQ,CAACG,IAAI,CAACD,UAAU;IAClEE,CAAC,EAAEJ,QAAQ,CAACC,eAAe,CAACI,SAAS,IAAIL,QAAQ,CAACG,IAAI,CAACE;EAC3D,CAAC,CAAC;EACFC,iBAAiB,EAAEA,CAAA,KAAM;AAC7B,CAAC,CAAC;AAEF,SAASZ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}