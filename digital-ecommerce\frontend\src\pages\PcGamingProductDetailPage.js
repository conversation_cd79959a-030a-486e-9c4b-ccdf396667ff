import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ShoppingCartIcon,
  HeartIcon,
  ShareIcon,
  StarIcon,
  CheckCircleIcon,
  TruckIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useProducts } from '../contexts/ProductContext';
import { useCart } from '../components/ShoppingCart';
import { useToast } from '../contexts/ToastContext';
import ProductReviews from '../components/ProductReviews';
import ProductQA from '../components/ProductQA';
import ProductSupport from '../components/ProductSupport';
import CustomerSupportChat from '../components/CustomerSupportChat';

const PcGamingProductDetailPage = () => {
  const { id } = useParams();
  const { getProductById } = useProducts();
  const { addToCart } = useCart();
  const { showSuccess } = useToast();
  const [product, setProduct] = useState(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    const foundProduct = getProductById(id);
    setProduct(foundProduct);
  }, [id, getProductById]);

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-light-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addToCart(product);
    }
    showSuccess('Added to Cart', `${product.name} (${quantity}) added to cart.`);
  };

  const handleAddToWishlist = () => {
    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);
  };

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'specifications', name: 'Specifications' },
    { id: 'reviews', name: 'Reviews' },
    { id: 'qa', name: 'Q&A' },
    { id: 'support', name: 'Support' }
  ];

  const RatingStars = ({ rating, size = 'w-5 h-5' }) => (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <StarIconSolid
          key={star}
          className={`${size} ${
            star <= rating ? 'text-yellow-400' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Product Header */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={product.images[selectedImage]}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              {product.images.length > 1 && (
                <div className="flex space-x-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImage(index)}
                      className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                        selectedImage === index ? 'border-light-orange-500' : 'border-gray-200'
                      }`}
                    >
                      <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
                <div className="flex items-center space-x-4 mb-4">
                  <RatingStars rating={Math.floor(product.rating)} />
                  <span className="text-sm text-gray-600">({product.reviews} reviews)</span>
                  {product.badge && (
                    <span className="bg-light-orange-100 text-light-orange-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      {product.badge}
                    </span>
                  )}
                </div>
                <p className="text-gray-700">{product.description}</p>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-4">
                <span className="text-3xl font-bold text-light-orange-600">${product.price}</span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-xl text-gray-500 line-through">${product.originalPrice}</span>
                )}
              </div>

              {/* Stock Status */}
              <div className="flex items-center space-x-2">
                {product.inStock ? (
                  <>
                    <CheckCircleIcon className="w-5 h-5 text-green-500" />
                    <span className="text-green-600 font-medium">In Stock ({product.stockCount} available)</span>
                  </>
                ) : (
                  <>
                    <XCircleIcon className="w-5 h-5 text-red-500" />
                    <span className="text-red-600 font-medium">Out of Stock</span>
                  </>
                )}
              </div>

              {/* Quantity and Actions */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700">Quantity:</label>
                  <select
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value))}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                  >
                    {[...Array(Math.min(10, product.stockCount))].map((_, i) => (
                      <option key={i + 1} value={i + 1}>{i + 1}</option>
                    ))}
                  </select>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleAddToCart}
                    disabled={!product.inStock}
                    className={`flex-1 flex items-center justify-center space-x-2 py-3 px-6 rounded-lg font-medium transition-colors ${
                      product.inStock
                        ? 'bg-light-orange-500 text-white hover:bg-light-orange-600'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <ShoppingCartIcon className="w-5 h-5" />
                    <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                  </button>
                  <button
                    onClick={handleAddToWishlist}
                    className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <HeartIcon className="w-5 h-5 text-gray-600" />
                  </button>
                  <button className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <ShareIcon className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
              </div>

              {/* Shipping & Warranty */}
              <div className="border-t border-gray-200 pt-6 space-y-3">
                <div className="flex items-center space-x-3">
                  <TruckIcon className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}
                    </p>
                    <p className="text-xs text-gray-600">
                      Estimated delivery: {product.shipping?.estimatedDays} days
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <ShieldCheckIcon className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Warranty</p>
                    <p className="text-xs text-gray-600">{product.warranty}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-light-orange-500 text-light-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-8">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Features</h3>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {product.features?.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {product.testimonials && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Testimonials</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.testimonials.map((testimonial, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <RatingStars rating={testimonial.rating} size="w-4 h-4" />
                            <span className="text-sm font-medium text-gray-900">{testimonial.user}</span>
                            {testimonial.verified && (
                              <CheckCircleIcon className="w-4 h-4 text-green-500" />
                            )}
                          </div>
                          <p className="text-sm text-gray-700">{testimonial.comment}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Technical Specifications</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {product.specifications && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">General Specifications</h4>
                      <dl className="space-y-2">
                        {Object.entries(product.specifications).map(([key, value]) => (
                          <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                            <dt className="text-sm text-gray-600">{key}:</dt>
                            <dd className="text-sm font-medium text-gray-900">{value}</dd>
                          </div>
                        ))}
                      </dl>
                    </div>
                  )}

                  {product.compatibility && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Compatibility</h4>
                      <dl className="space-y-2">
                        {Object.entries(product.compatibility).map(([key, value]) => (
                          <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                            <dt className="text-sm text-gray-600">{key}:</dt>
                            <dd className="text-sm font-medium text-gray-900">
                              {Array.isArray(value) ? value.join(', ') : value}
                            </dd>
                          </div>
                        ))}
                      </dl>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <ProductReviews productId={product.id} productName={product.name} />
            )}

            {activeTab === 'qa' && (
              <ProductQA productId={product.id} productName={product.name} />
            )}

            {activeTab === 'support' && (
              <ProductSupport product={product} />
            )}
          </div>
        </div>
      </div>

      {/* Customer Support Chat */}
      <CustomerSupportChat />
    </div>
  );
};

export default PcGamingProductDetailPage;
