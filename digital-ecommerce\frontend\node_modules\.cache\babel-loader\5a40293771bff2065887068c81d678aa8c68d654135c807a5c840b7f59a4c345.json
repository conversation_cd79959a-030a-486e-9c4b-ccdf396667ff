{"ast": null, "code": "import { invariant, isNumericalString } from 'motion-utils';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON><PERSON>'s special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex =\n// eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive, as it can match a lot of words\n/^var\\(--(?:([\\w-]+)|([\\w-]+), ?([a-zA-Z\\d ()%#.,-]+))\\)/u;\nfunction parseCSSVariable(current) {\n  const match = splitCSSVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token1, token2, fallback] = match;\n  return [`--${token1 ?? token2}`, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n  invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`);\n  const [token, fallback] = parseCSSVariable(current);\n  // No CSS variable detected\n  if (!token) return;\n  // Attempt to read this CSS variable off the element\n  const resolved = window.getComputedStyle(element).getPropertyValue(token);\n  if (resolved) {\n    const trimmed = resolved.trim();\n    return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n  }\n  return isCSSVariableToken(fallback) ? getVariableValue(fallback, element, depth + 1) : fallback;\n}\nexport { getVariableValue, parseCSSVariable };", "map": {"version": 3, "names": ["invariant", "isNumericalString", "isCSSVariableToken", "splitCSSVariableRegex", "parseCSSVariable", "current", "match", "exec", "token1", "token2", "fallback", "max<PERSON><PERSON><PERSON>", "getVariableValue", "element", "depth", "token", "resolved", "window", "getComputedStyle", "getPropertyValue", "trimmed", "trim", "parseFloat"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/utils/css-variables-conversion.mjs"], "sourcesContent": ["import { invariant, isNumericalString } from 'motion-utils';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON><PERSON>'s special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = \n// eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive, as it can match a lot of words\n/^var\\(--(?:([\\w-]+)|([\\w-]+), ?([a-zA-Z\\d ()%#.,-]+))\\)/u;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token1, token2, fallback] = match;\n    return [`--${token1 ?? token2}`, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`);\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    return isCSSVariableToken(fallback)\n        ? getVariableValue(fallback, element, depth + 1)\n        : fallback;\n}\n\nexport { getVariableValue, parseCSSVariable };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,iBAAiB,QAAQ,cAAc;AAC3D,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB;AAC3B;AACA,0DAA0D;AAC1D,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,MAAMC,KAAK,GAAGH,qBAAqB,CAACI,IAAI,CAACF,OAAO,CAAC;EACjD,IAAI,CAACC,KAAK,EACN,OAAO,GAAG;EACd,MAAM,GAAGE,MAAM,EAAEC,MAAM,EAAEC,QAAQ,CAAC,GAAGJ,KAAK;EAC1C,OAAO,CAAC,KAAKE,MAAM,IAAIC,MAAM,EAAE,EAAEC,QAAQ,CAAC;AAC9C;AACA,MAAMC,QAAQ,GAAG,CAAC;AAClB,SAASC,gBAAgBA,CAACP,OAAO,EAAEQ,OAAO,EAAEC,KAAK,GAAG,CAAC,EAAE;EACnDd,SAAS,CAACc,KAAK,IAAIH,QAAQ,EAAE,yDAAyDN,OAAO,sDAAsD,CAAC;EACpJ,MAAM,CAACU,KAAK,EAAEL,QAAQ,CAAC,GAAGN,gBAAgB,CAACC,OAAO,CAAC;EACnD;EACA,IAAI,CAACU,KAAK,EACN;EACJ;EACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,gBAAgB,CAACL,OAAO,CAAC,CAACM,gBAAgB,CAACJ,KAAK,CAAC;EACzE,IAAIC,QAAQ,EAAE;IACV,MAAMI,OAAO,GAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC;IAC/B,OAAOpB,iBAAiB,CAACmB,OAAO,CAAC,GAAGE,UAAU,CAACF,OAAO,CAAC,GAAGA,OAAO;EACrE;EACA,OAAOlB,kBAAkB,CAACQ,QAAQ,CAAC,GAC7BE,gBAAgB,CAACF,QAAQ,EAAEG,OAAO,EAAEC,KAAK,GAAG,CAAC,CAAC,GAC9CJ,QAAQ;AAClB;AAEA,SAASE,gBAAgB,EAAER,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}