{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ModernNavigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\nconst ModernNavigation = () => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigation = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon,\n    description: 'Welcome to ShopHub'\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon,\n    description: 'Browse all products',\n    megaMenu: true,\n    categories: [{\n      name: 'Electronics',\n      href: '/products?category=electronics',\n      icon: '💻'\n    }, {\n      name: 'Software',\n      href: '/products?category=software',\n      icon: '💿'\n    }, {\n      name: 'Gaming',\n      href: '/products?category=gaming',\n      icon: '🎮'\n    }]\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon,\n    description: 'Instant downloads',\n    badge: 'Instant'\n  }, {\n    name: 'PC Gaming',\n    href: '/pc-gaming',\n    icon: TagIcon,\n    description: 'Gaming hardware & software',\n    badge: 'Hot'\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon,\n    description: 'Learn about us'\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon,\n    description: 'Get in touch'\n  }];\n  const userNavigation = [{\n    name: 'Your Profile',\n    href: '/account',\n    icon: UserIcon\n  }, {\n    name: 'Order History',\n    href: '/orders',\n    icon: ShoppingBagIcon\n  }, {\n    name: 'Wishlist',\n    href: '/wishlist',\n    icon: HeartIcon\n  }, {\n    name: 'Settings',\n    href: '/settings',\n    icon: Cog6ToothIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Disclosure, {\n      as: \"nav\",\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${isScrolled ? 'backdrop-blur-xl shadow-xl border-b' : 'backdrop-blur-sm'}`,\n      style: {\n        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',\n        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',\n        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none'\n      },\n      children: ({\n        open\n      }) => /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex h-20 items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"flex items-center space-x-3 group\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    rotate: 360,\n                    scale: 1.1\n                  },\n                  transition: {\n                    duration: 0.6,\n                    type: \"spring\",\n                    stiffness: 200\n                  },\n                  className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                    className: \"w-7 h-7 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold transition-all duration-300 theme-transition\",\n                    style: {\n                      color: 'var(--text-primary)'\n                    },\n                    children: \"ShopHub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs font-medium transition-all duration-300 theme-transition\",\n                    style: {\n                      color: 'var(--accent-primary)'\n                    },\n                    children: \"Premium Store\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:block\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: navigation.map(item => {\n                  var _item$categories;\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    whileHover: {\n                      y: -2\n                    },\n                    transition: {\n                      duration: 0.2\n                    },\n                    className: \"relative group\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: item.href,\n                      className: classNames(isActive(item.href) ? 'text-white shadow-lg' : 'hover:shadow-lg', 'relative flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition'),\n                      style: {\n                        backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'transparent',\n                        color: isActive(item.href) ? 'white' : 'var(--text-primary)',\n                        borderColor: 'var(--border-primary)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"relative z-10\",\n                        children: item.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 170,\n                        columnNumber: 27\n                      }, this), item.badge && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-1 px-2 py-0.5 text-xs font-bold rounded-full bg-red-500 text-white\",\n                        children: item.badge\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 29\n                      }, this), !isActive(item.href) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 25\n                    }, this), item.megaMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-full left-0 mt-2 w-80 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-2xl border border-gray-100 p-6 theme-transition\",\n                        style: {\n                          backgroundColor: 'var(--bg-primary)',\n                          borderColor: 'var(--border-primary)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-lg font-bold theme-transition\",\n                            style: {\n                              color: 'var(--text-primary)'\n                            },\n                            children: \"Product Categories\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 190,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm theme-transition\",\n                            style: {\n                              color: 'var(--text-secondary)'\n                            },\n                            children: \"Explore our diverse product range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 194,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 189,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"grid grid-cols-1 gap-2\",\n                          children: (_item$categories = item.categories) === null || _item$categories === void 0 ? void 0 : _item$categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n                            to: category.href,\n                            className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition\",\n                            style: {\n                              backgroundColor: 'transparent'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-2xl\",\n                              children: category.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 207,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"font-medium theme-transition\",\n                                style: {\n                                  color: 'var(--text-primary)'\n                                },\n                                children: category.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 209,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs theme-transition\",\n                                style: {\n                                  color: 'var(--text-secondary)'\n                                },\n                                children: [\"Browse \", category.name.toLowerCase()]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 213,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 208,\n                              columnNumber: 37\n                            }, this)]\n                          }, category.name, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 201,\n                            columnNumber: 35\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 199,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 27\n                    }, this)]\n                  }, item.name, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center flex-1 max-w-2xl mx-8\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"relative w-full group\",\n                whileHover: {\n                  scale: 1.01\n                },\n                transition: {\n                  duration: 0.2\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: \"h-5 w-5 transition-colors duration-300\",\n                    style: {\n                      color: 'var(--text-secondary)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search products, brands, categories...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: \"w-full pl-12 pr-16 py-4 rounded-2xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition\",\n                  style: {\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-primary)',\n                    color: 'var(--text-primary)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 right-0 pr-4 flex items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"kbd\", {\n                      className: \"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded border theme-transition\",\n                      style: {\n                        backgroundColor: 'var(--bg-tertiary)',\n                        borderColor: 'var(--border-secondary)',\n                        color: 'var(--text-muted)'\n                      },\n                      children: \"\\u2318K\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), searchQuery && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-full left-0 right-0 mt-2 rounded-2xl shadow-2xl border z-50 theme-transition\",\n                  style: {\n                    backgroundColor: 'var(--bg-primary)',\n                    borderColor: 'var(--border-primary)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium mb-2 theme-transition\",\n                      style: {\n                        color: 'var(--text-secondary)'\n                      },\n                      children: \"Quick suggestions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: ['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets'].map(suggestion => /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"w-full text-left px-3 py-2 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition\",\n                        style: {\n                          color: 'var(--text-primary)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                          className: \"w-4 h-4 inline mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 33\n                        }, this), suggestion]\n                      }, suggestion, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/wishlist\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1,\n                    y: -2\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `relative p-3 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'}`,\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Menu, {\n                as: \"div\",\n                className: \"relative ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Menu.Button, {\n                    className: `relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"sr-only\",\n                      children: \"Open user menu\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 27\n                    }, this), user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"h-8 w-8 rounded-full ring-2 ring-white/20\",\n                      src: user.profilePicture,\n                      alt: \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden md:block text-sm font-medium\",\n                      children: (user === null || user === void 0 ? void 0 : user.firstName) || 'Account'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Transition, {\n                  as: Fragment,\n                  enter: \"transition ease-out duration-100\",\n                  enterFrom: \"transform opacity-0 scale-95\",\n                  enterTo: \"transform opacity-100 scale-100\",\n                  leave: \"transition ease-in duration-75\",\n                  leaveFrom: \"transform opacity-100 scale-100\",\n                  leaveTo: \"transform opacity-0 scale-95\",\n                  children: /*#__PURE__*/_jsxDEV(Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"w-12 h-12 rounded-full ring-2 ring-white/30\",\n                          src: user.profilePicture,\n                          alt: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 359,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                          children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                            className: \"w-6 h-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 366,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 365,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"font-semibold text-white\",\n                            children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 370,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-white/80\",\n                            children: user === null || user === void 0 ? void 0 : user.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 373,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 369,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"py-2\",\n                      children: [userNavigation.map(item => /*#__PURE__*/_jsxDEV(Menu.Item, {\n                        children: ({\n                          active\n                        }) => /*#__PURE__*/_jsxDEV(Link, {\n                          to: item.href,\n                          className: classNames(active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700', 'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'),\n                          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                            className: \"w-5 h-5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 390,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: item.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 391,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 383,\n                          columnNumber: 35\n                        }, this)\n                      }, item.name, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"border-t border-gray-100 mt-2 pt-2\",\n                        children: /*#__PURE__*/_jsxDEV(Menu.Item, {\n                          children: ({\n                            active\n                          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: logout,\n                            className: classNames(active ? 'bg-red-50 text-red-600' : 'text-red-600', 'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'),\n                            children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                              className: \"w-5 h-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 406,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Sign out\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 407,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 399,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 397,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: `px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'}`,\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05,\n                      y: -2\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                  className: `relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'} focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Open main menu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: open ? 180 : 0\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: open ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                      className: \"block h-6 w-6\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Disclosure.Panel, {\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto',\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              height: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeInOut\"\n            },\n            className: \"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1 px-6 pb-6 pt-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.1\n                },\n                className: \"relative mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search for products...\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                  className: \"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: navigation.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.1 * (index + 2)\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: Link,\n                    to: item.href,\n                    className: classNames(isActive(item.href) ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg' : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600', 'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'),\n                    children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                      className: classNames(isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500', 'w-6 h-6')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-lg\",\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 25\n                  }, this)\n                }, item.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.4\n                },\n                className: \"flex space-x-4 pt-6\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\",\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(Disclosure.Button, {\n                    as: \"button\",\n                    className: \"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\",\n                    children: \"Sign Up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernNavigation, \"nqhB+dQNtp/nfK+k0r5VlnahLcE=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = ModernNavigation;\nexport default ModernNavigation;\nvar _c;\n$RefreshReg$(_c, \"ModernNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "Link", "useLocation", "motion", "Disclosure", "<PERSON><PERSON>", "Transition", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "Cog6ToothIcon", "ArrowRightOnRectangleIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "_Fragment", "classNames", "classes", "filter", "Boolean", "join", "ModernNavigation", "_s", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "window", "href", "encodeURIComponent", "handleScroll", "scrollY", "addEventListener", "removeEventListener", "navigation", "name", "icon", "description", "megaMenu", "categories", "badge", "userNavigation", "isActive", "path", "pathname", "children", "as", "className", "style", "backgroundColor", "borderBottomColor", "boxShadow", "open", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "map", "item", "_item$categories", "y", "borderColor", "category", "toLowerCase", "placeholder", "value", "onChange", "target", "onKeyDown", "key", "suggestion", "button", "whileTap", "<PERSON><PERSON>", "profilePicture", "src", "alt", "firstName", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Items", "lastName", "email", "<PERSON><PERSON>", "active", "onClick", "animate", "Panel", "initial", "opacity", "height", "exit", "ease", "x", "delay", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ModernNavigation.js"], "sourcesContent": ["import React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst ModernNavigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    {\n      name: 'Home',\n      href: '/',\n      icon: HomeIcon,\n      description: 'Welcome to ShopHub'\n    },\n    {\n      name: 'Products',\n      href: '/products',\n      icon: TagIcon,\n      description: 'Browse all products',\n      megaMenu: true,\n      categories: [\n        { name: 'Electronics', href: '/products?category=electronics', icon: '💻' },\n        { name: 'Software', href: '/products?category=software', icon: '💿' },\n        { name: 'Gaming', href: '/products?category=gaming', icon: '🎮' }\n      ]\n    },\n    {\n      name: 'Digital',\n      href: '/digital-products',\n      icon: TagIcon,\n      description: 'Instant downloads',\n      badge: 'Instant'\n    },\n    {\n      name: 'PC Gaming',\n      href: '/pc-gaming',\n      icon: TagIcon,\n      description: 'Gaming hardware & software',\n      badge: 'Hot'\n    },\n    {\n      name: 'About',\n      href: '/about',\n      icon: InformationCircleIcon,\n      description: 'Learn about us'\n    },\n    {\n      name: 'Contact',\n      href: '/contact',\n      icon: PhoneIcon,\n      description: 'Get in touch'\n    }\n  ];\n\n  const userNavigation = [\n    { name: 'Your Profile', href: '/account', icon: UserIcon },\n    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },\n    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },\n    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <Disclosure as=\"nav\" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 theme-transition ${\n        isScrolled\n          ? 'backdrop-blur-xl shadow-xl border-b'\n          : 'backdrop-blur-sm'\n      }`}\n      style={{\n        backgroundColor: isScrolled ? 'var(--bg-primary)' : 'var(--bg-primary)',\n        borderBottomColor: isScrolled ? 'var(--border-primary)' : 'transparent',\n        boxShadow: isScrolled ? 'var(--shadow-lg)' : 'none'\n      }}>\n        {({ open }) => (\n          <>\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex h-20 items-center justify-between\">\n                {/* Logo */}\n                <div className=\"flex items-center\">\n                  <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n                    <motion.div\n                      whileHover={{ rotate: 360, scale: 1.1 }}\n                      transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                      className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                    >\n                      <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n                    </motion.div>\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-2xl font-bold transition-all duration-300 theme-transition\"\n                            style={{ color: 'var(--text-primary)' }}>\n                        ShopHub\n                      </span>\n                      <span className=\"text-xs font-medium transition-all duration-300 theme-transition\"\n                            style={{ color: 'var(--accent-primary)' }}>\n                        Premium Store\n                      </span>\n                    </div>\n                  </Link>\n                </div>\n\n                {/* Desktop Navigation */}\n                <div className=\"hidden lg:block\">\n                  <div className=\"flex items-center space-x-1\">\n                    {navigation.map((item) => (\n                      <motion.div\n                        key={item.name}\n                        whileHover={{ y: -2 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"relative group\"\n                      >\n                        <Link\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'text-white shadow-lg'\n                              : 'hover:shadow-lg',\n                            'relative flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 theme-transition'\n                          )}\n                          style={{\n                            backgroundColor: isActive(item.href) ? 'var(--accent-primary)' : 'transparent',\n                            color: isActive(item.href) ? 'white' : 'var(--text-primary)',\n                            borderColor: 'var(--border-primary)'\n                          }}\n                        >\n                          <item.icon className=\"w-4 h-4\" />\n                          <span className=\"relative z-10\">{item.name}</span>\n                          {item.badge && (\n                            <span className=\"ml-1 px-2 py-0.5 text-xs font-bold rounded-full bg-red-500 text-white\">\n                              {item.badge}\n                            </span>\n                          )}\n                          {!isActive(item.href) && (\n                            <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                          )}\n                        </Link>\n\n                        {/* Mega Menu for Products */}\n                        {item.megaMenu && (\n                          <div className=\"absolute top-full left-0 mt-2 w-80 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50\">\n                            <div className=\"bg-white rounded-2xl shadow-2xl border border-gray-100 p-6 theme-transition\"\n                                 style={{\n                                   backgroundColor: 'var(--bg-primary)',\n                                   borderColor: 'var(--border-primary)'\n                                 }}>\n                              <div className=\"mb-4\">\n                                <h3 className=\"text-lg font-bold theme-transition\"\n                                    style={{ color: 'var(--text-primary)' }}>\n                                  Product Categories\n                                </h3>\n                                <p className=\"text-sm theme-transition\"\n                                   style={{ color: 'var(--text-secondary)' }}>\n                                  Explore our diverse product range\n                                </p>\n                              </div>\n                              <div className=\"grid grid-cols-1 gap-2\">\n                                {item.categories?.map((category) => (\n                                  <Link\n                                    key={category.name}\n                                    to={category.href}\n                                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition\"\n                                    style={{ backgroundColor: 'transparent' }}\n                                  >\n                                    <span className=\"text-2xl\">{category.icon}</span>\n                                    <div>\n                                      <div className=\"font-medium theme-transition\"\n                                           style={{ color: 'var(--text-primary)' }}>\n                                        {category.name}\n                                      </div>\n                                      <div className=\"text-xs theme-transition\"\n                                           style={{ color: 'var(--text-secondary)' }}>\n                                        Browse {category.name.toLowerCase()}\n                                      </div>\n                                    </div>\n                                  </Link>\n                                ))}\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Enhanced Search Bar */}\n                <div className=\"hidden md:flex items-center flex-1 max-w-2xl mx-8\">\n                  <motion.div\n                    className=\"relative w-full group\"\n                    whileHover={{ scale: 1.01 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\n                      <MagnifyingGlassIcon className=\"h-5 w-5 transition-colors duration-300\"\n                                           style={{ color: 'var(--text-secondary)' }} />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search products, brands, categories...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-16 py-4 rounded-2xl transition-all duration-300 border-2 focus:outline-none shadow-lg hover:shadow-xl theme-transition\"\n                      style={{\n                        backgroundColor: 'var(--bg-secondary)',\n                        borderColor: 'var(--border-primary)',\n                        color: 'var(--text-primary)'\n                      }}\n                    />\n                    <div className=\"absolute inset-y-0 right-0 pr-4 flex items-center\">\n                      <div className=\"flex items-center space-x-2\">\n                        <kbd className=\"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium rounded border theme-transition\"\n                             style={{\n                               backgroundColor: 'var(--bg-tertiary)',\n                               borderColor: 'var(--border-secondary)',\n                               color: 'var(--text-muted)'\n                             }}>\n                          ⌘K\n                        </kbd>\n                      </div>\n                    </div>\n\n                    {/* Search suggestions dropdown */}\n                    {searchQuery && (\n                      <div className=\"absolute top-full left-0 right-0 mt-2 rounded-2xl shadow-2xl border z-50 theme-transition\"\n                           style={{\n                             backgroundColor: 'var(--bg-primary)',\n                             borderColor: 'var(--border-primary)'\n                           }}>\n                        <div className=\"p-4\">\n                          <div className=\"text-sm font-medium mb-2 theme-transition\"\n                               style={{ color: 'var(--text-secondary)' }}>\n                            Quick suggestions\n                          </div>\n                          <div className=\"space-y-2\">\n                            {['Gaming Laptops', 'Microsoft Office', 'Gaming Headsets'].map((suggestion) => (\n                              <button\n                                key={suggestion}\n                                className=\"w-full text-left px-3 py-2 rounded-lg hover:bg-light-orange-50 transition-colors theme-transition\"\n                                style={{ color: 'var(--text-primary)' }}\n                              >\n                                <MagnifyingGlassIcon className=\"w-4 h-4 inline mr-2\" />\n                                {suggestion}\n                              </button>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n                </div>\n\n                {/* Right side items */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Wishlist */}\n                  <Link to=\"/wishlist\">\n                    <motion.button\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`relative p-3 rounded-xl transition-all duration-300 group ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                          : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'\n                      }`}\n                    >\n                      <HeartIcon className=\"w-6 h-6\" />\n                    </motion.button>\n                  </Link>\n\n                  {/* Shopping Cart */}\n                  <div className=\"relative\">\n                    <ShoppingCart />\n                  </div>\n\n                  {/* User menu */}\n                  {isAuthenticated ? (\n                    <Menu as=\"div\" className=\"relative ml-3\">\n                      <div>\n                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                          isScrolled\n                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                            : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm hover:shadow-lg border border-white/20'\n                        }`}>\n                          <span className=\"sr-only\">Open user menu</span>\n                          {user?.profilePicture ? (\n                            <img\n                              className=\"h-8 w-8 rounded-full ring-2 ring-white/20\"\n                              src={user.profilePicture}\n                              alt=\"\"\n                            />\n                          ) : (\n                            <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                              <UserIcon className=\"w-5 h-5 text-white\" />\n                            </div>\n                          )}\n                          <span className=\"hidden md:block text-sm font-medium\">\n                            {user?.firstName || 'Account'}\n                          </span>\n                          <ChevronDownIcon className=\"w-4 h-4\" />\n                        </Menu.Button>\n                      </div>\n                      <Transition\n                        as={Fragment}\n                        enter=\"transition ease-out duration-100\"\n                        enterFrom=\"transform opacity-0 scale-95\"\n                        enterTo=\"transform opacity-100 scale-100\"\n                        leave=\"transition ease-in duration-75\"\n                        leaveFrom=\"transform opacity-100 scale-100\"\n                        leaveTo=\"transform opacity-0 scale-95\"\n                      >\n                        <Menu.Items className=\"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\">\n                          {/* User info header */}\n                          <div className=\"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                            <div className=\"flex items-center space-x-3\">\n                              {user?.profilePicture ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full ring-2 ring-white/30\"\n                                  src={user.profilePicture}\n                                  alt=\"\"\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                  <UserIcon className=\"w-6 h-6 text-white\" />\n                                </div>\n                              )}\n                              <div>\n                                <p className=\"font-semibold text-white\">\n                                  {user?.firstName} {user?.lastName}\n                                </p>\n                                <p className=\"text-sm text-white/80\">{user?.email}</p>\n                              </div>\n                            </div>\n                          </div>\n                          \n                          {/* Menu items */}\n                          <div className=\"py-2\">\n                            {userNavigation.map((item) => (\n                              <Menu.Item key={item.name}>\n                                {({ active }) => (\n                                  <Link\n                                    to={item.href}\n                                    className={classNames(\n                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',\n                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <item.icon className=\"w-5 h-5\" />\n                                    <span>{item.name}</span>\n                                  </Link>\n                                )}\n                              </Menu.Item>\n                            ))}\n                            <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                              <Menu.Item>\n                                {({ active }) => (\n                                  <button\n                                    onClick={logout}\n                                    className={classNames(\n                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',\n                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n                                    <span>Sign out</span>\n                                  </button>\n                                )}\n                              </Menu.Item>\n                            </div>\n                          </div>\n                        </Menu.Items>\n                      </Transition>\n                    </Menu>\n                  ) : (\n                    <div className=\"flex items-center space-x-3\">\n                      <Link to=\"/login\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${\n                            isScrolled\n                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'\n                              : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/40 hover:border-light-orange-200'\n                          }`}\n                        >\n                          Sign In\n                        </motion.button>\n                      </Link>\n                      <Link to=\"/register\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                        >\n                          Sign Up\n                        </motion.button>\n                      </Link>\n                    </div>\n                  )}\n\n                  {/* Mobile menu button */}\n                  <div className=\"lg:hidden\">\n                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-gray-700 hover:text-light-orange-600 hover:bg-white/90 backdrop-blur-sm border border-white/20'\n                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>\n                      <span className=\"sr-only\">Open main menu</span>\n                      <motion.div\n                        animate={{ rotate: open ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {open ? (\n                          <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        ) : (\n                          <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        )}\n                      </motion.div>\n                    </Disclosure.Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu */}\n            <Disclosure.Panel className=\"lg:hidden\">\n              <motion.div\n                initial={{ opacity: 0, height: 0, y: -20 }}\n                animate={{ opacity: 1, height: 'auto', y: 0 }}\n                exit={{ opacity: 0, height: 0, y: -20 }}\n                transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                className=\"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\"\n              >\n                <div className=\"space-y-1 px-6 pb-6 pt-6\">\n                  {/* Mobile Search */}\n                  <motion.div \n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 }}\n                    className=\"relative mb-6\"\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                    />\n                  </motion.div>\n\n                  {/* Mobile Navigation */}\n                  <div className=\"space-y-3\">\n                    {navigation.map((item, index) => (\n                      <motion.div\n                        key={item.name}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: 0.1 * (index + 2) }}\n                      >\n                        <Disclosure.Button\n                          as={Link}\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'\n                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',\n                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'\n                          )}\n                        >\n                          <item.icon className={classNames(\n                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',\n                            'w-6 h-6'\n                          )} />\n                          <span className=\"font-semibold text-lg\">{item.name}</span>\n                        </Disclosure.Button>\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  {/* Mobile Auth Buttons */}\n                  {!isAuthenticated && (\n                    <motion.div \n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 }}\n                      className=\"flex space-x-4 pt-6\"\n                    >\n                      <Link to=\"/login\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\"\n                        >\n                          Sign In\n                        </Disclosure.Button>\n                      </Link>\n                      <Link to=\"/register\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\"\n                        >\n                          Sign Up\n                        </Disclosure.Button>\n                      </Link>\n                    </motion.div>\n                  )}\n                </div>\n              </motion.div>\n            </Disclosure.Panel>\n          </>\n        )}\n      </Disclosure>\n\n      {/* Spacer */}\n      <div className=\"h-20\"></div>\n    </>\n  );\n};\n\nexport default ModernNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,IAAI,EAAEC,UAAU,QAAQ,mBAAmB;AAChE,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,yBAAyB,QACpB,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAvB,QAAA,IAAAwB,SAAA;AAElD,SAASC,UAAUA,CAAC,GAAGC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMsC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIR,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MACtBC,MAAM,CAACR,QAAQ,CAACS,IAAI,GAAG,oBAAoBC,kBAAkB,CAACZ,WAAW,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE;IACrF;EACF,CAAC;EAED5C,SAAS,CAAC,MAAM;IACd,MAAMgD,YAAY,GAAGA,CAAA,KAAM;MACzBd,aAAa,CAACW,MAAM,CAACI,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDJ,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMH,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,UAAU,GAAG,CACjB;IACEC,IAAI,EAAE,MAAM;IACZP,IAAI,EAAE,GAAG;IACTQ,IAAI,EAAExC,QAAQ;IACdyC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBP,IAAI,EAAE,WAAW;IACjBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,CACV;MAAEJ,IAAI,EAAE,aAAa;MAAEP,IAAI,EAAE,gCAAgC;MAAEQ,IAAI,EAAE;IAAK,CAAC,EAC3E;MAAED,IAAI,EAAE,UAAU;MAAEP,IAAI,EAAE,6BAA6B;MAAEQ,IAAI,EAAE;IAAK,CAAC,EACrE;MAAED,IAAI,EAAE,QAAQ;MAAEP,IAAI,EAAE,2BAA2B;MAAEQ,IAAI,EAAE;IAAK,CAAC;EAErE,CAAC,EACD;IACED,IAAI,EAAE,SAAS;IACfP,IAAI,EAAE,mBAAmB;IACzBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,mBAAmB;IAChCG,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBP,IAAI,EAAE,YAAY;IAClBQ,IAAI,EAAEvC,OAAO;IACbwC,WAAW,EAAE,4BAA4B;IACzCG,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,OAAO;IACbP,IAAI,EAAE,QAAQ;IACdQ,IAAI,EAAErC,qBAAqB;IAC3BsC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfP,IAAI,EAAE,UAAU;IAChBQ,IAAI,EAAEtC,SAAS;IACfuC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMI,cAAc,GAAG,CACrB;IAAEN,IAAI,EAAE,cAAc;IAAEP,IAAI,EAAE,UAAU;IAAEQ,IAAI,EAAE1C;EAAS,CAAC,EAC1D;IAAEyC,IAAI,EAAE,eAAe;IAAEP,IAAI,EAAE,SAAS;IAAEQ,IAAI,EAAE5C;EAAgB,CAAC,EACjE;IAAE2C,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEzC;EAAU,CAAC,EACxD;IAAEwC,IAAI,EAAE,UAAU;IAAEP,IAAI,EAAE,WAAW;IAAEQ,IAAI,EAAEnC;EAAc,CAAC,CAC7D;EAED,MAAMyC,QAAQ,GAAIC,IAAI,IAAKxB,QAAQ,CAACyB,QAAQ,KAAKD,IAAI;EAErD,oBACErC,OAAA,CAAAC,SAAA;IAAAsC,QAAA,gBACEvC,OAAA,CAACnB,UAAU;MAAC2D,EAAE,EAAC,KAAK;MAACC,SAAS,EAAE,gFAC9BhC,UAAU,GACN,qCAAqC,GACrC,kBAAkB,EACrB;MACHiC,KAAK,EAAE;QACLC,eAAe,EAAElC,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;QACvEmC,iBAAiB,EAAEnC,UAAU,GAAG,uBAAuB,GAAG,aAAa;QACvEoC,SAAS,EAAEpC,UAAU,GAAG,kBAAkB,GAAG;MAC/C,CAAE;MAAA8B,QAAA,EACCA,CAAC;QAAEO;MAAK,CAAC,kBACR9C,OAAA,CAAAC,SAAA;QAAAsC,QAAA,gBACEvC,OAAA;UAAKyC,SAAS,EAAC,wCAAwC;UAAAF,QAAA,eACrDvC,OAAA;YAAKyC,SAAS,EAAC,wCAAwC;YAAAF,QAAA,gBAErDvC,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAF,QAAA,eAChCvC,OAAA,CAACtB,IAAI;gBAACqE,EAAE,EAAC,GAAG;gBAACN,SAAS,EAAC,mCAAmC;gBAAAF,QAAA,gBACxDvC,OAAA,CAACpB,MAAM,CAACoE,GAAG;kBACTC,UAAU,EAAE;oBAAEC,MAAM,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBACxCC,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE;kBAAI,CAAE;kBAC9Dd,SAAS,EAAC,2MAA2M;kBAAAF,QAAA,gBAErNvC,OAAA,CAACd,eAAe;oBAACuD,SAAS,EAAC;kBAAoB;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClD3D,OAAA;oBAAKyC,SAAS,EAAC;kBAA6E;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACb3D,OAAA;kBAAKyC,SAAS,EAAC,eAAe;kBAAAF,QAAA,gBAC5BvC,OAAA;oBAAMyC,SAAS,EAAC,iEAAiE;oBAC3EC,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAsB,CAAE;oBAAArB,QAAA,EAAC;kBAE/C;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP3D,OAAA;oBAAMyC,SAAS,EAAC,kEAAkE;oBAC5EC,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAwB,CAAE;oBAAArB,QAAA,EAAC;kBAEjD;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGN3D,OAAA;cAAKyC,SAAS,EAAC,iBAAiB;cAAAF,QAAA,eAC9BvC,OAAA;gBAAKyC,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,EACzCX,UAAU,CAACiC,GAAG,CAAEC,IAAI;kBAAA,IAAAC,gBAAA;kBAAA,oBACnB/D,OAAA,CAACpB,MAAM,CAACoE,GAAG;oBAETC,UAAU,EAAE;sBAAEe,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACtBZ,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAC9BZ,SAAS,EAAC,gBAAgB;oBAAAF,QAAA,gBAE1BvC,OAAA,CAACtB,IAAI;sBACHqE,EAAE,EAAEe,IAAI,CAACxC,IAAK;sBACdmB,SAAS,EAAEvC,UAAU,CACnBkC,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,GACf,sBAAsB,GACtB,iBAAiB,EACrB,8HACF,CAAE;sBACFoB,KAAK,EAAE;wBACLC,eAAe,EAAEP,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,GAAG,uBAAuB,GAAG,aAAa;wBAC9EsC,KAAK,EAAExB,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,GAAG,OAAO,GAAG,qBAAqB;wBAC5D2C,WAAW,EAAE;sBACf,CAAE;sBAAA1B,QAAA,gBAEFvC,OAAA,CAAC8D,IAAI,CAAChC,IAAI;wBAACW,SAAS,EAAC;sBAAS;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjC3D,OAAA;wBAAMyC,SAAS,EAAC,eAAe;wBAAAF,QAAA,EAAEuB,IAAI,CAACjC;sBAAI;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACjDG,IAAI,CAAC5B,KAAK,iBACTlC,OAAA;wBAAMyC,SAAS,EAAC,uEAAuE;wBAAAF,QAAA,EACpFuB,IAAI,CAAC5B;sBAAK;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CACP,EACA,CAACvB,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,iBACnBtB,OAAA;wBAAKyC,SAAS,EAAC;sBAAyJ;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC/K;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC,EAGNG,IAAI,CAAC9B,QAAQ,iBACZhC,OAAA;sBAAKyC,SAAS,EAAC,qIAAqI;sBAAAF,QAAA,eAClJvC,OAAA;wBAAKyC,SAAS,EAAC,6EAA6E;wBACvFC,KAAK,EAAE;0BACLC,eAAe,EAAE,mBAAmB;0BACpCsB,WAAW,EAAE;wBACf,CAAE;wBAAA1B,QAAA,gBACLvC,OAAA;0BAAKyC,SAAS,EAAC,MAAM;0BAAAF,QAAA,gBACnBvC,OAAA;4BAAIyC,SAAS,EAAC,oCAAoC;4BAC9CC,KAAK,EAAE;8BAAEkB,KAAK,EAAE;4BAAsB,CAAE;4BAAArB,QAAA,EAAC;0BAE7C;4BAAAiB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL3D,OAAA;4BAAGyC,SAAS,EAAC,0BAA0B;4BACpCC,KAAK,EAAE;8BAAEkB,KAAK,EAAE;4BAAwB,CAAE;4BAAArB,QAAA,EAAC;0BAE9C;4BAAAiB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACN3D,OAAA;0BAAKyC,SAAS,EAAC,wBAAwB;0BAAAF,QAAA,GAAAwB,gBAAA,GACpCD,IAAI,CAAC7B,UAAU,cAAA8B,gBAAA,uBAAfA,gBAAA,CAAiBF,GAAG,CAAEK,QAAQ,iBAC7BlE,OAAA,CAACtB,IAAI;4BAEHqE,EAAE,EAAEmB,QAAQ,CAAC5C,IAAK;4BAClBmB,SAAS,EAAC,wGAAwG;4BAClHC,KAAK,EAAE;8BAAEC,eAAe,EAAE;4BAAc,CAAE;4BAAAJ,QAAA,gBAE1CvC,OAAA;8BAAMyC,SAAS,EAAC,UAAU;8BAAAF,QAAA,EAAE2B,QAAQ,CAACpC;4BAAI;8BAAA0B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eACjD3D,OAAA;8BAAAuC,QAAA,gBACEvC,OAAA;gCAAKyC,SAAS,EAAC,8BAA8B;gCACxCC,KAAK,EAAE;kCAAEkB,KAAK,EAAE;gCAAsB,CAAE;gCAAArB,QAAA,EAC1C2B,QAAQ,CAACrC;8BAAI;gCAAA2B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACX,CAAC,eACN3D,OAAA;gCAAKyC,SAAS,EAAC,0BAA0B;gCACpCC,KAAK,EAAE;kCAAEkB,KAAK,EAAE;gCAAwB,CAAE;gCAAArB,QAAA,GAAC,SACvC,EAAC2B,QAAQ,CAACrC,IAAI,CAACsC,WAAW,CAAC,CAAC;8BAAA;gCAAAX,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA,GAfDO,QAAQ,CAACrC,IAAI;4BAAA2B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAgBd,CACP;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA,GAzEIG,IAAI,CAACjC,IAAI;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0EJ,CAAC;gBAAA,CACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3D,OAAA;cAAKyC,SAAS,EAAC,mDAAmD;cAAAF,QAAA,eAChEvC,OAAA,CAACpB,MAAM,CAACoE,GAAG;gBACTP,SAAS,EAAC,uBAAuB;gBACjCQ,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAd,QAAA,gBAE9BvC,OAAA;kBAAKyC,SAAS,EAAC,2EAA2E;kBAAAF,QAAA,eACxFvC,OAAA,CAACb,mBAAmB;oBAACsD,SAAS,EAAC,wCAAwC;oBAClDC,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAwB;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eACN3D,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACXc,WAAW,EAAC,wCAAwC;kBACpDC,KAAK,EAAE1D,WAAY;kBACnB2D,QAAQ,EAAGpD,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGtD,CAAC,IAAKA,CAAC,CAACuD,GAAG,KAAK,OAAO,IAAIxD,YAAY,CAACC,CAAC,CAAE;kBACvDuB,SAAS,EAAC,wIAAwI;kBAClJC,KAAK,EAAE;oBACLC,eAAe,EAAE,qBAAqB;oBACtCsB,WAAW,EAAE,uBAAuB;oBACpCL,KAAK,EAAE;kBACT;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF3D,OAAA;kBAAKyC,SAAS,EAAC,mDAAmD;kBAAAF,QAAA,eAChEvC,OAAA;oBAAKyC,SAAS,EAAC,6BAA6B;oBAAAF,QAAA,eAC1CvC,OAAA;sBAAKyC,SAAS,EAAC,kGAAkG;sBAC5GC,KAAK,EAAE;wBACLC,eAAe,EAAE,oBAAoB;wBACrCsB,WAAW,EAAE,yBAAyB;wBACtCL,KAAK,EAAE;sBACT,CAAE;sBAAArB,QAAA,EAAC;oBAER;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLhD,WAAW,iBACVX,OAAA;kBAAKyC,SAAS,EAAC,2FAA2F;kBACrGC,KAAK,EAAE;oBACLC,eAAe,EAAE,mBAAmB;oBACpCsB,WAAW,EAAE;kBACf,CAAE;kBAAA1B,QAAA,eACLvC,OAAA;oBAAKyC,SAAS,EAAC,KAAK;oBAAAF,QAAA,gBAClBvC,OAAA;sBAAKyC,SAAS,EAAC,2CAA2C;sBACrDC,KAAK,EAAE;wBAAEkB,KAAK,EAAE;sBAAwB,CAAE;sBAAArB,QAAA,EAAC;oBAEhD;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN3D,OAAA;sBAAKyC,SAAS,EAAC,WAAW;sBAAAF,QAAA,EACvB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAACsB,GAAG,CAAEa,UAAU,iBACxE1E,OAAA;wBAEEyC,SAAS,EAAC,mGAAmG;wBAC7GC,KAAK,EAAE;0BAAEkB,KAAK,EAAE;wBAAsB,CAAE;wBAAArB,QAAA,gBAExCvC,OAAA,CAACb,mBAAmB;0BAACsD,SAAS,EAAC;wBAAqB;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACtDe,UAAU;sBAAA,GALNA,UAAU;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAMT,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGN3D,OAAA;cAAKyC,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBAE1CvC,OAAA,CAACtB,IAAI;gBAACqE,EAAE,EAAC,WAAW;gBAAAR,QAAA,eAClBvC,OAAA,CAACpB,MAAM,CAAC+F,MAAM;kBACZ1B,UAAU,EAAE;oBAAEE,KAAK,EAAE,GAAG;oBAAEa,CAAC,EAAE,CAAC;kBAAE,CAAE;kBAClCY,QAAQ,EAAE;oBAAEzB,KAAK,EAAE;kBAAK,CAAE;kBAC1BV,SAAS,EAAE,6DACThC,UAAU,GACN,oFAAoF,GACpF,qHAAqH,EACxH;kBAAA8B,QAAA,eAEHvC,OAAA,CAACX,SAAS;oBAACoD,SAAS,EAAC;kBAAS;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAGP3D,OAAA;gBAAKyC,SAAS,EAAC,UAAU;gBAAAF,QAAA,eACvBvC,OAAA,CAACH,YAAY;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EAGL5C,eAAe,gBACdf,OAAA,CAAClB,IAAI;gBAAC0D,EAAE,EAAC,KAAK;gBAACC,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBACtCvC,OAAA;kBAAAuC,QAAA,eACEvC,OAAA,CAAClB,IAAI,CAAC+F,MAAM;oBAACpC,SAAS,EAAE,+FACtBhC,UAAU,GACN,oFAAoF,GACpF,qHAAqH,EACxH;oBAAA8B,QAAA,gBACDvC,OAAA;sBAAMyC,SAAS,EAAC,SAAS;sBAAAF,QAAA,EAAC;oBAAc;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC9C7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgE,cAAc,gBACnB9E,OAAA;sBACEyC,SAAS,EAAC,2CAA2C;sBACrDsC,GAAG,EAAEjE,IAAI,CAACgE,cAAe;sBACzBE,GAAG,EAAC;oBAAE;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,gBAEF3D,OAAA;sBAAKyC,SAAS,EAAC,mHAAmH;sBAAAF,QAAA,eAChIvC,OAAA,CAACZ,QAAQ;wBAACqD,SAAS,EAAC;sBAAoB;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACN,eACD3D,OAAA;sBAAMyC,SAAS,EAAC,qCAAqC;sBAAAF,QAAA,EAClD,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,SAAS,KAAI;oBAAS;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACP3D,OAAA,CAACN,eAAe;sBAAC+C,SAAS,EAAC;oBAAS;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACN3D,OAAA,CAACjB,UAAU;kBACTyD,EAAE,EAAE/D,QAAS;kBACbyG,KAAK,EAAC,kCAAkC;kBACxCC,SAAS,EAAC,8BAA8B;kBACxCC,OAAO,EAAC,iCAAiC;kBACzCC,KAAK,EAAC,gCAAgC;kBACtCC,SAAS,EAAC,iCAAiC;kBAC3CC,OAAO,EAAC,8BAA8B;kBAAAhD,QAAA,eAEtCvC,OAAA,CAAClB,IAAI,CAAC0G,KAAK;oBAAC/C,SAAS,EAAC,2JAA2J;oBAAAF,QAAA,gBAE/KvC,OAAA;sBAAKyC,SAAS,EAAC,iFAAiF;sBAAAF,QAAA,eAC9FvC,OAAA;wBAAKyC,SAAS,EAAC,6BAA6B;wBAAAF,QAAA,GACzCzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgE,cAAc,gBACnB9E,OAAA;0BACEyC,SAAS,EAAC,6CAA6C;0BACvDsC,GAAG,EAAEjE,IAAI,CAACgE,cAAe;0BACzBE,GAAG,EAAC;wBAAE;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,gBAEF3D,OAAA;0BAAKyC,SAAS,EAAC,qEAAqE;0BAAAF,QAAA,eAClFvC,OAAA,CAACZ,QAAQ;4BAACqD,SAAS,EAAC;0BAAoB;4BAAAe,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CACN,eACD3D,OAAA;0BAAAuC,QAAA,gBACEvC,OAAA;4BAAGyC,SAAS,EAAC,0BAA0B;4BAAAF,QAAA,GACpCzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,SAAS,EAAC,GAAC,EAACnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,QAAQ;0BAAA;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC,CAAC,eACJ3D,OAAA;4BAAGyC,SAAS,EAAC,uBAAuB;4BAAAF,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E;0BAAK;4BAAAlC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN3D,OAAA;sBAAKyC,SAAS,EAAC,MAAM;sBAAAF,QAAA,GAClBJ,cAAc,CAAC0B,GAAG,CAAEC,IAAI,iBACvB9D,OAAA,CAAClB,IAAI,CAAC6G,IAAI;wBAAApD,QAAA,EACPA,CAAC;0BAAEqD;wBAAO,CAAC,kBACV5F,OAAA,CAACtB,IAAI;0BACHqE,EAAE,EAAEe,IAAI,CAACxC,IAAK;0BACdmB,SAAS,EAAEvC,UAAU,CACnB0F,MAAM,GAAG,0CAA0C,GAAG,eAAe,EACrE,8EACF,CAAE;0BAAArD,QAAA,gBAEFvC,OAAA,CAAC8D,IAAI,CAAChC,IAAI;4BAACW,SAAS,EAAC;0BAAS;4BAAAe,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjC3D,OAAA;4BAAAuC,QAAA,EAAOuB,IAAI,CAACjC;0BAAI;4BAAA2B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBACP,GAZaG,IAAI,CAACjC,IAAI;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAad,CACZ,CAAC,eACF3D,OAAA;wBAAKyC,SAAS,EAAC,oCAAoC;wBAAAF,QAAA,eACjDvC,OAAA,CAAClB,IAAI,CAAC6G,IAAI;0BAAApD,QAAA,EACPA,CAAC;4BAAEqD;0BAAO,CAAC,kBACV5F,OAAA;4BACE6F,OAAO,EAAE7E,MAAO;4BAChByB,SAAS,EAAEvC,UAAU,CACnB0F,MAAM,GAAG,wBAAwB,GAAG,cAAc,EAClD,qFACF,CAAE;4BAAArD,QAAA,gBAEFvC,OAAA,CAACJ,yBAAyB;8BAAC6C,SAAS,EAAC;4BAAS;8BAAAe,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACjD3D,OAAA;8BAAAuC,QAAA,EAAM;4BAAQ;8BAAAiB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBACT;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,gBAEP3D,OAAA;gBAAKyC,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1CvC,OAAA,CAACtB,IAAI;kBAACqE,EAAE,EAAC,QAAQ;kBAAAR,QAAA,eACfvC,OAAA,CAACpB,MAAM,CAAC+F,MAAM;oBACZ1B,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEa,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCY,QAAQ,EAAE;sBAAEzB,KAAK,EAAE;oBAAK,CAAE;oBAC1BV,SAAS,EAAE,4EACThC,UAAU,GACN,yHAAyH,GACzH,mIAAmI,EACtI;oBAAA8B,QAAA,EACJ;kBAED;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACP3D,OAAA,CAACtB,IAAI;kBAACqE,EAAE,EAAC,WAAW;kBAAAR,QAAA,eAClBvC,OAAA,CAACpB,MAAM,CAAC+F,MAAM;oBACZ1B,UAAU,EAAE;sBAAEE,KAAK,EAAE,IAAI;sBAAEa,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCY,QAAQ,EAAE;sBAAEzB,KAAK,EAAE;oBAAK,CAAE;oBAC1BV,SAAS,EAAC,gOAAgO;oBAAAF,QAAA,EAC3O;kBAED;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,eAGD3D,OAAA;gBAAKyC,SAAS,EAAC,WAAW;gBAAAF,QAAA,eACxBvC,OAAA,CAACnB,UAAU,CAACgG,MAAM;kBAACpC,SAAS,EAAE,+FAC5BhC,UAAU,GACN,oEAAoE,GACpE,qGAAqG,+EAC3B;kBAAA8B,QAAA,gBAC9EvC,OAAA;oBAAMyC,SAAS,EAAC,SAAS;oBAAAF,QAAA,EAAC;kBAAc;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C3D,OAAA,CAACpB,MAAM,CAACoE,GAAG;oBACT8C,OAAO,EAAE;sBAAE5C,MAAM,EAAEJ,IAAI,GAAG,GAAG,GAAG;oBAAE,CAAE;oBACpCM,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAd,QAAA,EAE7BO,IAAI,gBACH9C,OAAA,CAACf,SAAS;sBAACwD,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE1D3D,OAAA,CAAChB,SAAS;sBAACyD,SAAS,EAAC,eAAe;sBAAC,eAAY;oBAAM;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA,CAACnB,UAAU,CAACkH,KAAK;UAACtD,SAAS,EAAC,WAAW;UAAAF,QAAA,eACrCvC,OAAA,CAACpB,MAAM,CAACoE,GAAG;YACTgD,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,CAAC;cAAElC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC3C8B,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,MAAM;cAAElC,CAAC,EAAE;YAAE,CAAE;YAC9CmC,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,MAAM,EAAE,CAAC;cAAElC,CAAC,EAAE,CAAC;YAAG,CAAE;YACxCZ,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAE+C,IAAI,EAAE;YAAY,CAAE;YACjD3D,SAAS,EAAC,kEAAkE;YAAAF,QAAA,eAE5EvC,OAAA;cAAKyC,SAAS,EAAC,0BAA0B;cAAAF,QAAA,gBAEvCvC,OAAA,CAACpB,MAAM,CAACoE,GAAG;gBACTgD,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCP,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEI,CAAC,EAAE;gBAAE,CAAE;gBAC9BjD,UAAU,EAAE;kBAAEkD,KAAK,EAAE;gBAAI,CAAE;gBAC3B7D,SAAS,EAAC,eAAe;gBAAAF,QAAA,gBAEzBvC,OAAA;kBAAKyC,SAAS,EAAC,sEAAsE;kBAAAF,QAAA,eACnFvC,OAAA,CAACb,mBAAmB;oBAACsD,SAAS,EAAC;kBAAuB;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN3D,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACXc,WAAW,EAAC,wBAAwB;kBACpCC,KAAK,EAAE1D,WAAY;kBACnB2D,QAAQ,EAAGpD,CAAC,IAAKN,cAAc,CAACM,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE;kBAChDG,SAAS,EAAGtD,CAAC,IAAKA,CAAC,CAACuD,GAAG,KAAK,OAAO,IAAIxD,YAAY,CAACC,CAAC,CAAE;kBACvDuB,SAAS,EAAC;gBAAgP;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGb3D,OAAA;gBAAKyC,SAAS,EAAC,WAAW;gBAAAF,QAAA,EACvBX,UAAU,CAACiC,GAAG,CAAC,CAACC,IAAI,EAAEyC,KAAK,kBAC1BvG,OAAA,CAACpB,MAAM,CAACoE,GAAG;kBAETgD,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCP,OAAO,EAAE;oBAAEG,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAE,CAAE;kBAC9BjD,UAAU,EAAE;oBAAEkD,KAAK,EAAE,GAAG,IAAIC,KAAK,GAAG,CAAC;kBAAE,CAAE;kBAAAhE,QAAA,eAEzCvC,OAAA,CAACnB,UAAU,CAACgG,MAAM;oBAChBrC,EAAE,EAAE9D,IAAK;oBACTqE,EAAE,EAAEe,IAAI,CAACxC,IAAK;oBACdmB,SAAS,EAAEvC,UAAU,CACnBkC,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,GACf,iFAAiF,GACjF,oEAAoE,EACxE,qFACF,CAAE;oBAAAiB,QAAA,gBAEFvC,OAAA,CAAC8D,IAAI,CAAChC,IAAI;sBAACW,SAAS,EAAEvC,UAAU,CAC9BkC,QAAQ,CAAC0B,IAAI,CAACxC,IAAI,CAAC,GAAG,YAAY,GAAG,iDAAiD,EACtF,SACF;oBAAE;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACL3D,OAAA;sBAAMyC,SAAS,EAAC,uBAAuB;sBAAAF,QAAA,EAAEuB,IAAI,CAACjC;oBAAI;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC,GApBfG,IAAI,CAACjC,IAAI;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBJ,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL,CAAC5C,eAAe,iBACff,OAAA,CAACpB,MAAM,CAACoE,GAAG;gBACTgD,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEjC,CAAC,EAAE;gBAAG,CAAE;gBAC/B8B,OAAO,EAAE;kBAAEG,OAAO,EAAE,CAAC;kBAAEjC,CAAC,EAAE;gBAAE,CAAE;gBAC9BZ,UAAU,EAAE;kBAAEkD,KAAK,EAAE;gBAAI,CAAE;gBAC3B7D,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,gBAE/BvC,OAAA,CAACtB,IAAI;kBAACqE,EAAE,EAAC,QAAQ;kBAACN,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eAClCvC,OAAA,CAACnB,UAAU,CAACgG,MAAM;oBAChBrC,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,wJAAwJ;oBAAAF,QAAA,EACnK;kBAED;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACP3D,OAAA,CAACtB,IAAI;kBAACqE,EAAE,EAAC,WAAW;kBAACN,SAAS,EAAC,QAAQ;kBAAAF,QAAA,eACrCvC,OAAA,CAACnB,UAAU,CAACgG,MAAM;oBAChBrC,EAAE,EAAC,QAAQ;oBACXC,SAAS,EAAC,8MAA8M;oBAAAF,QAAA,EACzN;kBAED;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAmB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA,eACnB;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAGb3D,OAAA;MAAKyC,SAAS,EAAC;IAAM;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eAC5B,CAAC;AAEP,CAAC;AAACnD,EAAA,CAxhBID,gBAAgB;EAAA,QAGH5B,WAAW,EACcmB,OAAO;AAAA;AAAA0G,EAAA,GAJ7CjG,gBAAgB;AA0hBtB,eAAeA,gBAAgB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}