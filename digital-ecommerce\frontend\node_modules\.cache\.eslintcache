[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "33", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js": "34", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js": "35", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js": "36", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js": "37", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js": "38", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js": "39", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js": "40", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js": "41", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js": "42", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "43", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js": "44", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ThemeContext.js": "45", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ThemeToggle.js": "46"}, {"size": 584, "mtime": 1750249986668, "results": "47", "hashOfConfig": "48"}, {"size": 5003, "mtime": 1750108593800, "results": "49", "hashOfConfig": "48"}, {"size": 12230, "mtime": 1750326556169, "results": "50", "hashOfConfig": "48"}, {"size": 21958, "mtime": 1750326707665, "results": "51", "hashOfConfig": "48"}, {"size": 9872, "mtime": 1750164540216, "results": "52", "hashOfConfig": "48"}, {"size": 1876, "mtime": 1750247300183, "results": "53", "hashOfConfig": "48"}, {"size": 994, "mtime": 1750166931473, "results": "54", "hashOfConfig": "48"}, {"size": 7010, "mtime": 1750159469458, "results": "55", "hashOfConfig": "48"}, {"size": 8712, "mtime": 1750159406548, "results": "56", "hashOfConfig": "48"}, {"size": 11692, "mtime": 1750279696806, "results": "57", "hashOfConfig": "48"}, {"size": 7547, "mtime": 1750166070393, "results": "58", "hashOfConfig": "48"}, {"size": 4196, "mtime": 1750250970753, "results": "59", "hashOfConfig": "48"}, {"size": 11440, "mtime": 1750164163184, "results": "60", "hashOfConfig": "48"}, {"size": 23909, "mtime": 1750270542017, "results": "61", "hashOfConfig": "48"}, {"size": 12667, "mtime": 1750164007467, "results": "62", "hashOfConfig": "48"}, {"size": 19371, "mtime": 1750271571376, "results": "63", "hashOfConfig": "48"}, {"size": 12456, "mtime": 1750164967846, "results": "64", "hashOfConfig": "48"}, {"size": 9102, "mtime": 1750166125481, "results": "65", "hashOfConfig": "48"}, {"size": 15294, "mtime": 1750164732603, "results": "66", "hashOfConfig": "48"}, {"size": 7212, "mtime": 1750166452419, "results": "67", "hashOfConfig": "48"}, {"size": 12346, "mtime": 1750166411028, "results": "68", "hashOfConfig": "48"}, {"size": 13681, "mtime": 1750166786153, "results": "69", "hashOfConfig": "48"}, {"size": 12020, "mtime": 1750278446850, "results": "70", "hashOfConfig": "48"}, {"size": 3218, "mtime": 1750164772685, "results": "71", "hashOfConfig": "48"}, {"size": 9048, "mtime": 1750166894082, "results": "72", "hashOfConfig": "48"}, {"size": 9766, "mtime": 1750278585130, "results": "73", "hashOfConfig": "48"}, {"size": 8128, "mtime": 1750271460156, "results": "74", "hashOfConfig": "48"}, {"size": 16161, "mtime": 1750278064733, "results": "75", "hashOfConfig": "48"}, {"size": 2903, "mtime": 1750164073874, "results": "76", "hashOfConfig": "48"}, {"size": 1653, "mtime": 1750164096843, "results": "77", "hashOfConfig": "48"}, {"size": 34651, "mtime": 1750280202734, "results": "78", "hashOfConfig": "48"}, {"size": 7514, "mtime": 1750278548880, "results": "79", "hashOfConfig": "48"}, {"size": 58961, "mtime": 1750279507327, "results": "80", "hashOfConfig": "48"}, {"size": 3898, "mtime": 1750277956931, "results": "81", "hashOfConfig": "48"}, {"size": 3308, "mtime": 1750277932035, "results": "82", "hashOfConfig": "48"}, {"size": 34787, "mtime": 1750325648047, "results": "83", "hashOfConfig": "48"}, {"size": 16034, "mtime": 1750325557965, "results": "84", "hashOfConfig": "48"}, {"size": 13947, "mtime": 1750325586950, "results": "85", "hashOfConfig": "48"}, {"size": 12877, "mtime": 1750281306725, "results": "86", "hashOfConfig": "48"}, {"size": 14027, "mtime": 1750325997968, "results": "87", "hashOfConfig": "48"}, {"size": 15013, "mtime": 1750325501977, "results": "88", "hashOfConfig": "48"}, {"size": 16608, "mtime": 1750282102009, "results": "89", "hashOfConfig": "48"}, {"size": 13106, "mtime": 1750159208689, "results": "90", "hashOfConfig": "48"}, {"size": 12649, "mtime": 1750282260855, "results": "91", "hashOfConfig": "48"}, {"size": 10237, "mtime": 1750326393901, "results": "92", "hashOfConfig": "48"}, {"size": 10298, "mtime": 1750326317369, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js", ["232", "233"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["234"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["235"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js", ["236", "237", "238", "239"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["240"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js", ["241"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js", ["242"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js", ["243", "244", "245", "246"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js", ["247"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js", ["248", "249", "250"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js", ["251"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js", ["252"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js", ["253", "254"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js", ["255"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js", ["256"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ThemeContext.js", ["257", "258"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ThemeToggle.js", ["259", "260"], [], {"ruleId": "261", "severity": 1, "message": "262", "line": 18, "column": 24, "nodeType": "263", "messageId": "264", "endLine": 18, "endColumn": 39}, {"ruleId": "261", "severity": 1, "message": "265", "line": 19, "column": 19, "nodeType": "263", "messageId": "264", "endLine": 19, "endColumn": 29}, {"ruleId": "261", "severity": 1, "message": "266", "line": 8, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 8, "endColumn": 17}, {"ruleId": "261", "severity": 1, "message": "267", "line": 5, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 5, "endColumn": 10}, {"ruleId": "261", "severity": 1, "message": "268", "line": 14, "column": 8, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 25}, {"ruleId": "261", "severity": 1, "message": "269", "line": 22, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 22, "endColumn": 27}, {"ruleId": "261", "severity": 1, "message": "270", "line": 24, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 24, "endColumn": 20}, {"ruleId": "261", "severity": 1, "message": "271", "line": 71, "column": 9, "nodeType": "263", "messageId": "264", "endLine": 71, "endColumn": 30}, {"ruleId": "261", "severity": 1, "message": "272", "line": 18, "column": 37, "nodeType": "263", "messageId": "264", "endLine": 18, "endColumn": 49}, {"ruleId": "273", "severity": 1, "message": "274", "line": 56, "column": 6, "nodeType": "275", "endLine": 56, "endColumn": 8, "suggestions": "276"}, {"ruleId": "261", "severity": 1, "message": "277", "line": 74, "column": 9, "nodeType": "263", "messageId": "264", "endLine": 74, "endColumn": 24}, {"ruleId": "261", "severity": 1, "message": "278", "line": 5, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 5, "endColumn": 12}, {"ruleId": "273", "severity": 1, "message": "279", "line": 54, "column": 6, "nodeType": "275", "endLine": 54, "endColumn": 21, "suggestions": "280"}, {"ruleId": "281", "severity": 1, "message": "282", "line": 67, "column": 5, "nodeType": "283", "messageId": "284", "endLine": 94, "endColumn": 6}, {"ruleId": "261", "severity": 1, "message": "285", "line": 159, "column": 9, "nodeType": "263", "messageId": "264", "endLine": 159, "endColumn": 18}, {"ruleId": "273", "severity": 1, "message": "286", "line": 94, "column": 6, "nodeType": "275", "endLine": 94, "endColumn": 8, "suggestions": "287"}, {"ruleId": "261", "severity": 1, "message": "288", "line": 30, "column": 33, "nodeType": "263", "messageId": "264", "endLine": 30, "endColumn": 57}, {"ruleId": "261", "severity": 1, "message": "289", "line": 31, "column": 22, "nodeType": "263", "messageId": "264", "endLine": 31, "endColumn": 35}, {"ruleId": "261", "severity": 1, "message": "290", "line": 32, "column": 18, "nodeType": "263", "messageId": "264", "endLine": 32, "endColumn": 27}, {"ruleId": "261", "severity": 1, "message": "291", "line": 21, "column": 52, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 72}, {"ruleId": "261", "severity": 1, "message": "292", "line": 3, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 3, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "292", "line": 3, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 3, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "293", "line": 8, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 8, "endColumn": 11}, {"ruleId": "261", "severity": 1, "message": "294", "line": 8, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 8, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "295", "line": 7, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 7, "endColumn": 11}, {"ruleId": "273", "severity": 1, "message": "296", "line": 88, "column": 6, "nodeType": "275", "endLine": 88, "endColumn": 25, "suggestions": "297"}, {"ruleId": "273", "severity": 1, "message": "298", "line": 159, "column": 6, "nodeType": "275", "endLine": 159, "endColumn": 65, "suggestions": "299"}, {"ruleId": "261", "severity": 1, "message": "300", "line": 1, "column": 27, "nodeType": "263", "messageId": "264", "endLine": 1, "endColumn": 36}, {"ruleId": "301", "severity": 1, "message": "302", "line": 174, "column": 7, "nodeType": "303", "endLine": 214, "endColumn": 8}, "no-unused-vars", "'setPcCategories' is assigned a value but never used.", "Identifier", "unusedVar", "'setBundles' is assigned a value but never used.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'ConfirmationModal' is defined but never used.", "'showDeleteConfirm' is assigned a value but never used.", "'isDeleting' is assigned a value but never used.", "'confirmDeleteCategory' is assigned a value but never used.", "'isInWishlist' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'categories.length' and 'products'. Either include them or remove the dependency array.", "ArrayExpression", ["304"], "'handleSelectAll' is assigned a value but never used.", "'PhotoIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData.sku'. Either include it or remove the dependency array.", ["305"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'moveImage' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["306"], "'setSelectedComponentType' is assigned a value but never used.", "'setPriceRange' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'calculateBundlePrice' is assigned a value but never used.", "'motion' is defined but never used.", "'StarIcon' is defined but never used.", "'ArrowPathIcon' is defined but never used.", "'UserIcon' is defined but never used.", "React Hook useCallback has a missing dependency: 'announceThemeChange'. Either include it or remove the dependency array.", ["307"], "React Hook useCallback has a missing dependency: 'triggerGamingFeedback'. Either include it or remove the dependency array.", ["308"], "'useEffect' is defined but never used.", "jsx-a11y/role-supports-aria-props", "The attribute aria-pressed is not supported by the role switch.", "JSXOpeningElement", {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, {"desc": "317", "fix": "318"}, "Update the dependencies array to be: [categories.length, products]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [formData.name, formData.sku]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [removeToast]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [announceThemeChange, getEffectiveTheme]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [getEffectiveTheme, theme, saveThemePreference, applyTheme, triggerGamingFeedback]", {"range": "327", "text": "328"}, [1753, 1755], "[categories.length, products]", [1446, 1461], "[formData.name, formData.sku]", [2610, 2612], "[removeToast]", [2974, 2993], "[announceThemeChange, getEffectiveTheme]", [5271, 5330], "[getEffectiveTheme, theme, saveThemePreference, applyTheme, triggerGamingFeedback]"]