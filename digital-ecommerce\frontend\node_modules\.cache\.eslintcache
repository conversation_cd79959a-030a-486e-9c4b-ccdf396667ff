[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "33", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js": "34", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js": "35", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js": "36", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js": "37", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js": "38", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js": "39", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js": "40", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js": "41", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js": "42", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "43", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js": "44", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductPreviewModal.js": "45"}, {"size": 584, "mtime": 1750249986668, "results": "46", "hashOfConfig": "47"}, {"size": 5003, "mtime": 1750108593800, "results": "48", "hashOfConfig": "47"}, {"size": 12100, "mtime": 1750327357055, "results": "49", "hashOfConfig": "47"}, {"size": 39038, "mtime": 1750332142256, "results": "50", "hashOfConfig": "47"}, {"size": 9872, "mtime": 1750164540216, "results": "51", "hashOfConfig": "47"}, {"size": 1876, "mtime": 1750247300183, "results": "52", "hashOfConfig": "47"}, {"size": 994, "mtime": 1750166931473, "results": "53", "hashOfConfig": "47"}, {"size": 7010, "mtime": 1750159469458, "results": "54", "hashOfConfig": "47"}, {"size": 8712, "mtime": 1750159406548, "results": "55", "hashOfConfig": "47"}, {"size": 11692, "mtime": 1750279696806, "results": "56", "hashOfConfig": "47"}, {"size": 7547, "mtime": 1750166070393, "results": "57", "hashOfConfig": "47"}, {"size": 4196, "mtime": 1750250970753, "results": "58", "hashOfConfig": "47"}, {"size": 11440, "mtime": 1750164163184, "results": "59", "hashOfConfig": "47"}, {"size": 25326, "mtime": 1750329228558, "results": "60", "hashOfConfig": "47"}, {"size": 12667, "mtime": 1750164007467, "results": "61", "hashOfConfig": "47"}, {"size": 21954, "mtime": 1750331155536, "results": "62", "hashOfConfig": "47"}, {"size": 13884, "mtime": 1750329443807, "results": "63", "hashOfConfig": "47"}, {"size": 9102, "mtime": 1750166125481, "results": "64", "hashOfConfig": "47"}, {"size": 15294, "mtime": 1750164732603, "results": "65", "hashOfConfig": "47"}, {"size": 7212, "mtime": 1750166452419, "results": "66", "hashOfConfig": "47"}, {"size": 12346, "mtime": 1750166411028, "results": "67", "hashOfConfig": "47"}, {"size": 13681, "mtime": 1750166786153, "results": "68", "hashOfConfig": "47"}, {"size": 12020, "mtime": 1750278446850, "results": "69", "hashOfConfig": "47"}, {"size": 3218, "mtime": 1750164772685, "results": "70", "hashOfConfig": "47"}, {"size": 10302, "mtime": 1750330636688, "results": "71", "hashOfConfig": "47"}, {"size": 10111, "mtime": 1750328587945, "results": "72", "hashOfConfig": "47"}, {"size": 8128, "mtime": 1750271460156, "results": "73", "hashOfConfig": "47"}, {"size": 16161, "mtime": 1750278064733, "results": "74", "hashOfConfig": "47"}, {"size": 2903, "mtime": 1750164073874, "results": "75", "hashOfConfig": "47"}, {"size": 1653, "mtime": 1750164096843, "results": "76", "hashOfConfig": "47"}, {"size": 34651, "mtime": 1750280202734, "results": "77", "hashOfConfig": "47"}, {"size": 7778, "mtime": 1750328641019, "results": "78", "hashOfConfig": "47"}, {"size": 58961, "mtime": 1750279507327, "results": "79", "hashOfConfig": "47"}, {"size": 3898, "mtime": 1750277956931, "results": "80", "hashOfConfig": "47"}, {"size": 3308, "mtime": 1750277932035, "results": "81", "hashOfConfig": "47"}, {"size": 34787, "mtime": 1750325648047, "results": "82", "hashOfConfig": "47"}, {"size": 16975, "mtime": 1750328453940, "results": "83", "hashOfConfig": "47"}, {"size": 13947, "mtime": 1750325586950, "results": "84", "hashOfConfig": "47"}, {"size": 12877, "mtime": 1750281306725, "results": "85", "hashOfConfig": "47"}, {"size": 14027, "mtime": 1750325997968, "results": "86", "hashOfConfig": "47"}, {"size": 15013, "mtime": 1750325501977, "results": "87", "hashOfConfig": "47"}, {"size": 16608, "mtime": 1750282102009, "results": "88", "hashOfConfig": "47"}, {"size": 13106, "mtime": 1750159208689, "results": "89", "hashOfConfig": "47"}, {"size": 12649, "mtime": 1750282260855, "results": "90", "hashOfConfig": "47"}, {"size": 18518, "mtime": 1750328061017, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js", ["227", "228"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["229"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["230"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js", ["231", "232", "233", "234"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["235"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js", ["236"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js", ["237"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js", ["238", "239", "240", "241"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js", ["242"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js", ["243", "244", "245"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js", ["246"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js", ["247"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js", ["248", "249"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js", ["250"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js", ["251"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductPreviewModal.js", ["252", "253"], [], {"ruleId": "254", "severity": 1, "message": "255", "line": 18, "column": 24, "nodeType": "256", "messageId": "257", "endLine": 18, "endColumn": 39}, {"ruleId": "254", "severity": 1, "message": "258", "line": 19, "column": 19, "nodeType": "256", "messageId": "257", "endLine": 19, "endColumn": 29}, {"ruleId": "254", "severity": 1, "message": "259", "line": 8, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 8, "endColumn": 17}, {"ruleId": "254", "severity": 1, "message": "260", "line": 5, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 5, "endColumn": 10}, {"ruleId": "254", "severity": 1, "message": "261", "line": 14, "column": 8, "nodeType": "256", "messageId": "257", "endLine": 14, "endColumn": 25}, {"ruleId": "254", "severity": 1, "message": "262", "line": 22, "column": 10, "nodeType": "256", "messageId": "257", "endLine": 22, "endColumn": 27}, {"ruleId": "254", "severity": 1, "message": "263", "line": 24, "column": 10, "nodeType": "256", "messageId": "257", "endLine": 24, "endColumn": 20}, {"ruleId": "254", "severity": 1, "message": "264", "line": 71, "column": 9, "nodeType": "256", "messageId": "257", "endLine": 71, "endColumn": 30}, {"ruleId": "254", "severity": 1, "message": "265", "line": 19, "column": 37, "nodeType": "256", "messageId": "257", "endLine": 19, "endColumn": 49}, {"ruleId": "266", "severity": 1, "message": "267", "line": 56, "column": 6, "nodeType": "268", "endLine": 56, "endColumn": 8, "suggestions": "269"}, {"ruleId": "254", "severity": 1, "message": "270", "line": 74, "column": 9, "nodeType": "256", "messageId": "257", "endLine": 74, "endColumn": 24}, {"ruleId": "254", "severity": 1, "message": "271", "line": 5, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 5, "endColumn": 12}, {"ruleId": "266", "severity": 1, "message": "272", "line": 54, "column": 6, "nodeType": "268", "endLine": 54, "endColumn": 21, "suggestions": "273"}, {"ruleId": "274", "severity": 1, "message": "275", "line": 67, "column": 5, "nodeType": "276", "messageId": "277", "endLine": 94, "endColumn": 6}, {"ruleId": "254", "severity": 1, "message": "278", "line": 159, "column": 9, "nodeType": "256", "messageId": "257", "endLine": 159, "endColumn": 18}, {"ruleId": "266", "severity": 1, "message": "279", "line": 94, "column": 6, "nodeType": "268", "endLine": 94, "endColumn": 8, "suggestions": "280"}, {"ruleId": "254", "severity": 1, "message": "281", "line": 31, "column": 33, "nodeType": "256", "messageId": "257", "endLine": 31, "endColumn": 57}, {"ruleId": "254", "severity": 1, "message": "282", "line": 32, "column": 22, "nodeType": "256", "messageId": "257", "endLine": 32, "endColumn": 35}, {"ruleId": "254", "severity": 1, "message": "283", "line": 33, "column": 18, "nodeType": "256", "messageId": "257", "endLine": 33, "endColumn": 27}, {"ruleId": "254", "severity": 1, "message": "284", "line": 21, "column": 52, "nodeType": "256", "messageId": "257", "endLine": 21, "endColumn": 72}, {"ruleId": "254", "severity": 1, "message": "285", "line": 3, "column": 10, "nodeType": "256", "messageId": "257", "endLine": 3, "endColumn": 16}, {"ruleId": "254", "severity": 1, "message": "285", "line": 3, "column": 10, "nodeType": "256", "messageId": "257", "endLine": 3, "endColumn": 16}, {"ruleId": "254", "severity": 1, "message": "286", "line": 8, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 8, "endColumn": 11}, {"ruleId": "254", "severity": 1, "message": "287", "line": 8, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 8, "endColumn": 16}, {"ruleId": "254", "severity": 1, "message": "288", "line": 7, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 7, "endColumn": 11}, {"ruleId": "254", "severity": 1, "message": "286", "line": 8, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 8, "endColumn": 11}, {"ruleId": "254", "severity": 1, "message": "289", "line": 15, "column": 3, "nodeType": "256", "messageId": "257", "endLine": 15, "endColumn": 10}, "no-unused-vars", "'setPcCategories' is assigned a value but never used.", "Identifier", "unusedVar", "'setBundles' is assigned a value but never used.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'ConfirmationModal' is defined but never used.", "'showDeleteConfirm' is assigned a value but never used.", "'isDeleting' is assigned a value but never used.", "'confirmDeleteCategory' is assigned a value but never used.", "'isInWishlist' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'categories.length' and 'products'. Either include them or remove the dependency array.", "ArrayExpression", ["290"], "'handleSelectAll' is assigned a value but never used.", "'PhotoIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData.sku'. Either include it or remove the dependency array.", ["291"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'moveImage' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["292"], "'setSelectedComponentType' is assigned a value but never used.", "'setPriceRange' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'calculateBundlePrice' is assigned a value but never used.", "'motion' is defined but never used.", "'StarIcon' is defined but never used.", "'ArrowPathIcon' is defined but never used.", "'UserIcon' is defined but never used.", "'EyeIcon' is defined but never used.", {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, "Update the dependencies array to be: [categories.length, products]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [formData.name, formData.sku]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [removeToast]", {"range": "303", "text": "304"}, [1753, 1755], "[categories.length, products]", [1446, 1461], "[formData.name, formData.sku]", [2610, 2612], "[removeToast]"]