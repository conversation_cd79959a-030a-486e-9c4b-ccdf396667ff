[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "33", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js": "34", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js": "35", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js": "36", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js": "37", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js": "38", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js": "39", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js": "40", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js": "41", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js": "42", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "43", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js": "44"}, {"size": 584, "mtime": 1750249986668, "results": "45", "hashOfConfig": "46"}, {"size": 5003, "mtime": 1750108593800, "results": "47", "hashOfConfig": "46"}, {"size": 12100, "mtime": 1750327357055, "results": "48", "hashOfConfig": "46"}, {"size": 21682, "mtime": 1750327357085, "results": "49", "hashOfConfig": "46"}, {"size": 9872, "mtime": 1750164540216, "results": "50", "hashOfConfig": "46"}, {"size": 1876, "mtime": 1750247300183, "results": "51", "hashOfConfig": "46"}, {"size": 994, "mtime": 1750166931473, "results": "52", "hashOfConfig": "46"}, {"size": 7010, "mtime": 1750159469458, "results": "53", "hashOfConfig": "46"}, {"size": 8712, "mtime": 1750159406548, "results": "54", "hashOfConfig": "46"}, {"size": 11692, "mtime": 1750279696806, "results": "55", "hashOfConfig": "46"}, {"size": 7547, "mtime": 1750166070393, "results": "56", "hashOfConfig": "46"}, {"size": 4196, "mtime": 1750250970753, "results": "57", "hashOfConfig": "46"}, {"size": 11440, "mtime": 1750164163184, "results": "58", "hashOfConfig": "46"}, {"size": 23909, "mtime": 1750270542017, "results": "59", "hashOfConfig": "46"}, {"size": 12667, "mtime": 1750164007467, "results": "60", "hashOfConfig": "46"}, {"size": 19371, "mtime": 1750271571376, "results": "61", "hashOfConfig": "46"}, {"size": 12456, "mtime": 1750164967846, "results": "62", "hashOfConfig": "46"}, {"size": 9102, "mtime": 1750166125481, "results": "63", "hashOfConfig": "46"}, {"size": 15294, "mtime": 1750164732603, "results": "64", "hashOfConfig": "46"}, {"size": 7212, "mtime": 1750166452419, "results": "65", "hashOfConfig": "46"}, {"size": 12346, "mtime": 1750166411028, "results": "66", "hashOfConfig": "46"}, {"size": 13681, "mtime": 1750166786153, "results": "67", "hashOfConfig": "46"}, {"size": 12020, "mtime": 1750278446850, "results": "68", "hashOfConfig": "46"}, {"size": 3218, "mtime": 1750164772685, "results": "69", "hashOfConfig": "46"}, {"size": 9048, "mtime": 1750166894082, "results": "70", "hashOfConfig": "46"}, {"size": 9766, "mtime": 1750278585130, "results": "71", "hashOfConfig": "46"}, {"size": 8128, "mtime": 1750271460156, "results": "72", "hashOfConfig": "46"}, {"size": 16161, "mtime": 1750278064733, "results": "73", "hashOfConfig": "46"}, {"size": 2903, "mtime": 1750164073874, "results": "74", "hashOfConfig": "46"}, {"size": 1653, "mtime": 1750164096843, "results": "75", "hashOfConfig": "46"}, {"size": 34651, "mtime": 1750280202734, "results": "76", "hashOfConfig": "46"}, {"size": 7514, "mtime": 1750278548880, "results": "77", "hashOfConfig": "46"}, {"size": 58961, "mtime": 1750279507327, "results": "78", "hashOfConfig": "46"}, {"size": 3898, "mtime": 1750277956931, "results": "79", "hashOfConfig": "46"}, {"size": 3308, "mtime": 1750277932035, "results": "80", "hashOfConfig": "46"}, {"size": 34787, "mtime": 1750325648047, "results": "81", "hashOfConfig": "46"}, {"size": 16034, "mtime": 1750325557965, "results": "82", "hashOfConfig": "46"}, {"size": 13947, "mtime": 1750325586950, "results": "83", "hashOfConfig": "46"}, {"size": 12877, "mtime": 1750281306725, "results": "84", "hashOfConfig": "46"}, {"size": 14027, "mtime": 1750325997968, "results": "85", "hashOfConfig": "46"}, {"size": 15013, "mtime": 1750325501977, "results": "86", "hashOfConfig": "46"}, {"size": 16608, "mtime": 1750282102009, "results": "87", "hashOfConfig": "46"}, {"size": 13106, "mtime": 1750159208689, "results": "88", "hashOfConfig": "46"}, {"size": 12649, "mtime": 1750282260855, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js", ["222", "223"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["224"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["225"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js", ["226", "227", "228", "229"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["230"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js", ["231"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js", ["232"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js", ["233", "234", "235", "236"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ToastContext.js", ["237"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\pcGamingProducts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingPage.js", ["238", "239", "240"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\PcBuilder.js", ["241"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingComparePage.js", ["242"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PcGamingProductDetailPage.js", ["243", "244"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductSupport.js", ["245"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\CustomerSupportChat.js", ["246"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductQA.js", [], [], {"ruleId": "247", "severity": 1, "message": "248", "line": 18, "column": 24, "nodeType": "249", "messageId": "250", "endLine": 18, "endColumn": 39}, {"ruleId": "247", "severity": 1, "message": "251", "line": 19, "column": 19, "nodeType": "249", "messageId": "250", "endLine": 19, "endColumn": 29}, {"ruleId": "247", "severity": 1, "message": "252", "line": 8, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 8, "endColumn": 17}, {"ruleId": "247", "severity": 1, "message": "253", "line": 5, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 5, "endColumn": 10}, {"ruleId": "247", "severity": 1, "message": "254", "line": 14, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 14, "endColumn": 25}, {"ruleId": "247", "severity": 1, "message": "255", "line": 22, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 22, "endColumn": 27}, {"ruleId": "247", "severity": 1, "message": "256", "line": 24, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 24, "endColumn": 20}, {"ruleId": "247", "severity": 1, "message": "257", "line": 71, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 71, "endColumn": 30}, {"ruleId": "247", "severity": 1, "message": "258", "line": 18, "column": 37, "nodeType": "249", "messageId": "250", "endLine": 18, "endColumn": 49}, {"ruleId": "259", "severity": 1, "message": "260", "line": 56, "column": 6, "nodeType": "261", "endLine": 56, "endColumn": 8, "suggestions": "262"}, {"ruleId": "247", "severity": 1, "message": "263", "line": 74, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 74, "endColumn": 24}, {"ruleId": "247", "severity": 1, "message": "264", "line": 5, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 5, "endColumn": 12}, {"ruleId": "259", "severity": 1, "message": "265", "line": 54, "column": 6, "nodeType": "261", "endLine": 54, "endColumn": 21, "suggestions": "266"}, {"ruleId": "267", "severity": 1, "message": "268", "line": 67, "column": 5, "nodeType": "269", "messageId": "270", "endLine": 94, "endColumn": 6}, {"ruleId": "247", "severity": 1, "message": "271", "line": 159, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 159, "endColumn": 18}, {"ruleId": "259", "severity": 1, "message": "272", "line": 94, "column": 6, "nodeType": "261", "endLine": 94, "endColumn": 8, "suggestions": "273"}, {"ruleId": "247", "severity": 1, "message": "274", "line": 30, "column": 33, "nodeType": "249", "messageId": "250", "endLine": 30, "endColumn": 57}, {"ruleId": "247", "severity": 1, "message": "275", "line": 31, "column": 22, "nodeType": "249", "messageId": "250", "endLine": 31, "endColumn": 35}, {"ruleId": "247", "severity": 1, "message": "276", "line": 32, "column": 18, "nodeType": "249", "messageId": "250", "endLine": 32, "endColumn": 27}, {"ruleId": "247", "severity": 1, "message": "277", "line": 21, "column": 52, "nodeType": "249", "messageId": "250", "endLine": 21, "endColumn": 72}, {"ruleId": "247", "severity": 1, "message": "278", "line": 3, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "278", "line": 3, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "279", "line": 8, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 8, "endColumn": 11}, {"ruleId": "247", "severity": 1, "message": "280", "line": 8, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 8, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "281", "line": 7, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 7, "endColumn": 11}, "no-unused-vars", "'setPcCategories' is assigned a value but never used.", "Identifier", "unusedVar", "'setBundles' is assigned a value but never used.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'ConfirmationModal' is defined but never used.", "'showDeleteConfirm' is assigned a value but never used.", "'isDeleting' is assigned a value but never used.", "'confirmDeleteCategory' is assigned a value but never used.", "'isInWishlist' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'categories.length' and 'products'. Either include them or remove the dependency array.", "ArrayExpression", ["282"], "'handleSelectAll' is assigned a value but never used.", "'PhotoIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData.sku'. Either include it or remove the dependency array.", ["283"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'moveImage' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", ["284"], "'setSelectedComponentType' is assigned a value but never used.", "'setPriceRange' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'calculateBundlePrice' is assigned a value but never used.", "'motion' is defined but never used.", "'StarIcon' is defined but never used.", "'ArrowPathIcon' is defined but never used.", "'UserIcon' is defined but never used.", {"desc": "285", "fix": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, "Update the dependencies array to be: [categories.length, products]", {"range": "291", "text": "292"}, "Update the dependencies array to be: [formData.name, formData.sku]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [removeToast]", {"range": "295", "text": "296"}, [1753, 1755], "[categories.length, products]", [1446, 1461], "[formData.name, formData.sku]", [2610, 2612], "[removeToast]"]