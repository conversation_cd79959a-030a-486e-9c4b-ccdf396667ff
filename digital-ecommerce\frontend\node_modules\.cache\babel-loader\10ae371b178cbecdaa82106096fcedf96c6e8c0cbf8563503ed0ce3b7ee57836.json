{"ast": null, "code": "const statsBuffer = {\n  value: null,\n  addProjectionMetrics: null\n};\nexport { statsBuffer };", "map": {"version": 3, "names": ["statsBuffer", "value", "addProjectionMetrics"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/stats/buffer.mjs"], "sourcesContent": ["const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,KAAK,EAAE,IAAI;EACXC,oBAAoB,EAAE;AAC1B,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}