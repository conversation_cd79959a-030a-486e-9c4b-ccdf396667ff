{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\PcGamingComparePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { ArrowLeftIcon, ShoppingCartIcon, HeartIcon, CheckIcon, XMarkIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PcGamingComparePage = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const {\n    getProductById\n  } = useProducts();\n  const {\n    showSuccess\n  } = useToast();\n  const [products, setProducts] = useState([]);\n  useEffect(() => {\n    var _searchParams$get;\n    const productIds = ((_searchParams$get = searchParams.get('products')) === null || _searchParams$get === void 0 ? void 0 : _searchParams$get.split(',')) || [];\n    const loadedProducts = productIds.map(id => getProductById(id)).filter(Boolean);\n    setProducts(loadedProducts);\n  }, [searchParams, getProductById]);\n  const addToCart = product => {\n    showSuccess('Added to Cart', `${product.name} added to cart.`);\n  };\n  const addToWishlist = product => {\n    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);\n  };\n  if (products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"No Products to Compare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/pc-gaming\",\n          className: \"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeftIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Back to PC Gaming\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  const getSpecificationKeys = () => {\n    const allKeys = new Set();\n    products.forEach(product => {\n      if (product.specifications) {\n        Object.keys(product.specifications).forEach(key => allKeys.add(key));\n      }\n    });\n    return Array.from(allKeys);\n  };\n  const specKeys = getSpecificationKeys();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b border-gray-200 sticky top-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pc-gaming\",\n              className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowLeftIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Back to PC Gaming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"Compare Products (\", products.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-6 w-48 bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"PRODUCT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"p-6 text-center min-w-80\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: product.images[0],\n                      alt: product.name,\n                      className: \"w-32 h-32 object-cover rounded-lg mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-lg text-gray-900 mb-2\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `w-4 h-4 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                          }, i, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 112,\n                            columnNumber: 33\n                          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-600 ml-1\",\n                            children: [\"(\", product.reviews, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 119,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 110,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 109,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl font-bold text-light-orange-600\",\n                          children: [\"$\", product.price.toLocaleString()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 123,\n                          columnNumber: 29\n                        }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm text-gray-500 line-through ml-2\",\n                          children: [\"$\", product.originalPrice.toLocaleString()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 127,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 122,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => addToCart(product),\n                          className: \"flex-1 bg-light-orange-500 text-white py-2 px-4 rounded-lg hover:bg-light-orange-600 transition-colors flex items-center justify-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 137,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Add to Cart\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 138,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 133,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => addToWishlist(product),\n                          className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                          children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                            className: \"w-4 h-4 text-gray-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 144,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 140,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 132,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 23\n                  }, this)\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 text-sm text-gray-600\",\n                  children: product.description\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), products.map(product => {\n                  var _product$subcategory;\n                  return /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 text-sm text-gray-600\",\n                    children: (_product$subcategory = product.subcategory) === null || _product$subcategory === void 0 ? void 0 : _product$subcategory.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"In Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4\",\n                  children: product.inStock ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 text-green-600\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium\",\n                      children: \"In Stock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 text-red-600\",\n                    children: [/*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Out of Stock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 25\n                  }, this)\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), specKeys.map(specKey => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: specKey\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), products.map(product => {\n                  var _product$specificatio;\n                  return /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 text-sm text-gray-600\",\n                    children: ((_product$specificatio = product.specifications) === null || _product$specificatio === void 0 ? void 0 : _product$specificatio[specKey]) || '-'\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 23\n                  }, this);\n                })]\n              }, specKey, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)), products.some(p => p.performance) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: products.length + 1,\n                    className: \"p-4 font-semibold text-gray-900\",\n                    children: \"Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), ['1080p_ultra', '1440p_ultra', '4k_high', 'rayTracing'].map(perfKey => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                    children: perfKey.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), products.map(product => {\n                    var _product$performance;\n                    return /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"p-4 text-sm text-gray-600\",\n                      children: ((_product$performance = product.performance) === null || _product$performance === void 0 ? void 0 : _product$performance[perfKey]) || '-'\n                    }, product.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 27\n                    }, this);\n                  })]\n                }, perfKey, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true), products.some(p => p.features) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: products.length + 1,\n                    className: \"p-4 font-semibold text-gray-900\",\n                    children: \"Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                    children: \"Key Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4\",\n                    children: product.features ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"text-sm text-gray-600 space-y-1\",\n                      children: product.features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: \"flex items-start space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                          className: \"w-3 h-3 text-green-500 mt-0.5 flex-shrink-0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 247,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: feature\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 248,\n                          columnNumber: 35\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400\",\n                      children: \"-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 29\n                    }, this)\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Warranty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 text-sm text-gray-600\",\n                  children: product.warranty || '-'\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/pc-gaming\",\n          className: \"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Continue Shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(PcGamingComparePage, \"eRGWLOC0gpwCjsNq64aRItw37q0=\", false, function () {\n  return [useSearchParams, useProducts, useToast];\n});\n_c = PcGamingComparePage;\nexport default PcGamingComparePage;\nvar _c;\n$RefreshReg$(_c, \"PcGamingComparePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Link", "motion", "ArrowLeftIcon", "ShoppingCartIcon", "HeartIcon", "CheckIcon", "XMarkIcon", "StarIcon", "useProducts", "useToast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PcGamingComparePage", "_s", "searchParams", "getProductById", "showSuccess", "products", "setProducts", "_searchParams$get", "productIds", "get", "split", "loadedProducts", "map", "id", "filter", "Boolean", "addToCart", "product", "name", "addToWishlist", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "getSpecificationKeys", "allKeys", "Set", "for<PERSON>ach", "specifications", "Object", "keys", "key", "add", "Array", "from", "specKeys", "src", "images", "alt", "_", "i", "Math", "floor", "rating", "reviews", "price", "toLocaleString", "originalPrice", "onClick", "description", "_product$subcategory", "subcategory", "replace", "l", "toUpperCase", "inStock", "spec<PERSON><PERSON>", "_product$specificatio", "some", "p", "performance", "colSpan", "perfKey", "_product$performance", "features", "slice", "feature", "index", "warranty", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/PcGamingComparePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  ArrowLeftIcon,\n  ShoppingCartIcon,\n  HeartIcon,\n  CheckIcon,\n  XMarkIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\n\nconst PcGamingComparePage = () => {\n  const [searchParams] = useSearchParams();\n  const { getProductById } = useProducts();\n  const { showSuccess } = useToast();\n  const [products, setProducts] = useState([]);\n\n  useEffect(() => {\n    const productIds = searchParams.get('products')?.split(',') || [];\n    const loadedProducts = productIds.map(id => getProductById(id)).filter(Boolean);\n    setProducts(loadedProducts);\n  }, [searchParams, getProductById]);\n\n  const addToCart = (product) => {\n    showSuccess('Added to Cart', `${product.name} added to cart.`);\n  };\n\n  const addToWishlist = (product) => {\n    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);\n  };\n\n  if (products.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">No Products to Compare</h1>\n          <Link\n            to=\"/pc-gaming\"\n            className=\"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <ArrowLeftIcon className=\"w-5 h-5\" />\n            <span>Back to PC Gaming</span>\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  const getSpecificationKeys = () => {\n    const allKeys = new Set();\n    products.forEach(product => {\n      if (product.specifications) {\n        Object.keys(product.specifications).forEach(key => allKeys.add(key));\n      }\n    });\n    return Array.from(allKeys);\n  };\n\n  const specKeys = getSpecificationKeys();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                to=\"/pc-gaming\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowLeftIcon className=\"w-5 h-5\" />\n                <span>Back to PC Gaming</span>\n              </Link>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Compare Products ({products.length})\n              </h1>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Comparison Table */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              {/* Product Headers */}\n              <thead>\n                <tr className=\"border-b border-gray-200\">\n                  <th className=\"text-left p-6 w-48 bg-gray-50\">\n                    <span className=\"text-sm font-medium text-gray-500\">PRODUCT</span>\n                  </th>\n                  {products.map(product => (\n                    <th key={product.id} className=\"p-6 text-center min-w-80\">\n                      <div className=\"space-y-4\">\n                        <img\n                          src={product.images[0]}\n                          alt={product.name}\n                          className=\"w-32 h-32 object-cover rounded-lg mx-auto\"\n                        />\n                        <div>\n                          <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                            {product.name}\n                          </h3>\n                          <div className=\"flex items-center justify-center space-x-2 mb-2\">\n                            <div className=\"flex items-center\">\n                              {[...Array(5)].map((_, i) => (\n                                <StarIcon\n                                  key={i}\n                                  className={`w-4 h-4 ${\n                                    i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                                  }`}\n                                />\n                              ))}\n                              <span className=\"text-sm text-gray-600 ml-1\">({product.reviews})</span>\n                            </div>\n                          </div>\n                          <div className=\"text-center mb-4\">\n                            <span className=\"text-2xl font-bold text-light-orange-600\">\n                              ${product.price.toLocaleString()}\n                            </span>\n                            {product.originalPrice && product.originalPrice > product.price && (\n                              <span className=\"text-sm text-gray-500 line-through ml-2\">\n                                ${product.originalPrice.toLocaleString()}\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => addToCart(product)}\n                              className=\"flex-1 bg-light-orange-500 text-white py-2 px-4 rounded-lg hover:bg-light-orange-600 transition-colors flex items-center justify-center space-x-2\"\n                            >\n                              <ShoppingCartIcon className=\"w-4 h-4\" />\n                              <span>Add to Cart</span>\n                            </button>\n                            <button\n                              onClick={() => addToWishlist(product)}\n                              className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n                            >\n                              <HeartIcon className=\"w-4 h-4 text-gray-600\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </th>\n                  ))}\n                </tr>\n              </thead>\n\n              {/* Specifications */}\n              <tbody>\n                {/* Basic Info */}\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Description</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.description}\n                    </td>\n                  ))}\n                </tr>\n\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Category</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.subcategory?.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </td>\n                  ))}\n                </tr>\n\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">In Stock</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4\">\n                      {product.inStock ? (\n                        <div className=\"flex items-center space-x-2 text-green-600\">\n                          <CheckIcon className=\"w-4 h-4\" />\n                          <span className=\"text-sm font-medium\">In Stock</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-2 text-red-600\">\n                          <XMarkIcon className=\"w-4 h-4\" />\n                          <span className=\"text-sm font-medium\">Out of Stock</span>\n                        </div>\n                      )}\n                    </td>\n                  ))}\n                </tr>\n\n                {/* Specifications */}\n                {specKeys.map(specKey => (\n                  <tr key={specKey} className=\"border-b border-gray-100\">\n                    <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">\n                      {specKey}\n                    </td>\n                    {products.map(product => (\n                      <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                        {product.specifications?.[specKey] || '-'}\n                      </td>\n                    ))}\n                  </tr>\n                ))}\n\n                {/* Performance (if available) */}\n                {products.some(p => p.performance) && (\n                  <>\n                    <tr className=\"bg-gray-50\">\n                      <td colSpan={products.length + 1} className=\"p-4 font-semibold text-gray-900\">\n                        Performance\n                      </td>\n                    </tr>\n                    {['1080p_ultra', '1440p_ultra', '4k_high', 'rayTracing'].map(perfKey => (\n                      <tr key={perfKey} className=\"border-b border-gray-100\">\n                        <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">\n                          {perfKey.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                        </td>\n                        {products.map(product => (\n                          <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                            {product.performance?.[perfKey] || '-'}\n                          </td>\n                        ))}\n                      </tr>\n                    ))}\n                  </>\n                )}\n\n                {/* Features */}\n                {products.some(p => p.features) && (\n                  <>\n                    <tr className=\"bg-gray-50\">\n                      <td colSpan={products.length + 1} className=\"p-4 font-semibold text-gray-900\">\n                        Features\n                      </td>\n                    </tr>\n                    <tr className=\"border-b border-gray-100\">\n                      <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Key Features</td>\n                      {products.map(product => (\n                        <td key={product.id} className=\"p-4\">\n                          {product.features ? (\n                            <ul className=\"text-sm text-gray-600 space-y-1\">\n                              {product.features.slice(0, 5).map((feature, index) => (\n                                <li key={index} className=\"flex items-start space-x-2\">\n                                  <CheckIcon className=\"w-3 h-3 text-green-500 mt-0.5 flex-shrink-0\" />\n                                  <span>{feature}</span>\n                                </li>\n                              ))}\n                            </ul>\n                          ) : (\n                            <span className=\"text-gray-400\">-</span>\n                          )}\n                        </td>\n                      ))}\n                    </tr>\n                  </>\n                )}\n\n                {/* Warranty */}\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Warranty</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.warranty || '-'}\n                    </td>\n                  ))}\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"mt-8 text-center\">\n          <Link\n            to=\"/pc-gaming\"\n            className=\"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <span>Continue Shopping</span>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PcGamingComparePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,IAAI,QAAQ,kBAAkB;AACxD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,QAAQ,QACH,6BAA6B;AACpC,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,YAAY,CAAC,GAAGjB,eAAe,CAAC,CAAC;EACxC,MAAM;IAAEkB;EAAe,CAAC,GAAGT,WAAW,CAAC,CAAC;EACxC,MAAM;IAAEU;EAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAClC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IAAA,IAAAuB,iBAAA;IACd,MAAMC,UAAU,GAAG,EAAAD,iBAAA,GAAAL,YAAY,CAACO,GAAG,CAAC,UAAU,CAAC,cAAAF,iBAAA,uBAA5BA,iBAAA,CAA8BG,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE;IACjE,MAAMC,cAAc,GAAGH,UAAU,CAACI,GAAG,CAACC,EAAE,IAAIV,cAAc,CAACU,EAAE,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC/ET,WAAW,CAACK,cAAc,CAAC;EAC7B,CAAC,EAAE,CAACT,YAAY,EAAEC,cAAc,CAAC,CAAC;EAElC,MAAMa,SAAS,GAAIC,OAAO,IAAK;IAC7Bb,WAAW,CAAC,eAAe,EAAE,GAAGa,OAAO,CAACC,IAAI,iBAAiB,CAAC;EAChE,CAAC;EAED,MAAMC,aAAa,GAAIF,OAAO,IAAK;IACjCb,WAAW,CAAC,mBAAmB,EAAE,GAAGa,OAAO,CAACC,IAAI,qBAAqB,CAAC;EACxE,CAAC;EAED,IAAIb,QAAQ,CAACe,MAAM,KAAK,CAAC,EAAE;IACzB,oBACEvB,OAAA;MAAKwB,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC5GzB,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA;UAAIwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjF7B,OAAA,CAACX,IAAI;UACHyC,EAAE,EAAC,YAAY;UACfN,SAAS,EAAC,oIAAoI;UAAAC,QAAA,gBAE9IzB,OAAA,CAACT,aAAa;YAACiC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC7B,OAAA;YAAAyB,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzBzB,QAAQ,CAAC0B,OAAO,CAACd,OAAO,IAAI;MAC1B,IAAIA,OAAO,CAACe,cAAc,EAAE;QAC1BC,MAAM,CAACC,IAAI,CAACjB,OAAO,CAACe,cAAc,CAAC,CAACD,OAAO,CAACI,GAAG,IAAIN,OAAO,CAACO,GAAG,CAACD,GAAG,CAAC,CAAC;MACtE;IACF,CAAC,CAAC;IACF,OAAOE,KAAK,CAACC,IAAI,CAACT,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMU,QAAQ,GAAGX,oBAAoB,CAAC,CAAC;EAEvC,oBACE/B,OAAA;IAAKwB,SAAS,EAAC,8DAA8D;IAAAC,QAAA,gBAE3EzB,OAAA;MAAKwB,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEzB,OAAA;QAAKwB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1DzB,OAAA;UAAKwB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDzB,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzB,OAAA,CAACX,IAAI;cACHyC,EAAE,EAAC,YAAY;cACfN,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAE3FzB,OAAA,CAACT,aAAa;gBAACiC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrC7B,OAAA;gBAAAyB,QAAA,EAAM;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACP7B,OAAA;cAAIwB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,oBAC7B,EAACjB,QAAQ,CAACe,MAAM,EAAC,GACrC;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DzB,OAAA;QAAKwB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DzB,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BzB,OAAA;YAAOwB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBAEvBzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAIwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCzB,OAAA;kBAAIwB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC3CzB,OAAA;oBAAMwB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBpB,OAAA;kBAAqBwB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvDzB,OAAA;oBAAKwB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBzB,OAAA;sBACE2C,GAAG,EAAEvB,OAAO,CAACwB,MAAM,CAAC,CAAC,CAAE;sBACvBC,GAAG,EAAEzB,OAAO,CAACC,IAAK;sBAClBG,SAAS,EAAC;oBAA2C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF7B,OAAA;sBAAAyB,QAAA,gBACEzB,OAAA;wBAAIwB,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EACrDL,OAAO,CAACC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACL7B,OAAA;wBAAKwB,SAAS,EAAC,iDAAiD;wBAAAC,QAAA,eAC9DzB,OAAA;0BAAKwB,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAC/B,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACzB,GAAG,CAAC,CAAC+B,CAAC,EAAEC,CAAC,kBACtB/C,OAAA,CAACJ,QAAQ;4BAEP4B,SAAS,EAAE,WACTuB,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC7B,OAAO,CAAC8B,MAAM,CAAC,GAAG,8BAA8B,GAAG,eAAe;0BAChF,GAHEH,CAAC;4BAAArB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAIP,CACF,CAAC,eACF7B,OAAA;4BAAMwB,SAAS,EAAC,4BAA4B;4BAAAC,QAAA,GAAC,GAAC,EAACL,OAAO,CAAC+B,OAAO,EAAC,GAAC;0BAAA;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN7B,OAAA;wBAAKwB,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAC/BzB,OAAA;0BAAMwB,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,GAAC,GACxD,EAACL,OAAO,CAACgC,KAAK,CAACC,cAAc,CAAC,CAAC;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,EACNT,OAAO,CAACkC,aAAa,IAAIlC,OAAO,CAACkC,aAAa,GAAGlC,OAAO,CAACgC,KAAK,iBAC7DpD,OAAA;0BAAMwB,SAAS,EAAC,yCAAyC;0BAAAC,QAAA,GAAC,GACvD,EAACL,OAAO,CAACkC,aAAa,CAACD,cAAc,CAAC,CAAC;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACN7B,OAAA;wBAAKwB,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BzB,OAAA;0BACEuD,OAAO,EAAEA,CAAA,KAAMpC,SAAS,CAACC,OAAO,CAAE;0BAClCI,SAAS,EAAC,mJAAmJ;0BAAAC,QAAA,gBAE7JzB,OAAA,CAACR,gBAAgB;4BAACgC,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACxC7B,OAAA;4BAAAyB,QAAA,EAAM;0BAAW;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CAAC,eACT7B,OAAA;0BACEuD,OAAO,EAAEA,CAAA,KAAMjC,aAAa,CAACF,OAAO,CAAE;0BACtCI,SAAS,EAAC,0EAA0E;0BAAAC,QAAA,eAEpFzB,OAAA,CAACP,SAAS;4BAAC+B,SAAS,EAAC;0BAAuB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAlDCT,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmDf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGR7B,OAAA;cAAAyB,QAAA,gBAEEzB,OAAA;gBAAIwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCzB,OAAA;kBAAIwB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBpB,OAAA;kBAAqBwB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvDL,OAAO,CAACoC;gBAAW,GADbpC,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAEL7B,OAAA;gBAAIwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCzB,OAAA;kBAAIwB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO;kBAAA,IAAAqC,oBAAA;kBAAA,oBACnBzD,OAAA;oBAAqBwB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,GAAAgC,oBAAA,GACvDrC,OAAO,CAACsC,WAAW,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC,GADvEzC,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CAAC;gBAAA,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAEL7B,OAAA;gBAAIwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCzB,OAAA;kBAAIwB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBpB,OAAA;kBAAqBwB,SAAS,EAAC,KAAK;kBAAAC,QAAA,EACjCL,OAAO,CAAC0C,OAAO,gBACd9D,OAAA;oBAAKwB,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDzB,OAAA,CAACN,SAAS;sBAAC8B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC7B,OAAA;sBAAMwB,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEN7B,OAAA;oBAAKwB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,gBACvDzB,OAAA,CAACL,SAAS;sBAAC6B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC7B,OAAA;sBAAMwB,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBACN,GAXMT,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EAGJa,QAAQ,CAAC3B,GAAG,CAACgD,OAAO,iBACnB/D,OAAA;gBAAkBwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACpDzB,OAAA;kBAAIwB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACrDsC;gBAAO;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO;kBAAA,IAAA4C,qBAAA;kBAAA,oBACnBhE,OAAA;oBAAqBwB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACvD,EAAAuC,qBAAA,GAAA5C,OAAO,CAACe,cAAc,cAAA6B,qBAAA,uBAAtBA,qBAAA,CAAyBD,OAAO,CAAC,KAAI;kBAAG,GADlC3C,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CAAC;gBAAA,CACN,CAAC;cAAA,GARKkC,OAAO;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASZ,CACL,CAAC,EAGDrB,QAAQ,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,iBAChCnE,OAAA,CAAAE,SAAA;gBAAAuB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACxBzB,OAAA;oBAAIoE,OAAO,EAAE5D,QAAQ,CAACe,MAAM,GAAG,CAAE;oBAACC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAE9E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACJ,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC,CAACd,GAAG,CAACsD,OAAO,iBAClErE,OAAA;kBAAkBwB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACpDzB,OAAA;oBAAIwB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrD4C,OAAO,CAACV,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO;oBAAA,IAAAkD,oBAAA;oBAAA,oBACnBtE,OAAA;sBAAqBwB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EACvD,EAAA6C,oBAAA,GAAAlD,OAAO,CAAC+C,WAAW,cAAAG,oBAAA,uBAAnBA,oBAAA,CAAsBD,OAAO,CAAC,KAAI;oBAAG,GAD/BjD,OAAO,CAACJ,EAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CAAC;kBAAA,CACN,CAAC;gBAAA,GARKwC,OAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASZ,CACL,CAAC;cAAA,eACF,CACH,EAGArB,QAAQ,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,QAAQ,CAAC,iBAC7BvE,OAAA,CAAAE,SAAA;gBAAAuB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACxBzB,OAAA;oBAAIoE,OAAO,EAAE5D,QAAQ,CAACe,MAAM,GAAG,CAAE;oBAACC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAE9E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACL7B,OAAA;kBAAIwB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACtCzB,OAAA;oBAAIwB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBpB,OAAA;oBAAqBwB,SAAS,EAAC,KAAK;oBAAAC,QAAA,EACjCL,OAAO,CAACmD,QAAQ,gBACfvE,OAAA;sBAAIwB,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC5CL,OAAO,CAACmD,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzD,GAAG,CAAC,CAAC0D,OAAO,EAAEC,KAAK,kBAC/C1E,OAAA;wBAAgBwB,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACpDzB,OAAA,CAACN,SAAS;0BAAC8B,SAAS,EAAC;wBAA6C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACrE7B,OAAA;0BAAAyB,QAAA,EAAOgD;wBAAO;0BAAA/C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAFf6C,KAAK;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGV,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,gBAEL7B,OAAA;sBAAMwB,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACxC,GAZMT,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAaf,CACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,eACL,CACH,eAGD7B,OAAA;gBAAIwB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCzB,OAAA;kBAAIwB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBpB,OAAA;kBAAqBwB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvDL,OAAO,CAACuD,QAAQ,IAAI;gBAAG,GADjBvD,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BzB,OAAA,CAACX,IAAI;UACHyC,EAAE,EAAC,YAAY;UACfN,SAAS,EAAC,oIAAoI;UAAAC,QAAA,eAE9IzB,OAAA;YAAAyB,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAhRID,mBAAmB;EAAA,QACAf,eAAe,EACXS,WAAW,EACdC,QAAQ;AAAA;AAAA8E,EAAA,GAH5BzE,mBAAmB;AAkRzB,eAAeA,mBAAmB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}