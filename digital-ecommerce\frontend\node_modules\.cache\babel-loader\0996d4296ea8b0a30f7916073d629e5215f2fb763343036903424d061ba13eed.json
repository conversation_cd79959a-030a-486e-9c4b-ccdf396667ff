{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\PcGamingComparePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { ArrowLeftIcon, ShoppingCartIcon, HeartIcon, CheckIcon, XMarkIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport { useCart } from '../components/ShoppingCart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PcGamingComparePage = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const {\n    getProductById\n  } = useProducts();\n  const {\n    showSuccess\n  } = useToast();\n  const {\n    addToCart\n  } = useCart();\n  const [products, setProducts] = useState([]);\n  useEffect(() => {\n    var _searchParams$get;\n    const productIds = ((_searchParams$get = searchParams.get('products')) === null || _searchParams$get === void 0 ? void 0 : _searchParams$get.split(',')) || [];\n    const loadedProducts = productIds.map(id => getProductById(id)).filter(Boolean);\n    setProducts(loadedProducts);\n  }, [searchParams, getProductById]);\n  const handleAddToCart = product => {\n    addToCart(product);\n    showSuccess('Added to Cart', `${product.name} added to cart.`);\n  };\n  const addToWishlist = product => {\n    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);\n  };\n  if (products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"No Products to Compare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/pc-gaming\",\n          className: \"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeftIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Back to PC Gaming\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  const getSpecificationKeys = () => {\n    const allKeys = new Set();\n    products.forEach(product => {\n      if (product.specifications) {\n        Object.keys(product.specifications).forEach(key => allKeys.add(key));\n      }\n    });\n    return Array.from(allKeys);\n  };\n  const specKeys = getSpecificationKeys();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b border-gray-200 sticky top-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/pc-gaming\",\n              className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowLeftIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Back to PC Gaming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"Compare Products (\", products.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-6 w-48 bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"PRODUCT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"p-6 text-center min-w-80\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: product.images[0],\n                      alt: product.name,\n                      className: \"w-32 h-32 object-cover rounded-lg mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-lg text-gray-900 mb-2\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 109,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `w-4 h-4 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                          }, i, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 115,\n                            columnNumber: 33\n                          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-600 ml-1\",\n                            children: [\"(\", product.reviews, \")\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 122,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 113,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 112,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl font-bold text-light-orange-600\",\n                          children: [\"$\", product.price.toLocaleString()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 126,\n                          columnNumber: 29\n                        }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm text-gray-500 line-through ml-2\",\n                          children: [\"$\", product.originalPrice.toLocaleString()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 130,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 125,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleAddToCart(product),\n                          disabled: !product.inStock,\n                          className: `flex-1 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 ${product.inStock ? 'bg-light-orange-500 text-white hover:bg-light-orange-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n                          children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 145,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: product.inStock ? 'Add to Cart' : 'Out of Stock'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 146,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 136,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => addToWishlist(product),\n                          className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                          children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                            className: \"w-4 h-4 text-gray-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 152,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 148,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 135,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this)\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 text-sm text-gray-600\",\n                  children: product.description\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), products.map(product => {\n                  var _product$subcategory;\n                  return /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 text-sm text-gray-600\",\n                    children: (_product$subcategory = product.subcategory) === null || _product$subcategory === void 0 ? void 0 : _product$subcategory.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"In Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4\",\n                  children: product.inStock ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 text-green-600\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium\",\n                      children: \"In Stock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 text-red-600\",\n                    children: [/*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Out of Stock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), specKeys.map(specKey => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: specKey\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), products.map(product => {\n                  var _product$specificatio;\n                  return /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 text-sm text-gray-600\",\n                    children: ((_product$specificatio = product.specifications) === null || _product$specificatio === void 0 ? void 0 : _product$specificatio[specKey]) || '-'\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this);\n                })]\n              }, specKey, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)), products.some(p => p.performance) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: products.length + 1,\n                    className: \"p-4 font-semibold text-gray-900\",\n                    children: \"Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), ['1080p_ultra', '1440p_ultra', '4k_high', 'rayTracing'].map(perfKey => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                    children: perfKey.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 25\n                  }, this), products.map(product => {\n                    var _product$performance;\n                    return /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"p-4 text-sm text-gray-600\",\n                      children: ((_product$performance = product.performance) === null || _product$performance === void 0 ? void 0 : _product$performance[perfKey]) || '-'\n                    }, product.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this);\n                  })]\n                }, perfKey, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true), products.some(p => p.features) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: products.length + 1,\n                    className: \"p-4 font-semibold text-gray-900\",\n                    children: \"Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                    children: \"Key Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-4\",\n                    children: product.features ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"text-sm text-gray-600 space-y-1\",\n                      children: product.features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: \"flex items-start space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                          className: \"w-3 h-3 text-green-500 mt-0.5 flex-shrink-0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 255,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: feature\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 256,\n                          columnNumber: 35\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400\",\n                      children: \"-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 29\n                    }, this)\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 bg-gray-50 font-medium text-gray-700\",\n                  children: \"Warranty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 text-sm text-gray-600\",\n                  children: product.warranty || '-'\n                }, product.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/pc-gaming\",\n          className: \"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Continue Shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(PcGamingComparePage, \"4Km5RYkePXiYp63j6jYEsYLDmX0=\", false, function () {\n  return [useSearchParams, useProducts, useToast, useCart];\n});\n_c = PcGamingComparePage;\nexport default PcGamingComparePage;\nvar _c;\n$RefreshReg$(_c, \"PcGamingComparePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Link", "motion", "ArrowLeftIcon", "ShoppingCartIcon", "HeartIcon", "CheckIcon", "XMarkIcon", "StarIcon", "useProducts", "useToast", "useCart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PcGamingComparePage", "_s", "searchParams", "getProductById", "showSuccess", "addToCart", "products", "setProducts", "_searchParams$get", "productIds", "get", "split", "loadedProducts", "map", "id", "filter", "Boolean", "handleAddToCart", "product", "name", "addToWishlist", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "getSpecificationKeys", "allKeys", "Set", "for<PERSON>ach", "specifications", "Object", "keys", "key", "add", "Array", "from", "specKeys", "src", "images", "alt", "_", "i", "Math", "floor", "rating", "reviews", "price", "toLocaleString", "originalPrice", "onClick", "disabled", "inStock", "description", "_product$subcategory", "subcategory", "replace", "l", "toUpperCase", "spec<PERSON><PERSON>", "_product$specificatio", "some", "p", "performance", "colSpan", "perfKey", "_product$performance", "features", "slice", "feature", "index", "warranty", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/PcGamingComparePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  ArrowLeftIcon,\n  ShoppingCartIcon,\n  HeartIcon,\n  CheckIcon,\n  XMarkIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport { useCart } from '../components/ShoppingCart';\n\nconst PcGamingComparePage = () => {\n  const [searchParams] = useSearchParams();\n  const { getProductById } = useProducts();\n  const { showSuccess } = useToast();\n  const { addToCart } = useCart();\n  const [products, setProducts] = useState([]);\n\n  useEffect(() => {\n    const productIds = searchParams.get('products')?.split(',') || [];\n    const loadedProducts = productIds.map(id => getProductById(id)).filter(Boolean);\n    setProducts(loadedProducts);\n  }, [searchParams, getProductById]);\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    showSuccess('Added to Cart', `${product.name} added to cart.`);\n  };\n\n  const addToWishlist = (product) => {\n    showSuccess('Added to Wishlist', `${product.name} added to wishlist.`);\n  };\n\n  if (products.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">No Products to Compare</h1>\n          <Link\n            to=\"/pc-gaming\"\n            className=\"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <ArrowLeftIcon className=\"w-5 h-5\" />\n            <span>Back to PC Gaming</span>\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  const getSpecificationKeys = () => {\n    const allKeys = new Set();\n    products.forEach(product => {\n      if (product.specifications) {\n        Object.keys(product.specifications).forEach(key => allKeys.add(key));\n      }\n    });\n    return Array.from(allKeys);\n  };\n\n  const specKeys = getSpecificationKeys();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                to=\"/pc-gaming\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowLeftIcon className=\"w-5 h-5\" />\n                <span>Back to PC Gaming</span>\n              </Link>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Compare Products ({products.length})\n              </h1>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Comparison Table */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              {/* Product Headers */}\n              <thead>\n                <tr className=\"border-b border-gray-200\">\n                  <th className=\"text-left p-6 w-48 bg-gray-50\">\n                    <span className=\"text-sm font-medium text-gray-500\">PRODUCT</span>\n                  </th>\n                  {products.map(product => (\n                    <th key={product.id} className=\"p-6 text-center min-w-80\">\n                      <div className=\"space-y-4\">\n                        <img\n                          src={product.images[0]}\n                          alt={product.name}\n                          className=\"w-32 h-32 object-cover rounded-lg mx-auto\"\n                        />\n                        <div>\n                          <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                            {product.name}\n                          </h3>\n                          <div className=\"flex items-center justify-center space-x-2 mb-2\">\n                            <div className=\"flex items-center\">\n                              {[...Array(5)].map((_, i) => (\n                                <StarIcon\n                                  key={i}\n                                  className={`w-4 h-4 ${\n                                    i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                                  }`}\n                                />\n                              ))}\n                              <span className=\"text-sm text-gray-600 ml-1\">({product.reviews})</span>\n                            </div>\n                          </div>\n                          <div className=\"text-center mb-4\">\n                            <span className=\"text-2xl font-bold text-light-orange-600\">\n                              ${product.price.toLocaleString()}\n                            </span>\n                            {product.originalPrice && product.originalPrice > product.price && (\n                              <span className=\"text-sm text-gray-500 line-through ml-2\">\n                                ${product.originalPrice.toLocaleString()}\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleAddToCart(product)}\n                              disabled={!product.inStock}\n                              className={`flex-1 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 ${\n                                product.inStock\n                                  ? 'bg-light-orange-500 text-white hover:bg-light-orange-600'\n                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                              }`}\n                            >\n                              <ShoppingCartIcon className=\"w-4 h-4\" />\n                              <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>\n                            </button>\n                            <button\n                              onClick={() => addToWishlist(product)}\n                              className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n                            >\n                              <HeartIcon className=\"w-4 h-4 text-gray-600\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </th>\n                  ))}\n                </tr>\n              </thead>\n\n              {/* Specifications */}\n              <tbody>\n                {/* Basic Info */}\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Description</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.description}\n                    </td>\n                  ))}\n                </tr>\n\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Category</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.subcategory?.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </td>\n                  ))}\n                </tr>\n\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">In Stock</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4\">\n                      {product.inStock ? (\n                        <div className=\"flex items-center space-x-2 text-green-600\">\n                          <CheckIcon className=\"w-4 h-4\" />\n                          <span className=\"text-sm font-medium\">In Stock</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-2 text-red-600\">\n                          <XMarkIcon className=\"w-4 h-4\" />\n                          <span className=\"text-sm font-medium\">Out of Stock</span>\n                        </div>\n                      )}\n                    </td>\n                  ))}\n                </tr>\n\n                {/* Specifications */}\n                {specKeys.map(specKey => (\n                  <tr key={specKey} className=\"border-b border-gray-100\">\n                    <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">\n                      {specKey}\n                    </td>\n                    {products.map(product => (\n                      <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                        {product.specifications?.[specKey] || '-'}\n                      </td>\n                    ))}\n                  </tr>\n                ))}\n\n                {/* Performance (if available) */}\n                {products.some(p => p.performance) && (\n                  <>\n                    <tr className=\"bg-gray-50\">\n                      <td colSpan={products.length + 1} className=\"p-4 font-semibold text-gray-900\">\n                        Performance\n                      </td>\n                    </tr>\n                    {['1080p_ultra', '1440p_ultra', '4k_high', 'rayTracing'].map(perfKey => (\n                      <tr key={perfKey} className=\"border-b border-gray-100\">\n                        <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">\n                          {perfKey.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                        </td>\n                        {products.map(product => (\n                          <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                            {product.performance?.[perfKey] || '-'}\n                          </td>\n                        ))}\n                      </tr>\n                    ))}\n                  </>\n                )}\n\n                {/* Features */}\n                {products.some(p => p.features) && (\n                  <>\n                    <tr className=\"bg-gray-50\">\n                      <td colSpan={products.length + 1} className=\"p-4 font-semibold text-gray-900\">\n                        Features\n                      </td>\n                    </tr>\n                    <tr className=\"border-b border-gray-100\">\n                      <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Key Features</td>\n                      {products.map(product => (\n                        <td key={product.id} className=\"p-4\">\n                          {product.features ? (\n                            <ul className=\"text-sm text-gray-600 space-y-1\">\n                              {product.features.slice(0, 5).map((feature, index) => (\n                                <li key={index} className=\"flex items-start space-x-2\">\n                                  <CheckIcon className=\"w-3 h-3 text-green-500 mt-0.5 flex-shrink-0\" />\n                                  <span>{feature}</span>\n                                </li>\n                              ))}\n                            </ul>\n                          ) : (\n                            <span className=\"text-gray-400\">-</span>\n                          )}\n                        </td>\n                      ))}\n                    </tr>\n                  </>\n                )}\n\n                {/* Warranty */}\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"p-4 bg-gray-50 font-medium text-gray-700\">Warranty</td>\n                  {products.map(product => (\n                    <td key={product.id} className=\"p-4 text-sm text-gray-600\">\n                      {product.warranty || '-'}\n                    </td>\n                  ))}\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"mt-8 text-center\">\n          <Link\n            to=\"/pc-gaming\"\n            className=\"inline-flex items-center space-x-2 px-6 py-3 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <span>Continue Shopping</span>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PcGamingComparePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,IAAI,QAAQ,kBAAkB;AACxD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,QAAQ,QACH,6BAA6B;AACpC,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,YAAY,CAAC,GAAGlB,eAAe,CAAC,CAAC;EACxC,MAAM;IAAEmB;EAAe,CAAC,GAAGV,WAAW,CAAC,CAAC;EACxC,MAAM;IAAEW;EAAY,CAAC,GAAGV,QAAQ,CAAC,CAAC;EAClC,MAAM;IAAEW;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IAAA,IAAAyB,iBAAA;IACd,MAAMC,UAAU,GAAG,EAAAD,iBAAA,GAAAN,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,cAAAF,iBAAA,uBAA5BA,iBAAA,CAA8BG,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE;IACjE,MAAMC,cAAc,GAAGH,UAAU,CAACI,GAAG,CAACC,EAAE,IAAIX,cAAc,CAACW,EAAE,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC/ET,WAAW,CAACK,cAAc,CAAC;EAC7B,CAAC,EAAE,CAACV,YAAY,EAAEC,cAAc,CAAC,CAAC;EAElC,MAAMc,eAAe,GAAIC,OAAO,IAAK;IACnCb,SAAS,CAACa,OAAO,CAAC;IAClBd,WAAW,CAAC,eAAe,EAAE,GAAGc,OAAO,CAACC,IAAI,iBAAiB,CAAC;EAChE,CAAC;EAED,MAAMC,aAAa,GAAIF,OAAO,IAAK;IACjCd,WAAW,CAAC,mBAAmB,EAAE,GAAGc,OAAO,CAACC,IAAI,qBAAqB,CAAC;EACxE,CAAC;EAED,IAAIb,QAAQ,CAACe,MAAM,KAAK,CAAC,EAAE;IACzB,oBACExB,OAAA;MAAKyB,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC5G1B,OAAA;QAAKyB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1B,OAAA;UAAIyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjF9B,OAAA,CAACZ,IAAI;UACH2C,EAAE,EAAC,YAAY;UACfN,SAAS,EAAC,oIAAoI;UAAAC,QAAA,gBAE9I1B,OAAA,CAACV,aAAa;YAACmC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC9B,OAAA;YAAA0B,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzBzB,QAAQ,CAAC0B,OAAO,CAACd,OAAO,IAAI;MAC1B,IAAIA,OAAO,CAACe,cAAc,EAAE;QAC1BC,MAAM,CAACC,IAAI,CAACjB,OAAO,CAACe,cAAc,CAAC,CAACD,OAAO,CAACI,GAAG,IAAIN,OAAO,CAACO,GAAG,CAACD,GAAG,CAAC,CAAC;MACtE;IACF,CAAC,CAAC;IACF,OAAOE,KAAK,CAACC,IAAI,CAACT,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMU,QAAQ,GAAGX,oBAAoB,CAAC,CAAC;EAEvC,oBACEhC,OAAA;IAAKyB,SAAS,EAAC,8DAA8D;IAAAC,QAAA,gBAE3E1B,OAAA;MAAKyB,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClE1B,OAAA;QAAKyB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D1B,OAAA;UAAKyB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD1B,OAAA;YAAKyB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1B,OAAA,CAACZ,IAAI;cACH2C,EAAE,EAAC,YAAY;cACfN,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAE3F1B,OAAA,CAACV,aAAa;gBAACmC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrC9B,OAAA;gBAAA0B,QAAA,EAAM;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACP9B,OAAA;cAAIyB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,oBAC7B,EAACjB,QAAQ,CAACe,MAAM,EAAC,GACrC;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D1B,OAAA;QAAKyB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D1B,OAAA;UAAKyB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1B,OAAA;YAAOyB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBAEvB1B,OAAA;cAAA0B,QAAA,eACE1B,OAAA;gBAAIyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtC1B,OAAA;kBAAIyB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,eAC3C1B,OAAA;oBAAMyB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBrB,OAAA;kBAAqByB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvD1B,OAAA;oBAAKyB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB1B,OAAA;sBACE4C,GAAG,EAAEvB,OAAO,CAACwB,MAAM,CAAC,CAAC,CAAE;sBACvBC,GAAG,EAAEzB,OAAO,CAACC,IAAK;sBAClBG,SAAS,EAAC;oBAA2C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACF9B,OAAA;sBAAA0B,QAAA,gBACE1B,OAAA;wBAAIyB,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EACrDL,OAAO,CAACC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACL9B,OAAA;wBAAKyB,SAAS,EAAC,iDAAiD;wBAAAC,QAAA,eAC9D1B,OAAA;0BAAKyB,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAC/B,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACzB,GAAG,CAAC,CAAC+B,CAAC,EAAEC,CAAC,kBACtBhD,OAAA,CAACL,QAAQ;4BAEP8B,SAAS,EAAE,WACTuB,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC7B,OAAO,CAAC8B,MAAM,CAAC,GAAG,8BAA8B,GAAG,eAAe;0BAChF,GAHEH,CAAC;4BAAArB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAIP,CACF,CAAC,eACF9B,OAAA;4BAAMyB,SAAS,EAAC,4BAA4B;4BAAAC,QAAA,GAAC,GAAC,EAACL,OAAO,CAAC+B,OAAO,EAAC,GAAC;0BAAA;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9B,OAAA;wBAAKyB,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAC/B1B,OAAA;0BAAMyB,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,GAAC,GACxD,EAACL,OAAO,CAACgC,KAAK,CAACC,cAAc,CAAC,CAAC;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,EACNT,OAAO,CAACkC,aAAa,IAAIlC,OAAO,CAACkC,aAAa,GAAGlC,OAAO,CAACgC,KAAK,iBAC7DrD,OAAA;0BAAMyB,SAAS,EAAC,yCAAyC;0BAAAC,QAAA,GAAC,GACvD,EAACL,OAAO,CAACkC,aAAa,CAACD,cAAc,CAAC,CAAC;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACN9B,OAAA;wBAAKyB,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7B1B,OAAA;0BACEwD,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAACC,OAAO,CAAE;0BACxCoC,QAAQ,EAAE,CAACpC,OAAO,CAACqC,OAAQ;0BAC3BjC,SAAS,EAAE,4FACTJ,OAAO,CAACqC,OAAO,GACX,0DAA0D,GAC1D,8CAA8C,EACjD;0BAAAhC,QAAA,gBAEH1B,OAAA,CAACT,gBAAgB;4BAACkC,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACxC9B,OAAA;4BAAA0B,QAAA,EAAOL,OAAO,CAACqC,OAAO,GAAG,aAAa,GAAG;0BAAc;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzD,CAAC,eACT9B,OAAA;0BACEwD,OAAO,EAAEA,CAAA,KAAMjC,aAAa,CAACF,OAAO,CAAE;0BACtCI,SAAS,EAAC,0EAA0E;0BAAAC,QAAA,eAEpF1B,OAAA,CAACR,SAAS;4BAACiC,SAAS,EAAC;0BAAuB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAvDCT,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGR9B,OAAA;cAAA0B,QAAA,gBAEE1B,OAAA;gBAAIyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtC1B,OAAA;kBAAIyB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBrB,OAAA;kBAAqByB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvDL,OAAO,CAACsC;gBAAW,GADbtC,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAEL9B,OAAA;gBAAIyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtC1B,OAAA;kBAAIyB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO;kBAAA,IAAAuC,oBAAA;kBAAA,oBACnB5D,OAAA;oBAAqByB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,GAAAkC,oBAAA,GACvDvC,OAAO,CAACwC,WAAW,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC,GADvE3C,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CAAC;gBAAA,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAEL9B,OAAA;gBAAIyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtC1B,OAAA;kBAAIyB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBrB,OAAA;kBAAqByB,SAAS,EAAC,KAAK;kBAAAC,QAAA,EACjCL,OAAO,CAACqC,OAAO,gBACd1D,OAAA;oBAAKyB,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD1B,OAAA,CAACP,SAAS;sBAACgC,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC9B,OAAA;sBAAMyB,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEN9B,OAAA;oBAAKyB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,gBACvD1B,OAAA,CAACN,SAAS;sBAAC+B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjC9B,OAAA;sBAAMyB,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBACN,GAXMT,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EAGJa,QAAQ,CAAC3B,GAAG,CAACiD,OAAO,iBACnBjE,OAAA;gBAAkByB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACpD1B,OAAA;kBAAIyB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACrDuC;gBAAO;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO;kBAAA,IAAA6C,qBAAA;kBAAA,oBACnBlE,OAAA;oBAAqByB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACvD,EAAAwC,qBAAA,GAAA7C,OAAO,CAACe,cAAc,cAAA8B,qBAAA,uBAAtBA,qBAAA,CAAyBD,OAAO,CAAC,KAAI;kBAAG,GADlC5C,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CAAC;gBAAA,CACN,CAAC;cAAA,GARKmC,OAAO;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASZ,CACL,CAAC,EAGDrB,QAAQ,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,iBAChCrE,OAAA,CAAAE,SAAA;gBAAAwB,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACxB1B,OAAA;oBAAIsE,OAAO,EAAE7D,QAAQ,CAACe,MAAM,GAAG,CAAE;oBAACC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAE9E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACJ,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC,CAACd,GAAG,CAACuD,OAAO,iBAClEvE,OAAA;kBAAkByB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACpD1B,OAAA;oBAAIyB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrD6C,OAAO,CAACT,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EACJrB,QAAQ,CAACO,GAAG,CAACK,OAAO;oBAAA,IAAAmD,oBAAA;oBAAA,oBACnBxE,OAAA;sBAAqByB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EACvD,EAAA8C,oBAAA,GAAAnD,OAAO,CAACgD,WAAW,cAAAG,oBAAA,uBAAnBA,oBAAA,CAAsBD,OAAO,CAAC,KAAI;oBAAG,GAD/BlD,OAAO,CAACJ,EAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CAAC;kBAAA,CACN,CAAC;gBAAA,GARKyC,OAAO;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASZ,CACL,CAAC;cAAA,eACF,CACH,EAGArB,QAAQ,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,QAAQ,CAAC,iBAC7BzE,OAAA,CAAAE,SAAA;gBAAAwB,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACxB1B,OAAA;oBAAIsE,OAAO,EAAE7D,QAAQ,CAACe,MAAM,GAAG,CAAE;oBAACC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAE9E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACL9B,OAAA;kBAAIyB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACtC1B,OAAA;oBAAIyB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBrB,OAAA;oBAAqByB,SAAS,EAAC,KAAK;oBAAAC,QAAA,EACjCL,OAAO,CAACoD,QAAQ,gBACfzE,OAAA;sBAAIyB,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC5CL,OAAO,CAACoD,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC1D,GAAG,CAAC,CAAC2D,OAAO,EAAEC,KAAK,kBAC/C5E,OAAA;wBAAgByB,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACpD1B,OAAA,CAACP,SAAS;0BAACgC,SAAS,EAAC;wBAA6C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACrE9B,OAAA;0BAAA0B,QAAA,EAAOiD;wBAAO;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAFf8C,KAAK;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGV,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,gBAEL9B,OAAA;sBAAMyB,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACxC,GAZMT,OAAO,CAACJ,EAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAaf,CACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,eACL,CACH,eAGD9B,OAAA;gBAAIyB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtC1B,OAAA;kBAAIyB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrErB,QAAQ,CAACO,GAAG,CAACK,OAAO,iBACnBrB,OAAA;kBAAqByB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACvDL,OAAO,CAACwD,QAAQ,IAAI;gBAAG,GADjBxD,OAAO,CAACJ,EAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9B,OAAA;QAAKyB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B1B,OAAA,CAACZ,IAAI;UACH2C,EAAE,EAAC,YAAY;UACfN,SAAS,EAAC,oIAAoI;UAAAC,QAAA,eAE9I1B,OAAA;YAAA0B,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAvRID,mBAAmB;EAAA,QACAhB,eAAe,EACXS,WAAW,EACdC,QAAQ,EACVC,OAAO;AAAA;AAAAgF,EAAA,GAJzB3E,mBAAmB;AAyRzB,eAAeA,mBAAmB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}