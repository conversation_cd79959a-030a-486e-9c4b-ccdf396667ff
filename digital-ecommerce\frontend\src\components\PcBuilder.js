import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CpuChipIcon,
  ComputerDesktopIcon,
  CircleStackIcon,
  BoltIcon,
  CubeIcon,
  FanIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ShoppingCartIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';

const PcBuilder = () => {
  const { getPcGamingProducts, checkCompatibility, calculateBundlePrice } = useProducts();
  const { showSuccess, showError, showWarning } = useToast();
  
  const [selectedComponents, setSelectedComponents] = useState({
    cpu: null,
    gpu: null,
    motherboard: null,
    memory: null,
    storage: null,
    psu: null,
    case: null,
    cooling: null
  });
  
  const [activeCategory, setActiveCategory] = useState('cpu');
  const [compatibilityCheck, setCompatibilityCheck] = useState({ compatible: true, issues: [] });
  const [totalPrice, setTotalPrice] = useState(0);
  const [buildName, setBuildName] = useState('My Gaming PC');

  const componentCategories = [
    { id: 'cpu', name: 'Processor', icon: CpuChipIcon, required: true },
    { id: 'gpu', name: 'Graphics Card', icon: ComputerDesktopIcon, required: true },
    { id: 'motherboard', name: 'Motherboard', icon: CircleStackIcon, required: true },
    { id: 'memory', name: 'Memory (RAM)', icon: CircleStackIcon, required: true },
    { id: 'storage', name: 'Storage', icon: CircleStackIcon, required: true },
    { id: 'psu', name: 'Power Supply', icon: BoltIcon, required: true },
    { id: 'case', name: 'PC Case', icon: CubeIcon, required: false },
    { id: 'cooling', name: 'Cooling', icon: FanIcon, required: false }
  ];

  // Get components for the active category
  const availableComponents = getPcGamingProducts('pc-component', activeCategory);

  useEffect(() => {
    // Calculate total price
    const components = Object.values(selectedComponents).filter(Boolean);
    const price = components.reduce((sum, component) => sum + component.price, 0);
    setTotalPrice(price);

    // Check compatibility
    if (components.length > 1) {
      const compatibility = checkCompatibility(components);
      setCompatibilityCheck(compatibility);
    }
  }, [selectedComponents, checkCompatibility]);

  const handleComponentSelect = (component) => {
    setSelectedComponents(prev => ({
      ...prev,
      [activeCategory]: component
    }));
    
    showSuccess('Component Added', `${component.name} added to your build.`);
    
    // Auto-advance to next category
    const currentIndex = componentCategories.findIndex(cat => cat.id === activeCategory);
    if (currentIndex < componentCategories.length - 1) {
      setActiveCategory(componentCategories[currentIndex + 1].id);
    }
  };

  const removeComponent = (categoryId) => {
    setSelectedComponents(prev => ({
      ...prev,
      [categoryId]: null
    }));
    showWarning('Component Removed', 'Component removed from your build.');
  };

  const saveBuild = () => {
    const components = Object.values(selectedComponents).filter(Boolean);
    if (components.length === 0) {
      showError('Empty Build', 'Please add some components to save your build.');
      return;
    }

    // Save to localStorage (in a real app, this would be saved to a backend)
    const builds = JSON.parse(localStorage.getItem('savedBuilds') || '[]');
    const newBuild = {
      id: Date.now(),
      name: buildName,
      components: selectedComponents,
      totalPrice,
      createdAt: new Date().toISOString()
    };
    
    builds.push(newBuild);
    localStorage.setItem('savedBuilds', JSON.stringify(builds));
    
    showSuccess('Build Saved', `${buildName} has been saved to your builds.`);
  };

  const addToCart = () => {
    const components = Object.values(selectedComponents).filter(Boolean);
    if (components.length === 0) {
      showError('Empty Build', 'Please add some components to add to cart.');
      return;
    }

    // In a real app, this would add all components to the cart
    showSuccess('Added to Cart', `All ${components.length} components added to cart.`);
  };

  const ComponentCard = ({ component }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => handleComponentSelect(component)}
      className="bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:border-light-orange-300 hover:shadow-md transition-all"
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-semibold text-gray-900 text-sm line-clamp-2">
          {component.name}
        </h3>
        <span className="text-lg font-bold text-light-orange-600 ml-2">
          ${component.price}
        </span>
      </div>
      
      <div className="flex items-center space-x-2 mb-2">
        <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
            <svg
              key={i}
              className={`w-3 h-3 ${
                i < Math.floor(component.rating) ? 'text-yellow-400' : 'text-gray-300'
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
          <span className="text-xs text-gray-600 ml-1">({component.reviews})</span>
        </div>
      </div>

      <p className="text-xs text-gray-600 line-clamp-2 mb-3">
        {component.description}
      </p>

      {component.specifications && (
        <div className="space-y-1">
          {Object.entries(component.specifications).slice(0, 2).map(([key, value]) => (
            <div key={key} className="flex justify-between text-xs">
              <span className="text-gray-500">{key}:</span>
              <span className="text-gray-700 font-medium">{value}</span>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-light-orange-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">PC Builder Tool</h1>
          <p className="text-gray-600">Build your perfect gaming PC with compatibility checking</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Build Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">Your Build</h2>
                <input
                  type="text"
                  value={buildName}
                  onChange={(e) => setBuildName(e.target.value)}
                  className="text-sm border border-gray-300 rounded px-2 py-1 w-32"
                />
              </div>

              {/* Compatibility Status */}
              <div className={`flex items-center space-x-2 p-3 rounded-lg mb-4 ${
                compatibilityCheck.compatible 
                  ? 'bg-green-50 text-green-800' 
                  : 'bg-red-50 text-red-800'
              }`}>
                {compatibilityCheck.compatible ? (
                  <CheckCircleIcon className="w-5 h-5" />
                ) : (
                  <ExclamationTriangleIcon className="w-5 h-5" />
                )}
                <span className="text-sm font-medium">
                  {compatibilityCheck.compatible ? 'All Compatible' : 'Compatibility Issues'}
                </span>
              </div>

              {/* Compatibility Issues */}
              {!compatibilityCheck.compatible && (
                <div className="mb-4">
                  {compatibilityCheck.issues.map((issue, index) => (
                    <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded mb-2">
                      {issue}
                    </div>
                  ))}
                </div>
              )}

              {/* Component List */}
              <div className="space-y-3 mb-6">
                {componentCategories.map(category => {
                  const component = selectedComponents[category.id];
                  const Icon = category.icon;
                  
                  return (
                    <div
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${
                        activeCategory === category.id
                          ? 'border-light-orange-300 bg-light-orange-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="w-5 h-5 text-gray-600" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {category.name}
                            {category.required && <span className="text-red-500 ml-1">*</span>}
                          </p>
                          {component ? (
                            <p className="text-xs text-gray-600 truncate max-w-32">
                              {component.name}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Not selected</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {component && (
                          <>
                            <span className="text-sm font-medium text-gray-900">
                              ${component.price}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removeComponent(category.id);
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              ×
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Total Price */}
              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-gray-900">Total:</span>
                  <span className="text-2xl font-bold text-light-orange-600">
                    ${totalPrice.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={addToCart}
                  disabled={totalPrice === 0}
                  className="w-full bg-light-orange-500 text-white py-3 rounded-lg hover:bg-light-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  <ShoppingCartIcon className="w-5 h-5" />
                  <span>Add All to Cart</span>
                </button>
                
                <button
                  onClick={saveBuild}
                  disabled={totalPrice === 0}
                  className="w-full border border-light-orange-500 text-light-orange-600 py-3 rounded-lg hover:bg-light-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  <BookmarkIcon className="w-5 h-5" />
                  <span>Save Build</span>
                </button>
              </div>
            </div>
          </div>

          {/* Component Selection */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                Select {componentCategories.find(cat => cat.id === activeCategory)?.name}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AnimatePresence>
                  {availableComponents.map(component => (
                    <ComponentCard key={component.id} component={component} />
                  ))}
                </AnimatePresence>
              </div>

              {availableComponents.length === 0 && (
                <div className="text-center py-8">
                  <CpuChipIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No components available for this category</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PcBuilder;
