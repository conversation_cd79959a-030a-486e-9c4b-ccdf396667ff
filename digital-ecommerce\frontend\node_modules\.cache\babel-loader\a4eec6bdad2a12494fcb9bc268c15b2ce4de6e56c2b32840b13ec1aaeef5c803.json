{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\AddProductModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PhotoIcon, PlusIcon, TrashIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';\nimport { categories } from '../data/products';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddProductModal = ({\n  isOpen,\n  onClose,\n  onSubmit\n}) => {\n  _s();\n  var _steps;\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    shortDescription: '',\n    price: '',\n    discountPrice: '',\n    currency: 'USD',\n    category: '',\n    subcategory: '',\n    type: 'physical',\n    stockCount: '',\n    sku: '',\n    tags: [],\n    keywords: '',\n    isActive: true,\n    isFeatured: false,\n    specifications: {},\n    images: [],\n    // PC Gaming specific fields\n    componentType: '',\n    pcType: '',\n    compatibility: {},\n    performance: {},\n    features: [],\n    warranty: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const [newTag, setNewTag] = useState('');\n\n  // Auto-generate SKU when product name changes\n  useEffect(() => {\n    if (formData.name && !formData.sku) {\n      const sku = formData.name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 8) + '-' + Date.now().toString().slice(-4);\n      setFormData(prev => ({\n        ...prev,\n        sku\n      }));\n    }\n  }, [formData.name]);\n  const steps = [{\n    id: 1,\n    name: 'Basic Info',\n    description: 'Product name, description, and category'\n  }, {\n    id: 2,\n    name: 'Pricing',\n    description: 'Price, discounts, and currency'\n  }, {\n    id: 3,\n    name: 'Images',\n    description: 'Product photos and media'\n  }, {\n    id: 4,\n    name: 'Details',\n    description: 'Stock, SKU, and specifications'\n  }, {\n    id: 5,\n    name: 'Settings',\n    description: 'Tags, keywords, and publication'\n  }];\n  const validateStep = step => {\n    const newErrors = {};\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Product name is required';\n        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';\n        if (!formData.description.trim()) newErrors.description = 'Description is required';\n        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';\n        if (!formData.category) newErrors.category = 'Category is required';\n        break;\n      case 2:\n        if (!formData.price) newErrors.price = 'Price is required';\n        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';\n        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {\n          newErrors.discountPrice = 'Discount price must be a positive number';\n        }\n        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {\n          newErrors.discountPrice = 'Discount price must be less than regular price';\n        }\n        break;\n      case 3:\n        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';\n        break;\n      case 4:\n        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {\n          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';\n        }\n        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';\n        break;\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleNext = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    }\n  };\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const handleImageUpload = files => {\n    const newImages = Array.from(files).map(file => ({\n      id: Date.now() + Math.random(),\n      file,\n      url: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size\n    }));\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...newImages]\n    }));\n  };\n  const handleDrag = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageUpload(e.dataTransfer.files);\n    }\n  };\n  const removeImage = imageId => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(img => img.id !== imageId)\n    }));\n  };\n  const moveImage = (fromIndex, toIndex) => {\n    const newImages = [...formData.images];\n    const [removed] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, removed);\n    setFormData(prev => ({\n      ...prev,\n      images: newImages\n    }));\n  };\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n  const removeTag = tagToRemove => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) return;\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n      // Reset form only if submission was successful\n      setFormData({\n        name: '',\n        description: '',\n        shortDescription: '',\n        price: '',\n        discountPrice: '',\n        currency: 'USD',\n        category: '',\n        subcategory: '',\n        type: 'physical',\n        stockCount: '',\n        sku: '',\n        tags: [],\n        keywords: '',\n        isActive: true,\n        isFeatured: false,\n        specifications: {},\n        images: []\n      });\n      setCurrentStep(1);\n      setErrors({});\n    } catch (error) {\n      // Error handling is now done in the parent component\n      console.error('Error creating product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const selectedCategory = categories.find(cat => cat.id === formData.category);\n  const renderStepContent = () => {\n    var _selectedCategory$sub, _categories$find;\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.name ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Enter product name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Short Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.shortDescription,\n              onChange: e => handleInputChange('shortDescription', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n              placeholder: \"Brief product description\",\n              maxLength: 150\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [formData.shortDescription.length, \"/150 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Description *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description,\n              onChange: e => handleInputChange('description', e.target.value),\n              rows: 4,\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.description ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Detailed product description\",\n              maxLength: 2000\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 38\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: [formData.description.length, \"/2000 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.category,\n                onChange: e => handleInputChange('category', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.category ? 'border-red-500' : 'border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Subcategory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.subcategory,\n                onChange: e => handleInputChange('subcategory', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                disabled: !(selectedCategory !== null && selectedCategory !== void 0 && selectedCategory.subcategories),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a subcategory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), selectedCategory === null || selectedCategory === void 0 ? void 0 : (_selectedCategory$sub = selectedCategory.subcategories) === null || _selectedCategory$sub === void 0 ? void 0 : _selectedCategory$sub.map(sub => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: sub,\n                  children: sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                }, sub, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  value: \"physical\",\n                  checked: formData.type === 'physical',\n                  onChange: e => handleInputChange('type', e.target.value),\n                  className: \"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this), \"Physical Product\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  value: \"digital\",\n                  checked: formData.type === 'digital',\n                  onChange: e => handleInputChange('type', e.target.value),\n                  className: \"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), \"Digital Product\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Price * (\", formData.currency, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                min: \"0\",\n                value: formData.price,\n                onChange: e => handleInputChange('price', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.price ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"0.00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), errors.price && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.currency,\n                onChange: e => handleInputChange('currency', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"USD\",\n                  children: \"USD ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"EUR\",\n                  children: \"EUR (\\u20AC)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"GBP\",\n                  children: \"GBP (\\xA3)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"CAD\",\n                  children: \"CAD (C$)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: [\"Discount Price (\", formData.currency, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              min: \"0\",\n              value: formData.discountPrice,\n              onChange: e => handleInputChange('discountPrice', e.target.value),\n              className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.discountPrice ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"0.00 (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), errors.discountPrice && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.discountPrice\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 40\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: \"Leave empty if no discount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), formData.price && formData.discountPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-800\",\n                children: \"Discount Amount:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-green-800\",\n                children: [formData.currency, \" \", (parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-800\",\n                children: \"Discount Percentage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-green-800\",\n                children: [((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price) * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Images *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive ? 'border-light-orange-500 bg-light-orange-50' : errors.images ? 'border-red-500 bg-red-50' : 'border-gray-300 hover:border-light-orange-400'}`,\n              onDragEnter: handleDrag,\n              onDragLeave: handleDrag,\n              onDragOver: handleDrag,\n              onDrop: handleDrop,\n              children: [/*#__PURE__*/_jsxDEV(ArrowUpTrayIcon, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"file-upload\",\n                  className: \"cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-sm font-medium text-gray-900\",\n                    children: \"Drop images here or click to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-1 block text-xs text-gray-500\",\n                    children: \"PNG, JPG, GIF up to 10MB each\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"file-upload\",\n                  name: \"file-upload\",\n                  type: \"file\",\n                  className: \"sr-only\",\n                  multiple: true,\n                  accept: \"image/*\",\n                  onChange: e => handleImageUpload(e.target.files)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), errors.images && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.images\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), formData.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-700 mb-3\",\n              children: [\"Uploaded Images (\", formData.images.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n              children: formData.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: image.url,\n                  alt: image.name,\n                  className: \"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => removeImage(image.id),\n                    className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 23\n                }, this), index === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\",\n                  children: \"Main\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 25\n                }, this)]\n              }, image.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-xs text-gray-500\",\n              children: \"The first image will be used as the main product image. Drag to reorder.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"SKU (Stock Keeping Unit) *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.sku,\n                onChange: e => handleInputChange('sku', e.target.value.toUpperCase()),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.sku ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"AUTO-GENERATED\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this), errors.sku && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.sku\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 32\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"Unique identifier for this product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), formData.type === 'physical' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Stock Quantity *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"0\",\n                value: formData.stockCount,\n                onChange: e => handleInputChange('stockCount', e.target.value),\n                className: `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${errors.stockCount ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this), errors.stockCount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.stockCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Specifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Specification name (e.g., Weight)\",\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Value (e.g., 1.5 kg)\",\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\",\n                children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add Specification\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), formData.category === 'pc-gaming' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 border-t border-gray-200 pt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"PC Gaming Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this), formData.subcategory === 'pc-component' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Component Type *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.componentType,\n                onChange: e => handleInputChange('componentType', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Component Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cpu\",\n                  children: \"CPU/Processor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"gpu\",\n                  children: \"Graphics Card\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"motherboard\",\n                  children: \"Motherboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"memory\",\n                  children: \"Memory/RAM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"storage\",\n                  children: \"Storage\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"psu\",\n                  children: \"Power Supply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"case\",\n                  children: \"PC Case\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cooling\",\n                  children: \"Cooling\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 19\n            }, this), formData.subcategory === 'pre-built-pc' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"PC Type *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.pcType,\n                onChange: e => handleInputChange('pcType', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select PC Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"entry-gaming\",\n                  children: \"Entry Gaming ($800-1200)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"mid-range-gaming\",\n                  children: \"Mid-Range Gaming ($1200-2000)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high-end-gaming\",\n                  children: \"High-End Gaming ($2000+)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"workstation\",\n                  children: \"Workstation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Warranty Period\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.warranty,\n                onChange: e => handleInputChange('warranty', e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                placeholder: \"e.g., 3 years manufacturer warranty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this);\n      case 5:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Tags\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2 mb-3\",\n              children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\",\n                children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeTag(tag),\n                  className: \"ml-2 text-light-orange-600 hover:text-light-orange-800\",\n                  children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newTag,\n                onChange: e => setNewTag(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && (e.preventDefault(), addTag()),\n                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n                placeholder: \"Add a tag\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addTag,\n                className: \"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\",\n                children: \"Add\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Keywords (for search)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.keywords,\n              onChange: e => handleInputChange('keywords', e.target.value),\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",\n              placeholder: \"Enter keywords separated by commas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500\",\n              children: \"Help customers find this product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"isActive\",\n                checked: formData.isActive,\n                onChange: e => handleInputChange('isActive', e.target.checked),\n                className: \"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"isActive\",\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Publish product (make it visible to customers)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"isFeatured\",\n                checked: formData.isFeatured,\n                onChange: e => handleInputChange('isFeatured', e.target.checked),\n                className: \"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"isFeatured\",\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Feature this product (show in featured sections)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-800 mb-2\",\n              children: \"Product Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-700 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 20\n                }, this), \" \", formData.name || 'Not set']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Price:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 20\n                }, this), \" \", formData.currency, \" \", formData.price || '0.00']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Category:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 20\n                }, this), \" \", ((_categories$find = categories.find(c => c.id === formData.category)) === null || _categories$find === void 0 ? void 0 : _categories$find.name) || 'Not set']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 20\n                }, this), \" \", formData.type]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Images:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 20\n                }, this), \" \", formData.images.length, \" uploaded\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 20\n                }, this), \" \", formData.isActive ? 'Active' : 'Draft']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Step content for step \", currentStep]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      exit: {\n        opacity: 0\n      },\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0.9,\n          opacity: 0\n        },\n        onClick: e => e.stopPropagation(),\n        className: \"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Add New Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Step \", currentStep, \" of \", steps.length, \": \", (_steps = steps[currentStep - 1]) === null || _steps === void 0 ? void 0 : _steps.description]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep > step.id ? 'bg-green-500 text-white' : currentStep === step.id ? 'bg-light-orange-500 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: currentStep > step.id ? '✓' : step.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `ml-2 text-sm font-medium ${currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'}`,\n                children: step.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 19\n              }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-0.5 mx-4 ${currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 max-h-96 overflow-y-auto\",\n          children: renderStepContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handlePrev,\n            disabled: currentStep === 1,\n            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 15\n            }, this), currentStep < steps.length ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleNext,\n              className: \"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\",\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              disabled: isSubmitting,\n              className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: isSubmitting ? 'Creating...' : 'Create Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 751,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 743,\n    columnNumber: 5\n  }, this);\n};\n_s(AddProductModal, \"1MBGGLc2EaYzXQ8ECJQKtlLxqII=\");\n_c = AddProductModal;\nexport default AddProductModal;\nvar _c;\n$RefreshReg$(_c, \"AddProductModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PhotoIcon", "PlusIcon", "TrashIcon", "ArrowUpTrayIcon", "categories", "jsxDEV", "_jsxDEV", "AddProductModal", "isOpen", "onClose", "onSubmit", "_s", "_steps", "currentStep", "setCurrentStep", "formData", "setFormData", "name", "description", "shortDescription", "price", "discountPrice", "currency", "category", "subcategory", "type", "stockCount", "sku", "tags", "keywords", "isActive", "isFeatured", "specifications", "images", "componentType", "pcType", "compatibility", "performance", "features", "warranty", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "dragActive", "setDragActive", "newTag", "setNewTag", "toUpperCase", "replace", "substring", "Date", "now", "toString", "slice", "prev", "steps", "id", "validateStep", "step", "newErrors", "trim", "length", "isNaN", "parseFloat", "parseInt", "Object", "keys", "handleNext", "Math", "min", "handlePrev", "max", "handleInputChange", "field", "value", "handleImageUpload", "files", "newImages", "Array", "from", "map", "file", "random", "url", "URL", "createObjectURL", "size", "handleDrag", "e", "preventDefault", "stopPropagation", "handleDrop", "dataTransfer", "removeImage", "imageId", "filter", "img", "moveImage", "fromIndex", "toIndex", "removed", "splice", "addTag", "includes", "removeTag", "tagToRemove", "tag", "handleSubmit", "error", "console", "selectedCate<PERSON><PERSON>", "find", "cat", "renderStepContent", "_selectedCategory$sub", "_categories$find", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "rows", "disabled", "subcategories", "sub", "l", "checked", "toFixed", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "htmlFor", "multiple", "accept", "image", "index", "src", "alt", "onClick", "onKeyPress", "key", "c", "div", "initial", "opacity", "animate", "exit", "scale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AddProductModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  XMarkIcon,\n  PhotoIcon,\n  PlusIcon,\n  TrashIcon,\n  ArrowUpTrayIcon\n} from '@heroicons/react/24/outline';\nimport { categories } from '../data/products';\n\nconst AddProductModal = ({ isOpen, onClose, onSubmit }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    shortDescription: '',\n    price: '',\n    discountPrice: '',\n    currency: 'USD',\n    category: '',\n    subcategory: '',\n    type: 'physical',\n    stockCount: '',\n    sku: '',\n    tags: [],\n    keywords: '',\n    isActive: true,\n    isFeatured: false,\n    specifications: {},\n    images: [],\n    // PC Gaming specific fields\n    componentType: '',\n    pcType: '',\n    compatibility: {},\n    performance: {},\n    features: [],\n    warranty: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const [newTag, setNewTag] = useState('');\n\n  // Auto-generate SKU when product name changes\n  useEffect(() => {\n    if (formData.name && !formData.sku) {\n      const sku = formData.name\n        .toUpperCase()\n        .replace(/[^A-Z0-9]/g, '')\n        .substring(0, 8) + '-' + Date.now().toString().slice(-4);\n      setFormData(prev => ({ ...prev, sku }));\n    }\n  }, [formData.name]);\n\n  const steps = [\n    { id: 1, name: 'Basic Info', description: 'Product name, description, and category' },\n    { id: 2, name: 'Pricing', description: 'Price, discounts, and currency' },\n    { id: 3, name: 'Images', description: 'Product photos and media' },\n    { id: 4, name: 'Details', description: 'Stock, SKU, and specifications' },\n    { id: 5, name: 'Settings', description: 'Tags, keywords, and publication' }\n  ];\n\n  const validateStep = (step) => {\n    const newErrors = {};\n\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Product name is required';\n        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';\n        if (!formData.description.trim()) newErrors.description = 'Description is required';\n        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';\n        if (!formData.category) newErrors.category = 'Category is required';\n        break;\n      case 2:\n        if (!formData.price) newErrors.price = 'Price is required';\n        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';\n        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {\n          newErrors.discountPrice = 'Discount price must be a positive number';\n        }\n        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {\n          newErrors.discountPrice = 'Discount price must be less than regular price';\n        }\n        break;\n      case 3:\n        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';\n        break;\n      case 4:\n        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {\n          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';\n        }\n        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';\n        break;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    }\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const handleImageUpload = (files) => {\n    const newImages = Array.from(files).map(file => ({\n      id: Date.now() + Math.random(),\n      file,\n      url: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size\n    }));\n\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...newImages]\n    }));\n  };\n\n  const handleDrag = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageUpload(e.dataTransfer.files);\n    }\n  };\n\n  const removeImage = (imageId) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(img => img.id !== imageId)\n    }));\n  };\n\n  const moveImage = (fromIndex, toIndex) => {\n    const newImages = [...formData.images];\n    const [removed] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, removed);\n    setFormData(prev => ({ ...prev, images: newImages }));\n  };\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n\n  const removeTag = (tagToRemove) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) return;\n\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n      // Reset form only if submission was successful\n      setFormData({\n        name: '',\n        description: '',\n        shortDescription: '',\n        price: '',\n        discountPrice: '',\n        currency: 'USD',\n        category: '',\n        subcategory: '',\n        type: 'physical',\n        stockCount: '',\n        sku: '',\n        tags: [],\n        keywords: '',\n        isActive: true,\n        isFeatured: false,\n        specifications: {},\n        images: []\n      });\n      setCurrentStep(1);\n      setErrors({});\n    } catch (error) {\n      // Error handling is now done in the parent component\n      console.error('Error creating product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const selectedCategory = categories.find(cat => cat.id === formData.category);\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.name ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter product name\"\n              />\n              {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Short Description\n              </label>\n              <input\n                type=\"text\"\n                value={formData.shortDescription}\n                onChange={(e) => handleInputChange('shortDescription', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Brief product description\"\n                maxLength={150}\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.shortDescription.length}/150 characters</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.description ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Detailed product description\"\n                maxLength={2000}\n              />\n              {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.description.length}/2000 characters</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => handleInputChange('category', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.category ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">Select a category</option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"mt-1 text-sm text-red-600\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subcategory\n                </label>\n                <select\n                  value={formData.subcategory}\n                  onChange={(e) => handleInputChange('subcategory', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  disabled={!selectedCategory?.subcategories}\n                >\n                  <option value=\"\">Select a subcategory</option>\n                  {selectedCategory?.subcategories?.map(sub => (\n                    <option key={sub} value={sub}>\n                      {sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Type\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"physical\"\n                    checked={formData.type === 'physical'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Physical Product\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"digital\"\n                    checked={formData.type === 'digital'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Digital Product\n                </label>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Price * ({formData.currency})\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  value={formData.price}\n                  onChange={(e) => handleInputChange('price', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.price ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"0.00\"\n                />\n                {errors.price && <p className=\"mt-1 text-sm text-red-600\">{errors.price}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Currency\n                </label>\n                <select\n                  value={formData.currency}\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                >\n                  <option value=\"USD\">USD ($)</option>\n                  <option value=\"EUR\">EUR (€)</option>\n                  <option value=\"GBP\">GBP (£)</option>\n                  <option value=\"CAD\">CAD (C$)</option>\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Discount Price ({formData.currency})\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.discountPrice}\n                onChange={(e) => handleInputChange('discountPrice', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.discountPrice ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00 (optional)\"\n              />\n              {errors.discountPrice && <p className=\"mt-1 text-sm text-red-600\">{errors.discountPrice}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">Leave empty if no discount</p>\n            </div>\n\n            {formData.price && formData.discountPrice && (\n              <div className=\"p-4 bg-green-50 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Amount:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {formData.currency} {(parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between mt-1\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Percentage:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {(((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price)) * 100).toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Images *\n              </label>\n              <div\n                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n                  dragActive\n                    ? 'border-light-orange-500 bg-light-orange-50'\n                    : errors.images\n                      ? 'border-red-500 bg-red-50'\n                      : 'border-gray-300 hover:border-light-orange-400'\n                }`}\n                onDragEnter={handleDrag}\n                onDragLeave={handleDrag}\n                onDragOver={handleDrag}\n                onDrop={handleDrop}\n              >\n                <ArrowUpTrayIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <div className=\"mt-4\">\n                  <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n                    <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                      Drop images here or click to upload\n                    </span>\n                    <span className=\"mt-1 block text-xs text-gray-500\">\n                      PNG, JPG, GIF up to 10MB each\n                    </span>\n                  </label>\n                  <input\n                    id=\"file-upload\"\n                    name=\"file-upload\"\n                    type=\"file\"\n                    className=\"sr-only\"\n                    multiple\n                    accept=\"image/*\"\n                    onChange={(e) => handleImageUpload(e.target.files)}\n                  />\n                </div>\n              </div>\n              {errors.images && <p className=\"mt-1 text-sm text-red-600\">{errors.images}</p>}\n            </div>\n\n            {formData.images.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-700 mb-3\">\n                  Uploaded Images ({formData.images.length})\n                </h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                  {formData.images.map((image, index) => (\n                    <div key={image.id} className=\"relative group\">\n                      <img\n                        src={image.url}\n                        alt={image.name}\n                        className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                      />\n                      <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\">\n                        <button\n                          onClick={() => removeImage(image.id)}\n                          className=\"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                      {index === 0 && (\n                        <div className=\"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\">\n                          Main\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n                <p className=\"mt-2 text-xs text-gray-500\">\n                  The first image will be used as the main product image. Drag to reorder.\n                </p>\n              </div>\n            )}\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  SKU (Stock Keeping Unit) *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.sku}\n                  onChange={(e) => handleInputChange('sku', e.target.value.toUpperCase())}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.sku ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"AUTO-GENERATED\"\n                />\n                {errors.sku && <p className=\"mt-1 text-sm text-red-600\">{errors.sku}</p>}\n                <p className=\"mt-1 text-xs text-gray-500\">Unique identifier for this product</p>\n              </div>\n\n              {formData.type === 'physical' && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Stock Quantity *\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    value={formData.stockCount}\n                    onChange={(e) => handleInputChange('stockCount', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                      errors.stockCount ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder=\"0\"\n                  />\n                  {errors.stockCount && <p className=\"mt-1 text-sm text-red-600\">{errors.stockCount}</p>}\n                </div>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Specifications\n              </label>\n              <div className=\"space-y-3\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Specification name (e.g., Weight)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Value (e.g., 1.5 kg)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                </div>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\"\n                >\n                  <PlusIcon className=\"w-4 h-4\" />\n                  <span>Add Specification</span>\n                </button>\n              </div>\n            </div>\n\n            {/* PC Gaming Specific Fields */}\n            {formData.category === 'pc-gaming' && (\n              <div className=\"space-y-4 border-t border-gray-200 pt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900\">PC Gaming Details</h3>\n\n                {formData.subcategory === 'pc-component' && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Component Type *\n                    </label>\n                    <select\n                      value={formData.componentType}\n                      onChange={(e) => handleInputChange('componentType', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                    >\n                      <option value=\"\">Select Component Type</option>\n                      <option value=\"cpu\">CPU/Processor</option>\n                      <option value=\"gpu\">Graphics Card</option>\n                      <option value=\"motherboard\">Motherboard</option>\n                      <option value=\"memory\">Memory/RAM</option>\n                      <option value=\"storage\">Storage</option>\n                      <option value=\"psu\">Power Supply</option>\n                      <option value=\"case\">PC Case</option>\n                      <option value=\"cooling\">Cooling</option>\n                    </select>\n                  </div>\n                )}\n\n                {formData.subcategory === 'pre-built-pc' && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      PC Type *\n                    </label>\n                    <select\n                      value={formData.pcType}\n                      onChange={(e) => handleInputChange('pcType', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                    >\n                      <option value=\"\">Select PC Type</option>\n                      <option value=\"entry-gaming\">Entry Gaming ($800-1200)</option>\n                      <option value=\"mid-range-gaming\">Mid-Range Gaming ($1200-2000)</option>\n                      <option value=\"high-end-gaming\">High-End Gaming ($2000+)</option>\n                      <option value=\"workstation\">Workstation</option>\n                    </select>\n                  </div>\n                )}\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Warranty Period\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.warranty}\n                    onChange={(e) => handleInputChange('warranty', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                    placeholder=\"e.g., 3 years manufacturer warranty\"\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Tags\n              </label>\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {formData.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\"\n                  >\n                    {tag}\n                    <button\n                      onClick={() => removeTag(tag)}\n                      className=\"ml-2 text-light-orange-600 hover:text-light-orange-800\"\n                    >\n                      <XMarkIcon className=\"w-3 h-3\" />\n                    </button>\n                  </span>\n                ))}\n              </div>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  placeholder=\"Add a tag\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addTag}\n                  className=\"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\"\n                >\n                  Add\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Keywords (for search)\n              </label>\n              <textarea\n                value={formData.keywords}\n                onChange={(e) => handleInputChange('keywords', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Enter keywords separated by commas\"\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">Help customers find this product</p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isActive\"\n                  checked={formData.isActive}\n                  onChange={(e) => handleInputChange('isActive', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isActive\" className=\"text-sm font-medium text-gray-700\">\n                  Publish product (make it visible to customers)\n                </label>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isFeatured\"\n                  checked={formData.isFeatured}\n                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isFeatured\" className=\"text-sm font-medium text-gray-700\">\n                  Feature this product (show in featured sections)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"p-4 bg-blue-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-800 mb-2\">Product Summary</h4>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p><strong>Name:</strong> {formData.name || 'Not set'}</p>\n                <p><strong>Price:</strong> {formData.currency} {formData.price || '0.00'}</p>\n                <p><strong>Category:</strong> {categories.find(c => c.id === formData.category)?.name || 'Not set'}</p>\n                <p><strong>Type:</strong> {formData.type}</p>\n                <p><strong>Images:</strong> {formData.images.length} uploaded</p>\n                <p><strong>Status:</strong> {formData.isActive ? 'Active' : 'Draft'}</p>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step content for step {currentStep}</div>;\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          onClick={(e) => e.stopPropagation()}\n          className=\"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\"\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">Add New Product</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Progress Steps */}\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                    currentStep > step.id\n                      ? 'bg-green-500 text-white'\n                      : currentStep === step.id\n                        ? 'bg-light-orange-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {currentStep > step.id ? '✓' : step.id}\n                  </div>\n                  <span className={`ml-2 text-sm font-medium ${\n                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'\n                  }`}>\n                    {step.name}\n                  </span>\n                  {index < steps.length - 1 && (\n                    <div className={`w-12 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 max-h-96 overflow-y-auto\">\n            {renderStepContent()}\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\">\n            <button\n              onClick={handlePrev}\n              disabled={currentStep === 1}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              Previous\n            </button>\n\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              \n              {currentStep < steps.length ? (\n                <button\n                  onClick={handleNext}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\"\n                >\n                  Next\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? 'Creating...' : 'Create Product'}\n                </button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default AddProductModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,eAAe,QACV,6BAA6B;AACpC,SAASC,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,MAAA;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,UAAU,EAAE,EAAE;IACdC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,CAAC,CAAC;IAClBC,MAAM,EAAE,EAAE;IACV;IACAC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,CAAC,CAAC;IACjBC,WAAW,EAAE,CAAC,CAAC;IACfC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACY,GAAG,EAAE;MAClC,MAAMA,GAAG,GAAGZ,QAAQ,CAACE,IAAI,CACtB+B,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1DtC,WAAW,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5B;MAAI,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACZ,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEnB,MAAMuC,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAExC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAE;EAA0C,CAAC,EACrF;IAAEuC,EAAE,EAAE,CAAC;IAAExC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAiC,CAAC,EACzE;IAAEuC,EAAE,EAAE,CAAC;IAAExC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAClE;IAAEuC,EAAE,EAAE,CAAC;IAAExC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAiC,CAAC,EACzE;IAAEuC,EAAE,EAAE,CAAC;IAAExC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAAkC,CAAC,CAC5E;EAED,MAAMwC,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,QAAQD,IAAI;MACV,KAAK,CAAC;QACJ,IAAI,CAAC5C,QAAQ,CAACE,IAAI,CAAC4C,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC3C,IAAI,GAAG,0BAA0B;QACtE,IAAIF,QAAQ,CAACE,IAAI,CAAC6C,MAAM,GAAG,GAAG,EAAEF,SAAS,CAAC3C,IAAI,GAAG,+CAA+C;QAChG,IAAI,CAACF,QAAQ,CAACG,WAAW,CAAC2C,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC1C,WAAW,GAAG,yBAAyB;QACnF,IAAIH,QAAQ,CAACG,WAAW,CAAC4C,MAAM,GAAG,IAAI,EAAEF,SAAS,CAAC1C,WAAW,GAAG,+CAA+C;QAC/G,IAAI,CAACH,QAAQ,CAACQ,QAAQ,EAAEqC,SAAS,CAACrC,QAAQ,GAAG,sBAAsB;QACnE;MACF,KAAK,CAAC;QACJ,IAAI,CAACR,QAAQ,CAACK,KAAK,EAAEwC,SAAS,CAACxC,KAAK,GAAG,mBAAmB;QAC1D,IAAI2C,KAAK,CAAChD,QAAQ,CAACK,KAAK,CAAC,IAAI4C,UAAU,CAACjD,QAAQ,CAACK,KAAK,CAAC,IAAI,CAAC,EAAEwC,SAAS,CAACxC,KAAK,GAAG,iCAAiC;QACjH,IAAIL,QAAQ,CAACM,aAAa,KAAK0C,KAAK,CAAChD,QAAQ,CAACM,aAAa,CAAC,IAAI2C,UAAU,CAACjD,QAAQ,CAACM,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;UACxGuC,SAAS,CAACvC,aAAa,GAAG,0CAA0C;QACtE;QACA,IAAIN,QAAQ,CAACM,aAAa,IAAI2C,UAAU,CAACjD,QAAQ,CAACM,aAAa,CAAC,IAAI2C,UAAU,CAACjD,QAAQ,CAACK,KAAK,CAAC,EAAE;UAC9FwC,SAAS,CAACvC,aAAa,GAAG,gDAAgD;QAC5E;QACA;MACF,KAAK,CAAC;QACJ,IAAIN,QAAQ,CAACkB,MAAM,CAAC6B,MAAM,KAAK,CAAC,EAAEF,SAAS,CAAC3B,MAAM,GAAG,wCAAwC;QAC7F;MACF,KAAK,CAAC;QACJ,IAAIlB,QAAQ,CAACU,IAAI,KAAK,UAAU,KAAK,CAACV,QAAQ,CAACW,UAAU,IAAIqC,KAAK,CAAChD,QAAQ,CAACW,UAAU,CAAC,IAAIuC,QAAQ,CAAClD,QAAQ,CAACW,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;UAC7HkC,SAAS,CAAClC,UAAU,GAAG,iEAAiE;QAC1F;QACA,IAAI,CAACX,QAAQ,CAACY,GAAG,CAACkC,IAAI,CAAC,CAAC,EAAED,SAAS,CAACjC,GAAG,GAAG,iBAAiB;QAC3D;IACJ;IAEAc,SAAS,CAACmB,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIV,YAAY,CAAC7C,WAAW,CAAC,EAAE;MAC7BC,cAAc,CAACyC,IAAI,IAAIc,IAAI,CAACC,GAAG,CAACf,IAAI,GAAG,CAAC,EAAEC,KAAK,CAACM,MAAM,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMS,UAAU,GAAGA,CAAA,KAAM;IACvBzD,cAAc,CAACyC,IAAI,IAAIc,IAAI,CAACG,GAAG,CAACjB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C3D,WAAW,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD,IAAInC,MAAM,CAACkC,KAAK,CAAC,EAAE;MACjBjC,SAAS,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAACC,IAAI,KAAK;MAC/CzB,EAAE,EAAEN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGiB,IAAI,CAACc,MAAM,CAAC,CAAC;MAC9BD,IAAI;MACJE,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC9BjE,IAAI,EAAEiE,IAAI,CAACjE,IAAI;MACfsE,IAAI,EAAEL,IAAI,CAACK;IACb,CAAC,CAAC,CAAC;IAEHvE,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,MAAM,EAAE,CAAC,GAAGsB,IAAI,CAACtB,MAAM,EAAE,GAAG6C,SAAS;IACvC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMU,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAAChE,IAAI,KAAK,WAAW,IAAIgE,CAAC,CAAChE,IAAI,KAAK,UAAU,EAAE;MACnDoB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,CAAC,CAAChE,IAAI,KAAK,WAAW,EAAE;MACjCoB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM+C,UAAU,GAAIH,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB9C,aAAa,CAAC,KAAK,CAAC;IAEpB,IAAI4C,CAAC,CAACI,YAAY,CAAChB,KAAK,IAAIY,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC,EAAE;MACnDD,iBAAiB,CAACa,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC;IACzC;EACF,CAAC;EAED,MAAMiB,WAAW,GAAIC,OAAO,IAAK;IAC/B/E,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,MAAM,EAAEsB,IAAI,CAACtB,MAAM,CAAC+D,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxC,EAAE,KAAKsC,OAAO;IACtD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,SAAS,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IACxC,MAAMtB,SAAS,GAAG,CAAC,GAAG/D,QAAQ,CAACkB,MAAM,CAAC;IACtC,MAAM,CAACoE,OAAO,CAAC,GAAGvB,SAAS,CAACwB,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;IAChDrB,SAAS,CAACwB,MAAM,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;IACrCrF,WAAW,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,MAAM,EAAE6C;IAAU,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAMyB,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIzD,MAAM,CAACe,IAAI,CAAC,CAAC,IAAI,CAAC9C,QAAQ,CAACa,IAAI,CAAC4E,QAAQ,CAAC1D,MAAM,CAACe,IAAI,CAAC,CAAC,CAAC,EAAE;MAC3D7C,WAAW,CAACuC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP3B,IAAI,EAAE,CAAC,GAAG2B,IAAI,CAAC3B,IAAI,EAAEkB,MAAM,CAACe,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC;MACHd,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAM0D,SAAS,GAAIC,WAAW,IAAK;IACjC1F,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,IAAI,EAAE2B,IAAI,CAAC3B,IAAI,CAACoE,MAAM,CAACW,GAAG,IAAIA,GAAG,KAAKD,WAAW;IACnD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAClD,YAAY,CAAC7C,WAAW,CAAC,EAAE;IAEhC8B,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMjC,QAAQ,CAACK,QAAQ,CAAC;MACxB;MACAC,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,EAAE;QACpBC,KAAK,EAAE,EAAE;QACTC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,EAAE;QACdC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,CAAC,CAAC;QAClBC,MAAM,EAAE;MACV,CAAC,CAAC;MACFnB,cAAc,CAAC,CAAC,CAAC;MACjB2B,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRlE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoE,gBAAgB,GAAG3G,UAAU,CAAC4G,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACxD,EAAE,KAAK1C,QAAQ,CAACQ,QAAQ,CAAC;EAE7E,MAAM2F,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,gBAAA;IAC9B,QAAQvG,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEP,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACEmB,IAAI,EAAC,MAAM;cACXkD,KAAK,EAAE5D,QAAQ,CAACE,IAAK;cACrB0G,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAC3D0C,SAAS,EAAE,6GACT7E,MAAM,CAACvB,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,EACjD;cACH4G,WAAW,EAAC;YAAoB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACDlF,MAAM,CAACvB,IAAI,iBAAIX,OAAA;cAAG+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACvB;YAAI;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACEmB,IAAI,EAAC,MAAM;cACXkD,KAAK,EAAE5D,QAAQ,CAACI,gBAAiB;cACjCwG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,kBAAkB,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cACvE0C,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC,2BAA2B;cACvCC,SAAS,EAAE;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAEvG,QAAQ,CAACI,gBAAgB,CAAC2C,MAAM,EAAC,iBAAe;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACEqE,KAAK,EAAE5D,QAAQ,CAACG,WAAY;cAC5ByG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,aAAa,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAClEoD,IAAI,EAAE,CAAE;cACRV,SAAS,EAAE,6GACT7E,MAAM,CAACtB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;cACH2G,WAAW,EAAC,8BAA8B;cAC1CC,SAAS,EAAE;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACDlF,MAAM,CAACtB,WAAW,iBAAIZ,OAAA;cAAG+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACtB;YAAW;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAEvG,QAAQ,CAACG,WAAW,CAAC4C,MAAM,EAAC,kBAAgB;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eAENpH,OAAA;YAAK+G,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEqE,KAAK,EAAE5D,QAAQ,CAACQ,QAAS;gBACzBoG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC/D0C,SAAS,EAAE,6GACT7E,MAAM,CAACjB,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;gBAAA+F,QAAA,gBAEHhH,OAAA;kBAAQqE,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CtH,UAAU,CAAC6E,GAAG,CAAC1D,QAAQ,iBACtBjB,OAAA;kBAA0BqE,KAAK,EAAEpD,QAAQ,CAACkC,EAAG;kBAAA6D,QAAA,EAC1C/F,QAAQ,CAACN;gBAAI,GADHM,QAAQ,CAACkC,EAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRlF,MAAM,CAACjB,QAAQ,iBAAIjB,OAAA;gBAAG+G,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACjB;cAAQ;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAENpH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEqE,KAAK,EAAE5D,QAAQ,CAACS,WAAY;gBAC5BmG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,aAAa,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAClE0C,SAAS,EAAC,2HAA2H;gBACrIW,QAAQ,EAAE,EAACjB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkB,aAAa,CAAC;gBAAAX,QAAA,gBAE3ChH,OAAA;kBAAQqE,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CX,gBAAgB,aAAhBA,gBAAgB,wBAAAI,qBAAA,GAAhBJ,gBAAgB,CAAEkB,aAAa,cAAAd,qBAAA,uBAA/BA,qBAAA,CAAiClC,GAAG,CAACiD,GAAG,iBACvC5H,OAAA;kBAAkBqE,KAAK,EAAEuD,GAAI;kBAAAZ,QAAA,EAC1BY,GAAG,CAACjF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEkF,CAAC,IAAIA,CAAC,CAACnF,WAAW,CAAC,CAAC;gBAAC,GADlDkF,GAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cAAK+G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhH,OAAA;gBAAO+G,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClChH,OAAA;kBACEmB,IAAI,EAAC,OAAO;kBACZkD,KAAK,EAAC,UAAU;kBAChByD,OAAO,EAAErH,QAAQ,CAACU,IAAI,KAAK,UAAW;kBACtCkG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;kBAC3D0C,SAAS,EAAC;gBAAwD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,oBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBAAO+G,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClChH,OAAA;kBACEmB,IAAI,EAAC,OAAO;kBACZkD,KAAK,EAAC,SAAS;kBACfyD,OAAO,EAAErH,QAAQ,CAACU,IAAI,KAAK,SAAU;kBACrCkG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;kBAC3D0C,SAAS,EAAC;gBAAwD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,mBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhH,OAAA;YAAK+G,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhH,OAAA;cAAK+G,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WACrD,EAACvG,QAAQ,CAACO,QAAQ,EAAC,GAC9B;cAAA;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACbkC,IAAI,EAAC,MAAM;gBACXW,GAAG,EAAC,GAAG;gBACPK,KAAK,EAAE5D,QAAQ,CAACK,KAAM;gBACtBuG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,OAAO,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC5D0C,SAAS,EAAE,6GACT7E,MAAM,CAACpB,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;gBACHyG,WAAW,EAAC;cAAM;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACDlF,MAAM,CAACpB,KAAK,iBAAId,OAAA;gBAAG+G,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACpB;cAAK;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eAENpH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEqE,KAAK,EAAE5D,QAAQ,CAACO,QAAS;gBACzBqG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC/D0C,SAAS,EAAC,2HAA2H;gBAAAC,QAAA,gBAErIhH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,kBAC9C,EAACvG,QAAQ,CAACO,QAAQ,EAAC,GACrC;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACEmB,IAAI,EAAC,QAAQ;cACbkC,IAAI,EAAC,MAAM;cACXW,GAAG,EAAC,GAAG;cACPK,KAAK,EAAE5D,QAAQ,CAACM,aAAc;cAC9BsG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,eAAe,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cACpE0C,SAAS,EAAE,6GACT7E,MAAM,CAACnB,aAAa,GAAG,gBAAgB,GAAG,iBAAiB,EAC1D;cACHwG,WAAW,EAAC;YAAiB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACDlF,MAAM,CAACnB,aAAa,iBAAIf,OAAA;cAAG+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACnB;YAAa;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5FpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAEL3G,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACM,aAAa,iBACvCf,OAAA;YAAK+G,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzChH,OAAA;cAAK+G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhH,OAAA;gBAAM+G,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5EpH,OAAA;gBAAM+G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC/CvG,QAAQ,CAACO,QAAQ,EAAC,GAAC,EAAC,CAAC0C,UAAU,CAACjD,QAAQ,CAACK,KAAK,CAAC,GAAG4C,UAAU,CAACjD,QAAQ,CAACM,aAAa,CAAC,EAAEgH,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDhH,OAAA;gBAAM+G,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChFpH,OAAA;gBAAM+G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC/C,CAAE,CAACtD,UAAU,CAACjD,QAAQ,CAACK,KAAK,CAAC,GAAG4C,UAAU,CAACjD,QAAQ,CAACM,aAAa,CAAC,IAAI2C,UAAU,CAACjD,QAAQ,CAACK,KAAK,CAAC,GAAI,GAAG,EAAEiH,OAAO,CAAC,CAAC,CAAC,EAAC,GACvH;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACE+G,SAAS,EAAE,uEACTzE,UAAU,GACN,4CAA4C,GAC5CJ,MAAM,CAACP,MAAM,GACX,0BAA0B,GAC1B,+CAA+C,EACpD;cACHqG,WAAW,EAAE9C,UAAW;cACxB+C,WAAW,EAAE/C,UAAW;cACxBgD,UAAU,EAAEhD,UAAW;cACvBiD,MAAM,EAAE7C,UAAW;cAAA0B,QAAA,gBAEnBhH,OAAA,CAACH,eAAe;gBAACkH,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DpH,OAAA;gBAAK+G,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBhH,OAAA;kBAAOoI,OAAO,EAAC,aAAa;kBAACrB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBACrDhH,OAAA;oBAAM+G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPpH,OAAA;oBAAM+G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACRpH,OAAA;kBACEmD,EAAE,EAAC,aAAa;kBAChBxC,IAAI,EAAC,aAAa;kBAClBQ,IAAI,EAAC,MAAM;kBACX4F,SAAS,EAAC,SAAS;kBACnBsB,QAAQ;kBACRC,MAAM,EAAC,SAAS;kBAChBjB,QAAQ,EAAGlC,CAAC,IAAKb,iBAAiB,CAACa,CAAC,CAACmC,MAAM,CAAC/C,KAAK;gBAAE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlF,MAAM,CAACP,MAAM,iBAAI3B,OAAA;cAAG+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9E,MAAM,CAACP;YAAM;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,EAEL3G,QAAQ,CAACkB,MAAM,CAAC6B,MAAM,GAAG,CAAC,iBACzBxD,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAI+G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,GAAC,mBACpC,EAACvG,QAAQ,CAACkB,MAAM,CAAC6B,MAAM,EAAC,GAC3C;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDvG,QAAQ,CAACkB,MAAM,CAACgD,GAAG,CAAC,CAAC4D,KAAK,EAAEC,KAAK,kBAChCxI,OAAA;gBAAoB+G,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC5ChH,OAAA;kBACEyI,GAAG,EAAEF,KAAK,CAACzD,GAAI;kBACf4D,GAAG,EAAEH,KAAK,CAAC5H,IAAK;kBAChBoG,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACFpH,OAAA;kBAAK+G,SAAS,EAAC,0IAA0I;kBAAAC,QAAA,eACvJhH,OAAA;oBACE2I,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC+C,KAAK,CAACpF,EAAE,CAAE;oBACrC4D,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,eAEnEhH,OAAA,CAACJ,SAAS;sBAACmH,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLoB,KAAK,KAAK,CAAC,iBACVxI,OAAA;kBAAK+G,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,EAAC;gBAEzF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA,GAlBOmB,KAAK,CAACpF,EAAE;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhH,OAAA;YAAK+G,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAE5D,QAAQ,CAACY,GAAI;gBACpBgG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,KAAK,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAC3B,WAAW,CAAC,CAAC,CAAE;gBACxEqE,SAAS,EAAE,6GACT7E,MAAM,CAACb,GAAG,GAAG,gBAAgB,GAAG,iBAAiB,EAChD;gBACHkG,WAAW,EAAC;cAAgB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDlF,MAAM,CAACb,GAAG,iBAAIrB,OAAA;gBAAG+G,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACb;cAAG;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEpH,OAAA;gBAAG+G,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,EAEL3G,QAAQ,CAACU,IAAI,KAAK,UAAU,iBAC3BnB,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACb6C,GAAG,EAAC,GAAG;gBACPK,KAAK,EAAE5D,QAAQ,CAACW,UAAW;gBAC3BiG,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,YAAY,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBACjE0C,SAAS,EAAE,6GACT7E,MAAM,CAACd,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,EACvD;gBACHmG,WAAW,EAAC;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EACDlF,MAAM,CAACd,UAAU,iBAAIpB,OAAA;gBAAG+G,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9E,MAAM,CAACd;cAAU;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cAAK+G,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhH,OAAA;gBAAK+G,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDhH,OAAA;kBACEmB,IAAI,EAAC,MAAM;kBACXoG,WAAW,EAAC,mCAAmC;kBAC/CR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC,eACFpH,OAAA;kBACEmB,IAAI,EAAC,MAAM;kBACXoG,WAAW,EAAC,sBAAsB;kBAClCR,SAAS,EAAC;gBAAoH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpH,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACb4F,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBAEjGhH,OAAA,CAACL,QAAQ;kBAACoH,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChCpH,OAAA;kBAAAgH,QAAA,EAAM;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL3G,QAAQ,CAACQ,QAAQ,KAAK,WAAW,iBAChCjB,OAAA;YAAK+G,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDhH,OAAA;cAAI+G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEvE3G,QAAQ,CAACS,WAAW,KAAK,cAAc,iBACtClB,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEqE,KAAK,EAAE5D,QAAQ,CAACmB,aAAc;gBAC9ByF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,eAAe,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBACpE0C,SAAS,EAAC,2HAA2H;gBAAAC,QAAA,gBAErIhH,OAAA;kBAAQqE,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpH,OAAA;kBAAQqE,KAAK,EAAC,aAAa;kBAAA2C,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDpH,OAAA;kBAAQqE,KAAK,EAAC,QAAQ;kBAAA2C,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpH,OAAA;kBAAQqE,KAAK,EAAC,SAAS;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCpH,OAAA;kBAAQqE,KAAK,EAAC,KAAK;kBAAA2C,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCpH,OAAA;kBAAQqE,KAAK,EAAC,MAAM;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCpH,OAAA;kBAAQqE,KAAK,EAAC,SAAS;kBAAA2C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEA3G,QAAQ,CAACS,WAAW,KAAK,cAAc,iBACtClB,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEqE,KAAK,EAAE5D,QAAQ,CAACoB,MAAO;gBACvBwF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,QAAQ,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC7D0C,SAAS,EAAC,2HAA2H;gBAAAC,QAAA,gBAErIhH,OAAA;kBAAQqE,KAAK,EAAC,EAAE;kBAAA2C,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCpH,OAAA;kBAAQqE,KAAK,EAAC,cAAc;kBAAA2C,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DpH,OAAA;kBAAQqE,KAAK,EAAC,kBAAkB;kBAAA2C,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvEpH,OAAA;kBAAQqE,KAAK,EAAC,iBAAiB;kBAAA2C,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjEpH,OAAA;kBAAQqE,KAAK,EAAC,aAAa;kBAAA2C,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAEDpH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAO+G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpH,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAE5D,QAAQ,CAACwB,QAAS;gBACzBoF,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC/D0C,SAAS,EAAC,2HAA2H;gBACrIQ,WAAW,EAAC;cAAqC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cAAK+G,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EACvCvG,QAAQ,CAACa,IAAI,CAACqD,GAAG,CAAC,CAAC0B,GAAG,EAAEmC,KAAK,kBAC5BxI,OAAA;gBAEE+G,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE5GX,GAAG,eACJrG,OAAA;kBACE2I,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAACE,GAAG,CAAE;kBAC9BU,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,eAElEhH,OAAA,CAACP,SAAS;oBAACsH,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA,GATJoB,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhH,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAE7B,MAAO;gBACd6E,QAAQ,EAAGlC,CAAC,IAAK1C,SAAS,CAAC0C,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;gBAC3CuE,UAAU,EAAGzD,CAAC,IAAKA,CAAC,CAAC0D,GAAG,KAAK,OAAO,KAAK1D,CAAC,CAACC,cAAc,CAAC,CAAC,EAAEa,MAAM,CAAC,CAAC,CAAE;gBACvEc,SAAS,EAAC,2HAA2H;gBACrIQ,WAAW,EAAC;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACFpH,OAAA;gBACEmB,IAAI,EAAC,QAAQ;gBACbwH,OAAO,EAAE1C,MAAO;gBAChBc,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,EAC1F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAO+G,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpH,OAAA;cACEqE,KAAK,EAAE5D,QAAQ,CAACc,QAAS;cACzB8F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACjD,KAAK,CAAE;cAC/DoD,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAoC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACFpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eAENpH,OAAA;YAAK+G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhH,OAAA;cAAK+G,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChH,OAAA;gBACEmB,IAAI,EAAC,UAAU;gBACfgC,EAAE,EAAC,UAAU;gBACb2E,OAAO,EAAErH,QAAQ,CAACe,QAAS;gBAC3B6F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACmC,MAAM,CAACQ,OAAO,CAAE;gBACjEf,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACFpH,OAAA;gBAAOoI,OAAO,EAAC,UAAU;gBAACrB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAExE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpH,OAAA;cAAK+G,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChH,OAAA;gBACEmB,IAAI,EAAC,UAAU;gBACfgC,EAAE,EAAC,YAAY;gBACf2E,OAAO,EAAErH,QAAQ,CAACgB,UAAW;gBAC7B4F,QAAQ,EAAGlC,CAAC,IAAKhB,iBAAiB,CAAC,YAAY,EAAEgB,CAAC,CAACmC,MAAM,CAACQ,OAAO,CAAE;gBACnEf,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACFpH,OAAA;gBAAOoI,OAAO,EAAC,YAAY;gBAACrB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpH,OAAA;YAAK+G,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxChH,OAAA;cAAI+G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EpH,OAAA;cAAK+G,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9ChH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3G,QAAQ,CAACE,IAAI,IAAI,SAAS;cAAA;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DpH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3G,QAAQ,CAACO,QAAQ,EAAC,GAAC,EAACP,QAAQ,CAACK,KAAK,IAAI,MAAM;cAAA;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EpH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAN,gBAAA,GAAAhH,UAAU,CAAC4G,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAAC3F,EAAE,KAAK1C,QAAQ,CAACQ,QAAQ,CAAC,cAAA6F,gBAAA,uBAAhDA,gBAAA,CAAkDnG,IAAI,KAAI,SAAS;cAAA;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGpH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3G,QAAQ,CAACU,IAAI;cAAA;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CpH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3G,QAAQ,CAACkB,MAAM,CAAC6B,MAAM,EAAC,WAAS;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEpH,OAAA;gBAAAgH,QAAA,gBAAGhH,OAAA;kBAAAgH,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3G,QAAQ,CAACe,QAAQ,GAAG,QAAQ,GAAG,OAAO;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOpH,OAAA;UAAAgH,QAAA,GAAK,wBAAsB,EAACzG,WAAW;QAAA;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACzD;EACF,CAAC;EAED,IAAI,CAAClH,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA,CAACR,eAAe;IAAAwH,QAAA,eACdhH,OAAA,CAACT,MAAM,CAACwJ,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,IAAI,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACrBlC,SAAS,EAAC,gFAAgF;MAC1F4B,OAAO,EAAExI,OAAQ;MAAA6G,QAAA,eAEjBhH,OAAA,CAACT,MAAM,CAACwJ,GAAG;QACTC,OAAO,EAAE;UAAEI,KAAK,EAAE,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACpCC,OAAO,EAAE;UAAEE,KAAK,EAAE,CAAC;UAAEH,OAAO,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACjCN,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAACE,eAAe,CAAC,CAAE;QACpC0B,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBAGvFhH,OAAA;UAAK+G,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7EhH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAI+G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEpH,OAAA;cAAG+G,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,OACnC,EAACzG,WAAW,EAAC,MAAI,EAAC2C,KAAK,CAACM,MAAM,EAAC,IAAE,GAAAlD,MAAA,GAAC4C,KAAK,CAAC3C,WAAW,GAAG,CAAC,CAAC,cAAAD,MAAA,uBAAtBA,MAAA,CAAwBM,WAAW;YAAA;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNpH,OAAA;YACE2I,OAAO,EAAExI,OAAQ;YACjB4G,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eAE9EhH,OAAA,CAACP,SAAS;cAACsH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDhH,OAAA;YAAK+G,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/C9D,KAAK,CAACyB,GAAG,CAAC,CAACtB,IAAI,EAAEmF,KAAK,kBACrBxI,OAAA;cAAmB+G,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9ChH,OAAA;gBAAK+G,SAAS,EAAE,6EACdxG,WAAW,GAAG8C,IAAI,CAACF,EAAE,GACjB,yBAAyB,GACzB5C,WAAW,KAAK8C,IAAI,CAACF,EAAE,GACrB,gCAAgC,GAChC,2BAA2B,EAChC;gBAAA6D,QAAA,EACAzG,WAAW,GAAG8C,IAAI,CAACF,EAAE,GAAG,GAAG,GAAGE,IAAI,CAACF;cAAE;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNpH,OAAA;gBAAM+G,SAAS,EAAE,4BACfxG,WAAW,IAAI8C,IAAI,CAACF,EAAE,GAAG,eAAe,GAAG,eAAe,EACzD;gBAAA6D,QAAA,EACA3D,IAAI,CAAC1C;cAAI;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACNoB,KAAK,GAAGtF,KAAK,CAACM,MAAM,GAAG,CAAC,iBACvBxD,OAAA;gBAAK+G,SAAS,EAAE,mBACdxG,WAAW,GAAG8C,IAAI,CAACF,EAAE,GAAG,cAAc,GAAG,aAAa;cACrD;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACN;YAAA,GAnBO/D,IAAI,CAACF,EAAE;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC1CJ,iBAAiB,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFhH,OAAA;YACE2I,OAAO,EAAE1E,UAAW;YACpByD,QAAQ,EAAEnH,WAAW,KAAK,CAAE;YAC5BwG,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpH,OAAA;YAAK+G,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhH,OAAA;cACE2I,OAAO,EAAExI,OAAQ;cACjB4G,SAAS,EAAC,yGAAyG;cAAAC,QAAA,EACpH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER7G,WAAW,GAAG2C,KAAK,CAACM,MAAM,gBACzBxD,OAAA;cACE2I,OAAO,EAAE7E,UAAW;cACpBiD,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAC9G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETpH,OAAA;cACE2I,OAAO,EAAErC,YAAa;cACtBoB,QAAQ,EAAEtF,YAAa;cACvB2E,SAAS,EAAC,qIAAqI;cAAAC,QAAA,EAE9I5E,YAAY,GAAG,aAAa,GAAG;YAAgB;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAAC/G,EAAA,CAp0BIJ,eAAe;AAAAoJ,EAAA,GAAfpJ,eAAe;AAs0BrB,eAAeA,eAAe;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}