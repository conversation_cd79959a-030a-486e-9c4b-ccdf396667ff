{"ast": null, "code": "import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\nfunction isForcedMotionValue(key, {\n  layout,\n  layoutId\n}) {\n  return transformProps.has(key) || key.startsWith(\"origin\") || (layout || layoutId !== undefined) && (!!scaleCorrectors[key] || key === \"opacity\");\n}\nexport { isForcedMotionValue };", "map": {"version": 3, "names": ["transformProps", "scaleCorrectors", "isForcedMotionValue", "key", "layout", "layoutId", "has", "startsWith", "undefined"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs"], "sourcesContent": ["import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,YAAY;AAC3C,SAASC,eAAe,QAAQ,8CAA8C;AAE9E,SAASC,mBAAmBA,CAACC,GAAG,EAAE;EAAEC,MAAM;EAAEC;AAAS,CAAC,EAAE;EACpD,OAAQL,cAAc,CAACM,GAAG,CAACH,GAAG,CAAC,IAC3BA,GAAG,CAACI,UAAU,CAAC,QAAQ,CAAC,IACvB,CAACH,MAAM,IAAIC,QAAQ,KAAKG,SAAS,MAC7B,CAAC,CAACP,eAAe,CAACE,GAAG,CAAC,IAAIA,GAAG,KAAK,SAAS,CAAE;AAC1D;AAEA,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}