{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminCategoriesPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, TagIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport AdminLayout from '../components/AdminLayout';\nimport ConfirmationModal from '../components/ConfirmationModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminCategoriesPage = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAdmin();\n  const {\n    categories,\n    addCategory,\n    deleteCategory\n  } = useProducts();\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [categoryToDelete, setCategoryToDelete] = useState(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    icon: '',\n    subcategories: []\n  });\n  const handleAddCategory = () => {\n    setFormData({\n      name: '',\n      description: '',\n      icon: '',\n      subcategories: []\n    });\n    setEditingCategory(null);\n    setShowAddModal(true);\n  };\n  const handleEditCategory = category => {\n    setFormData({\n      name: category.name,\n      description: category.description,\n      icon: category.icon,\n      subcategories: category.subcategories || []\n    });\n    setEditingCategory(category);\n    setShowAddModal(true);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const result = await addCategory(formData);\n      if (result.success) {\n        setShowAddModal(false);\n        setEditingCategory(null);\n        setFormData({\n          name: '',\n          description: '',\n          icon: '',\n          subcategories: []\n        });\n        showSuccess('Category Added', `${formData.name} has been successfully added.`);\n      } else {\n        showError('Add Category Failed', result.error || 'Failed to add category. Please try again.');\n      }\n    } catch (error) {\n      showError('Add Category Failed', 'An unexpected error occurred while adding the category.');\n    }\n  };\n  const handleDeleteCategory = category => {\n    setCategoryToDelete(category);\n    setShowDeleteConfirm(true);\n  };\n  const confirmDeleteCategory = async () => {\n    if (!categoryToDelete) return;\n    setIsDeleting(true);\n    try {\n      const result = await deleteCategory(categoryToDelete.id);\n      if (result.success) {\n        showSuccess('Category Deleted', `${categoryToDelete.name} has been successfully deleted.`);\n        setShowDeleteConfirm(false);\n        setCategoryToDelete(null);\n      } else {\n        showError('Delete Failed', result.error || 'Failed to delete category. Please try again.');\n      }\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting the category.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n  const CategoryCard = ({\n    category\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    layout: true,\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.9\n    },\n    className: \"p-6 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-3xl\",\n          children: category.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), hasPermission('categories') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleEditCategory(category),\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n            className: \"w-4 h-4 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleDeleteCategory(category.id),\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), category.subcategories && category.subcategories.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-sm font-medium mb-2 text-gray-700\",\n        children: \"Subcategories:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: category.subcategories.map((sub, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 text-xs rounded-full bg-light-orange-100 text-light-orange-800\",\n          children: sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n  const Modal = () => /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: showAddModal && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      exit: {\n        opacity: 0\n      },\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      onClick: () => setShowAddModal(false),\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0.9,\n          opacity: 0\n        },\n        onClick: e => e.stopPropagation(),\n        className: \"w-full max-w-md p-6 rounded-xl shadow-xl bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: editingCategory ? 'Edit Category' : 'Add New Category'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddModal(false),\n            className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-2 text-gray-700\",\n              children: \"Category Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => setFormData({\n                ...formData,\n                name: e.target.value\n              }),\n              className: \"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-2 text-gray-700\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description,\n              onChange: e => setFormData({\n                ...formData,\n                description: e.target.value\n              }),\n              rows: 3,\n              className: \"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-2 text-gray-700\",\n              children: \"Icon (Emoji)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.icon,\n              onChange: e => setFormData({\n                ...formData,\n                icon: e.target.value\n              }),\n              placeholder: \"\\uD83D\\uDCF1\",\n              className: \"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3 pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddModal(false),\n              className: \"flex-1 px-4 py-2 rounded-lg font-medium transition-colors bg-gray-200 text-gray-800 hover:bg-gray-300\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"flex-1 px-4 py-2 bg-light-orange-500 text-white rounded-lg font-medium hover:bg-light-orange-600 transition-colors\",\n              children: editingCategory ? 'Update' : 'Create'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Manage product categories and subcategories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), hasPermission('categories') && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleAddCategory,\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 rounded-xl shadow-lg bg-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-blue-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(TagIcon, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: categories.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 rounded-xl shadow-lg bg-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-green-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(TagIcon, {\n                className: \"w-6 h-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Active Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: categories.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 rounded-xl shadow-lg bg-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-purple-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(TagIcon, {\n                className: \"w-6 h-6 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: categories.reduce((total, cat) => {\n                  var _cat$subcategories;\n                  return total + (((_cat$subcategories = cat.subcategories) === null || _cat$subcategories === void 0 ? void 0 : _cat$subcategories.length) || 0);\n                }, 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(CategoryCard, {\n            category: category\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminCategoriesPage, \"nayO3/tbTB++6GzM03XREOABrjU=\", false, function () {\n  return [useAdmin, useProducts, useToast];\n});\n_c = AdminCategoriesPage;\nexport default AdminCategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"AdminCategoriesPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "PlusIcon", "PencilIcon", "TrashIcon", "TagIcon", "XMarkIcon", "useAdmin", "useProducts", "useToast", "AdminLayout", "ConfirmationModal", "jsxDEV", "_jsxDEV", "AdminCategoriesPage", "_s", "hasPermission", "categories", "addCategory", "deleteCategory", "showSuccess", "showError", "showAddModal", "setShowAddModal", "editingCategory", "setEditingCategory", "showDeleteConfirm", "setShowDeleteConfirm", "categoryToDelete", "setCategoryToDelete", "isDeleting", "setIsDeleting", "formData", "setFormData", "name", "description", "icon", "subcategories", "handleAddCategory", "handleEditCategory", "category", "handleSubmit", "e", "preventDefault", "result", "success", "error", "handleDeleteCategory", "confirmDeleteCategory", "id", "CategoryCard", "div", "layout", "initial", "opacity", "scale", "animate", "exit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "sub", "index", "replace", "l", "toUpperCase", "Modal", "stopPropagation", "onSubmit", "type", "value", "onChange", "target", "required", "rows", "placeholder", "button", "whileHover", "whileTap", "reduce", "total", "cat", "_cat$subcategories", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminCategoriesPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  TagIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport { useToast } from '../contexts/ToastContext';\nimport AdminLayout from '../components/AdminLayout';\nimport ConfirmationModal from '../components/ConfirmationModal';\n\nconst AdminCategoriesPage = () => {\n  const { hasPermission } = useAdmin();\n  const { categories, addCategory, deleteCategory } = useProducts();\n  const { showSuccess, showError } = useToast();\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [categoryToDelete, setCategoryToDelete] = useState(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    icon: '',\n    subcategories: []\n  });\n\n  const handleAddCategory = () => {\n    setFormData({ name: '', description: '', icon: '', subcategories: [] });\n    setEditingCategory(null);\n    setShowAddModal(true);\n  };\n\n  const handleEditCategory = (category) => {\n    setFormData({\n      name: category.name,\n      description: category.description,\n      icon: category.icon,\n      subcategories: category.subcategories || []\n    });\n    setEditingCategory(category);\n    setShowAddModal(true);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const result = await addCategory(formData);\n      if (result.success) {\n        setShowAddModal(false);\n        setEditingCategory(null);\n        setFormData({ name: '', description: '', icon: '', subcategories: [] });\n        showSuccess('Category Added', `${formData.name} has been successfully added.`);\n      } else {\n        showError('Add Category Failed', result.error || 'Failed to add category. Please try again.');\n      }\n    } catch (error) {\n      showError('Add Category Failed', 'An unexpected error occurred while adding the category.');\n    }\n  };\n\n  const handleDeleteCategory = (category) => {\n    setCategoryToDelete(category);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDeleteCategory = async () => {\n    if (!categoryToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      const result = await deleteCategory(categoryToDelete.id);\n      if (result.success) {\n        showSuccess('Category Deleted', `${categoryToDelete.name} has been successfully deleted.`);\n        setShowDeleteConfirm(false);\n        setCategoryToDelete(null);\n      } else {\n        showError('Delete Failed', result.error || 'Failed to delete category. Please try again.');\n      }\n    } catch (error) {\n      showError('Delete Failed', 'An unexpected error occurred while deleting the category.');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const CategoryCard = ({ category }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"p-6 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white\"\n    >\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"text-3xl\">{category.icon}</div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              {category.name}\n            </h3>\n            <p className=\"text-sm text-gray-600\">\n              {category.description}\n            </p>\n          </div>\n        </div>\n        {hasPermission('categories') && (\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => handleEditCategory(category)}\n              className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n            >\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n            <button\n              onClick={() => handleDeleteCategory(category.id)}\n              className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n            >\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          </div>\n        )}\n      </div>\n\n      {category.subcategories && category.subcategories.length > 0 && (\n        <div>\n          <h4 className=\"text-sm font-medium mb-2 text-gray-700\">\n            Subcategories:\n          </h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {category.subcategories.map((sub, index) => (\n              <span\n                key={index}\n                className=\"px-2 py-1 text-xs rounded-full bg-light-orange-100 text-light-orange-800\"\n              >\n                {sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n    </motion.div>\n  );\n\n  const Modal = () => (\n    <AnimatePresence>\n      {showAddModal && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\"\n          onClick={() => setShowAddModal(false)}\n        >\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.9, opacity: 0 }}\n            onClick={(e) => e.stopPropagation()}\n            className=\"w-full max-w-md p-6 rounded-xl shadow-xl bg-white\"\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                {editingCategory ? 'Edit Category' : 'Add New Category'}\n              </h3>\n              <button\n                onClick={() => setShowAddModal(false)}\n                className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n              >\n                <XMarkIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2 text-gray-700\">\n                  Category Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                  className=\"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium mb-2 text-gray-700\">\n                  Description\n                </label>\n                <textarea\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium mb-2 text-gray-700\">\n                  Icon (Emoji)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.icon}\n                  onChange={(e) => setFormData({ ...formData, icon: e.target.value })}\n                  placeholder=\"📱\"\n                  className=\"w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500\"\n                />\n              </div>\n\n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowAddModal(false)}\n                  className=\"flex-1 px-4 py-2 rounded-lg font-medium transition-colors bg-gray-200 text-gray-800 hover:bg-gray-300\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 px-4 py-2 bg-light-orange-500 text-white rounded-lg font-medium hover:bg-light-orange-600 transition-colors\"\n                >\n                  {editingCategory ? 'Update' : 'Create'}\n                </button>\n              </div>\n            </form>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Categories\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Manage product categories and subcategories\n            </p>\n          </div>\n          {hasPermission('categories') && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={handleAddCategory}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5\" />\n              <span>Add Category</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-3 bg-blue-100 rounded-full\">\n                <TagIcon className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Total Categories\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {categories.length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-3 bg-green-100 rounded-full\">\n                <TagIcon className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Active Categories\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {categories.length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-3 bg-purple-100 rounded-full\">\n                <TagIcon className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Subcategories\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {categories.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <AnimatePresence>\n            {categories.map(category => (\n              <CategoryCard key={category.id} category={category} />\n            ))}\n          </AnimatePresence>\n        </div>\n\n        {/* Modal */}\n        <Modal />\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminCategoriesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,SAAS,QACJ,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAc,CAAC,GAAGT,QAAQ,CAAC,CAAC;EACpC,MAAM;IAAEU,UAAU;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGX,WAAW,CAAC,CAAC;EACjE,MAAM;IAAEY,WAAW;IAAEC;EAAU,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAC7C,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BL,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CAAC;IACvEZ,kBAAkB,CAAC,IAAI,CAAC;IACxBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgB,kBAAkB,GAAIC,QAAQ,IAAK;IACvCP,WAAW,CAAC;MACVC,IAAI,EAAEM,QAAQ,CAACN,IAAI;MACnBC,WAAW,EAAEK,QAAQ,CAACL,WAAW;MACjCC,IAAI,EAAEI,QAAQ,CAACJ,IAAI;MACnBC,aAAa,EAAEG,QAAQ,CAACH,aAAa,IAAI;IAC3C,CAAC,CAAC;IACFZ,kBAAkB,CAACe,QAAQ,CAAC;IAC5BjB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM1B,WAAW,CAACc,QAAQ,CAAC;MAC1C,IAAIY,MAAM,CAACC,OAAO,EAAE;QAClBtB,eAAe,CAAC,KAAK,CAAC;QACtBE,kBAAkB,CAAC,IAAI,CAAC;QACxBQ,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,aAAa,EAAE;QAAG,CAAC,CAAC;QACvEjB,WAAW,CAAC,gBAAgB,EAAE,GAAGY,QAAQ,CAACE,IAAI,+BAA+B,CAAC;MAChF,CAAC,MAAM;QACLb,SAAS,CAAC,qBAAqB,EAAEuB,MAAM,CAACE,KAAK,IAAI,2CAA2C,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzB,SAAS,CAAC,qBAAqB,EAAE,yDAAyD,CAAC;IAC7F;EACF,CAAC;EAED,MAAM0B,oBAAoB,GAAIP,QAAQ,IAAK;IACzCX,mBAAmB,CAACW,QAAQ,CAAC;IAC7Bb,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMqB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACpB,gBAAgB,EAAE;IAEvBG,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMa,MAAM,GAAG,MAAMzB,cAAc,CAACS,gBAAgB,CAACqB,EAAE,CAAC;MACxD,IAAIL,MAAM,CAACC,OAAO,EAAE;QAClBzB,WAAW,CAAC,kBAAkB,EAAE,GAAGQ,gBAAgB,CAACM,IAAI,iCAAiC,CAAC;QAC1FP,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLR,SAAS,CAAC,eAAe,EAAEuB,MAAM,CAACE,KAAK,IAAI,8CAA8C,CAAC;MAC5F;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzB,SAAS,CAAC,eAAe,EAAE,2DAA2D,CAAC;IACzF,CAAC,SAAS;MACRU,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmB,YAAY,GAAGA,CAAC;IAAEV;EAAS,CAAC,kBAChC3B,OAAA,CAACb,MAAM,CAACmD,GAAG;IACTC,MAAM;IACNC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACpCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACjCG,SAAS,EAAC,+EAA+E;IAAAC,QAAA,gBAEzF9C,OAAA;MAAK6C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD9C,OAAA;QAAK6C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C9C,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAEnB,QAAQ,CAACJ;QAAI;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/ClD,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAI6C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChDnB,QAAQ,CAACN;UAAI;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACLlD,OAAA;YAAG6C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACjCnB,QAAQ,CAACL;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL/C,aAAa,CAAC,YAAY,CAAC,iBAC1BH,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9C,OAAA;UACEmD,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAACC,QAAQ,CAAE;UAC5CkB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eAE9D9C,OAAA,CAACV,UAAU;YAACuD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACTlD,OAAA;UACEmD,OAAO,EAAEA,CAAA,KAAMjB,oBAAoB,CAACP,QAAQ,CAACS,EAAE,CAAE;UACjDS,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eAE9D9C,OAAA,CAACT,SAAS;YAACsD,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELvB,QAAQ,CAACH,aAAa,IAAIG,QAAQ,CAACH,aAAa,CAAC4B,MAAM,GAAG,CAAC,iBAC1DpD,OAAA;MAAA8C,QAAA,gBACE9C,OAAA;QAAI6C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlD,OAAA;QAAK6C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClCnB,QAAQ,CAACH,aAAa,CAAC6B,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACrCvD,OAAA;UAEE6C,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EAEnFQ,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;QAAC,GAHxDH,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIN,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CACb;EAED,MAAMS,KAAK,GAAGA,CAAA,kBACZ3D,OAAA,CAACZ,eAAe;IAAA0D,QAAA,EACbrC,YAAY,iBACXT,OAAA,CAACb,MAAM,CAACmD,GAAG;MACTE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBE,OAAO,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACxBG,IAAI,EAAE;QAAEH,OAAO,EAAE;MAAE,CAAE;MACrBI,SAAS,EAAC,gFAAgF;MAC1FM,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,KAAK,CAAE;MAAAoC,QAAA,eAEtC9C,OAAA,CAACb,MAAM,CAACmD,GAAG;QACTE,OAAO,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAED,OAAO,EAAE;QAAE,CAAE;QACpCE,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAE;QAClCG,IAAI,EAAE;UAAEF,KAAK,EAAE,GAAG;UAAED,OAAO,EAAE;QAAE,CAAE;QACjCU,OAAO,EAAGtB,CAAC,IAAKA,CAAC,CAAC+B,eAAe,CAAC,CAAE;QACpCf,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAE7D9C,OAAA;UAAK6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD9C,OAAA;YAAI6C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChDnC,eAAe,GAAG,eAAe,GAAG;UAAkB;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACLlD,OAAA;YACEmD,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,KAAK,CAAE;YACtCmC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAE9D9C,OAAA,CAACP,SAAS;cAACoD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlD,OAAA;UAAM6D,QAAQ,EAAEjC,YAAa;UAACiB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjD9C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAO6C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACE8D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE5C,QAAQ,CAACE,IAAK;cACrB2C,QAAQ,EAAGnC,CAAC,IAAKT,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,IAAI,EAAEQ,CAAC,CAACoC,MAAM,CAACF;cAAM,CAAC,CAAE;cACpElB,SAAS,EAAC,qIAAqI;cAC/IqB,QAAQ;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAO6C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACE+D,KAAK,EAAE5C,QAAQ,CAACG,WAAY;cAC5B0C,QAAQ,EAAGnC,CAAC,IAAKT,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEO,CAAC,CAACoC,MAAM,CAACF;cAAM,CAAC,CAAE;cAC3EI,IAAI,EAAE,CAAE;cACRtB,SAAS,EAAC;YAAqI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAO6C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACE8D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE5C,QAAQ,CAACI,IAAK;cACrByC,QAAQ,EAAGnC,CAAC,IAAKT,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,IAAI,EAAEM,CAAC,CAACoC,MAAM,CAACF;cAAM,CAAC,CAAE;cACpEK,WAAW,EAAC,cAAI;cAChBvB,SAAS,EAAC;YAAqI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC9C,OAAA;cACE8D,IAAI,EAAC,QAAQ;cACbX,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,KAAK,CAAE;cACtCmC,SAAS,EAAC,uGAAuG;cAAAC,QAAA,EAClH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACE8D,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAE7HnC,eAAe,GAAG,QAAQ,GAAG;YAAQ;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACb;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAClB;EAED,oBACElD,OAAA,CAACH,WAAW;IAAAiD,QAAA,eACV9C,OAAA;MAAK6C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExB9C,OAAA;QAAK6C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD9C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAI6C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlD,OAAA;YAAG6C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACL/C,aAAa,CAAC,YAAY,CAAC,iBAC1BH,OAAA,CAACb,MAAM,CAACkF,MAAM;UACZC,UAAU,EAAE;YAAE5B,KAAK,EAAE;UAAK,CAAE;UAC5B6B,QAAQ,EAAE;YAAE7B,KAAK,EAAE;UAAK,CAAE;UAC1BS,OAAO,EAAE1B,iBAAkB;UAC3BoB,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvI9C,OAAA,CAACX,QAAQ;YAACwD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChClD,OAAA;YAAA8C,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9C,OAAA;UAAK6C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD9C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9C,OAAA;cAAK6C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C9C,OAAA,CAACR,OAAO;gBAACqD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAG6C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlD,OAAA;gBAAG6C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5C1C,UAAU,CAACgD;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAK6C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD9C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9C,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC5C9C,OAAA,CAACR,OAAO;gBAACqD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAG6C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlD,OAAA;gBAAG6C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5C1C,UAAU,CAACgD;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAK6C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD9C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9C,OAAA;cAAK6C,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7C9C,OAAA,CAACR,OAAO;gBAACqD,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAG6C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlD,OAAA;gBAAG6C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5C1C,UAAU,CAACoE,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG;kBAAA,IAAAC,kBAAA;kBAAA,OAAKF,KAAK,IAAI,EAAAE,kBAAA,GAAAD,GAAG,CAAClD,aAAa,cAAAmD,kBAAA,uBAAjBA,kBAAA,CAAmBvB,MAAM,KAAI,CAAC,CAAC;gBAAA,GAAE,CAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE9C,OAAA,CAACZ,eAAe;UAAA0D,QAAA,EACb1C,UAAU,CAACiD,GAAG,CAAC1B,QAAQ,iBACtB3B,OAAA,CAACqC,YAAY;YAAmBV,QAAQ,EAAEA;UAAS,GAAhCA,QAAQ,CAACS,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuB,CACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNlD,OAAA,CAAC2D,KAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAChD,EAAA,CA1TID,mBAAmB;EAAA,QACGP,QAAQ,EACkBC,WAAW,EAC5BC,QAAQ;AAAA;AAAAgF,EAAA,GAHvC3E,mBAAmB;AA4TzB,eAAeA,mBAAmB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}