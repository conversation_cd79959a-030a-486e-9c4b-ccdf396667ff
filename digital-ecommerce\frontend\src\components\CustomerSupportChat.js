import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  UserIcon,
  ComputerDesktopIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { useToast } from '../contexts/ToastContext';

const CustomerSupportChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('chat'); // chat, contact, hours
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      message: 'Hello! I\'m here to help you with your PC gaming needs. How can I assist you today?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const { showSuccess } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickQuestions = [
    'What PC specs do I need for gaming?',
    'How do I check component compatibility?',
    'What\'s your return policy?',
    'Do you offer installation services?',
    'How do I track my order?'
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      type: 'user',
      message: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate bot response
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        type: 'bot',
        message: getBotResponse(inputMessage),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const getBotResponse = (userMessage) => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('compatibility') || message.includes('compatible')) {
      return 'Great question! Our PC Builder tool automatically checks compatibility between components. You can also check our compatibility guides in the product specifications. Would you like me to direct you to a specific component category?';
    } else if (message.includes('return') || message.includes('refund')) {
      return 'We offer a 30-day return policy for most items. PC components have a 15-day return window if opened. Digital products are non-refundable. Would you like more details about returns for a specific product type?';
    } else if (message.includes('specs') || message.includes('requirements')) {
      return 'PC gaming requirements vary by game and resolution. For 1080p gaming, we recommend at least a GTX 1660 Super and Ryzen 5 5600. For 1440p, consider RTX 4070 and Ryzen 7 7700X. What resolution and games are you targeting?';
    } else if (message.includes('installation') || message.includes('setup')) {
      return 'We offer installation guides and video tutorials for all components. For complex builds, we partner with local technicians. Would you like me to connect you with our technical support team?';
    } else if (message.includes('order') || message.includes('track')) {
      return 'You can track your order in your account dashboard or with the tracking number sent to your email. Need help finding your order status?';
    } else {
      return 'I understand you need help with that. Let me connect you with one of our PC gaming specialists who can provide detailed assistance. Would you prefer to continue chatting or schedule a call?';
    }
  };

  const handleQuickQuestion = (question) => {
    setInputMessage(question);
  };

  const handleContactSubmit = (e) => {
    e.preventDefault();
    showSuccess('Message Sent', 'We\'ll get back to you within 24 hours!');
    setIsOpen(false);
  };

  return (
    <>
      {/* Chat Toggle Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 z-40 p-4 bg-light-orange-500 text-white rounded-full shadow-lg hover:bg-light-orange-600 transition-colors ${
          isOpen ? 'hidden' : 'block'
        }`}
      >
        <ChatBubbleLeftRightIcon className="w-6 h-6" />
      </motion.button>

      {/* Chat Widget */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-6 right-6 z-50 w-96 h-[500px] bg-white rounded-xl shadow-2xl border border-gray-200 flex flex-col"
          >
            {/* Header */}
            <div className="bg-light-orange-500 text-white p-4 rounded-t-xl flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <ComputerDesktopIcon className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-semibold">PC Gaming Support</h3>
                  <p className="text-xs opacity-90">We're here to help!</p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-white/20 rounded-full transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setActiveTab('chat')}
                className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
                  activeTab === 'chat'
                    ? 'text-light-orange-600 border-b-2 border-light-orange-500'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Chat
              </button>
              <button
                onClick={() => setActiveTab('contact')}
                className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
                  activeTab === 'contact'
                    ? 'text-light-orange-600 border-b-2 border-light-orange-500'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Contact
              </button>
              <button
                onClick={() => setActiveTab('hours')}
                className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
                  activeTab === 'hours'
                    ? 'text-light-orange-600 border-b-2 border-light-orange-500'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Hours
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-hidden">
              {activeTab === 'chat' && (
                <div className="h-full flex flex-col">
                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                            message.type === 'user'
                              ? 'bg-light-orange-500 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          {message.message}
                        </div>
                      </div>
                    ))}
                    
                    {isTyping && (
                      <div className="flex justify-start">
                        <div className="bg-gray-100 px-3 py-2 rounded-lg">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Quick Questions */}
                  {messages.length === 1 && (
                    <div className="p-4 border-t border-gray-200">
                      <p className="text-xs text-gray-600 mb-2">Quick questions:</p>
                      <div className="space-y-1">
                        {quickQuestions.slice(0, 3).map((question, index) => (
                          <button
                            key={index}
                            onClick={() => handleQuickQuestion(question)}
                            className="block w-full text-left text-xs text-light-orange-600 hover:text-light-orange-700 py-1"
                          >
                            {question}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Input */}
                  <div className="p-4 border-t border-gray-200">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        placeholder="Type your message..."
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                      />
                      <button
                        onClick={handleSendMessage}
                        disabled={!inputMessage.trim()}
                        className="p-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <PaperAirplaneIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'contact' && (
                <div className="p-4">
                  <form onSubmit={handleContactSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Name
                      </label>
                      <input
                        type="text"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Subject
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-light-orange-500 focus:border-transparent">
                        <option>General Question</option>
                        <option>Technical Support</option>
                        <option>Order Issue</option>
                        <option>Return/Exchange</option>
                        <option>PC Building Help</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Message
                      </label>
                      <textarea
                        rows={4}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-light-orange-500 focus:border-transparent"
                        placeholder="How can we help you?"
                      />
                    </div>
                    <button
                      type="submit"
                      className="w-full bg-light-orange-500 text-white py-2 px-4 rounded-lg hover:bg-light-orange-600 transition-colors text-sm font-medium"
                    >
                      Send Message
                    </button>
                  </form>
                </div>
              )}

              {activeTab === 'hours' && (
                <div className="p-4 space-y-4">
                  <div className="flex items-center space-x-3 text-green-600">
                    <ClockIcon className="w-5 h-5" />
                    <span className="text-sm font-medium">We're Online!</span>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <PhoneIcon className="w-4 h-4 text-gray-600" />
                      <div>
                        <p className="text-sm font-medium">Phone Support</p>
                        <p className="text-xs text-gray-600">1-800-PC-GAMING</p>
                        <p className="text-xs text-gray-600">Mon-Fri: 9AM-8PM EST</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <EnvelopeIcon className="w-4 h-4 text-gray-600" />
                      <div>
                        <p className="text-sm font-medium">Email Support</p>
                        <p className="text-xs text-gray-600"><EMAIL></p>
                        <p className="text-xs text-gray-600">Response within 24 hours</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <ChatBubbleLeftRightIcon className="w-4 h-4 text-gray-600" />
                      <div>
                        <p className="text-sm font-medium">Live Chat</p>
                        <p className="text-xs text-gray-600">Available 24/7</p>
                        <p className="text-xs text-gray-600">Average response: 2 minutes</p>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Business Hours</h4>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div className="flex justify-between">
                        <span>Monday - Friday</span>
                        <span>9:00 AM - 8:00 PM EST</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Saturday</span>
                        <span>10:00 AM - 6:00 PM EST</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sunday</span>
                        <span>12:00 PM - 5:00 PM EST</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default CustomerSupportChat;
