{"ast": null, "code": "import { anticipate, backInOut, circInOut } from 'motion-utils';\nconst unsupportedEasingFunctions = {\n  anticipate,\n  backInOut,\n  circInOut\n};\nfunction isUnsupportedEase(key) {\n  return key in unsupportedEasingFunctions;\n}\nfunction replaceStringEasing(transition) {\n  if (typeof transition.ease === \"string\" && isUnsupportedEase(transition.ease)) {\n    transition.ease = unsupportedEasingFunctions[transition.ease];\n  }\n}\nexport { replaceStringEasing };", "map": {"version": 3, "names": ["anticipate", "backInOut", "circInOut", "unsupportedEasingFunctions", "isUnsupportedEase", "key", "replaceStringEasing", "transition", "ease"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs"], "sourcesContent": ["import { anticipate, backInOut, circInOut } from 'motion-utils';\n\nconst unsupportedEasingFunctions = {\n    anticipate,\n    backInOut,\n    circInOut,\n};\nfunction isUnsupportedEase(key) {\n    return key in unsupportedEasingFunctions;\n}\nfunction replaceStringEasing(transition) {\n    if (typeof transition.ease === \"string\" &&\n        isUnsupportedEase(transition.ease)) {\n        transition.ease = unsupportedEasingFunctions[transition.ease];\n    }\n}\n\nexport { replaceStringEasing };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,cAAc;AAE/D,MAAMC,0BAA0B,GAAG;EAC/BH,UAAU;EACVC,SAAS;EACTC;AACJ,CAAC;AACD,SAASE,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,IAAIF,0BAA0B;AAC5C;AACA,SAASG,mBAAmBA,CAACC,UAAU,EAAE;EACrC,IAAI,OAAOA,UAAU,CAACC,IAAI,KAAK,QAAQ,IACnCJ,iBAAiB,CAACG,UAAU,CAACC,IAAI,CAAC,EAAE;IACpCD,UAAU,CAACC,IAAI,GAAGL,0BAA0B,CAACI,UAAU,CAACC,IAAI,CAAC;EACjE;AACJ;AAEA,SAASF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}