{"ast": null, "code": "import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\nfunction applyGeneratorOptions({\n  type,\n  ...options\n}) {\n  if (isGenerator(type) && supportsLinearEasing()) {\n    return type.applyToOptions(options);\n  } else {\n    options.duration ?? (options.duration = 300);\n    options.ease ?? (options.ease = \"easeOut\");\n  }\n  return options;\n}\nexport { applyGeneratorOptions };", "map": {"version": 3, "names": ["supportsLinearEasing", "isGenerator", "applyGeneratorOptions", "type", "options", "applyToOptions", "duration", "ease"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs"], "sourcesContent": ["import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,WAAW,QAAQ,yCAAyC;AAErE,SAASC,qBAAqBA,CAAC;EAAEC,IAAI;EAAE,GAAGC;AAAQ,CAAC,EAAE;EACjD,IAAIH,WAAW,CAACE,IAAI,CAAC,IAAIH,oBAAoB,CAAC,CAAC,EAAE;IAC7C,OAAOG,IAAI,CAACE,cAAc,CAACD,OAAO,CAAC;EACvC,CAAC,MACI;IACDA,OAAO,CAACE,QAAQ,KAAKF,OAAO,CAACE,QAAQ,GAAG,GAAG,CAAC;IAC5CF,OAAO,CAACG,IAAI,KAAKH,OAAO,CAACG,IAAI,GAAG,SAAS,CAAC;EAC9C;EACA,OAAOH,OAAO;AAClB;AAEA,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}