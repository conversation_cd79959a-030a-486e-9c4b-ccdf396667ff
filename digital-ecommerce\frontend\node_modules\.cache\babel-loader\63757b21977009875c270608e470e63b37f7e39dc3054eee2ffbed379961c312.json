{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\n\n// Gaming Store Theme Context - 2025\n// Provides comprehensive theme management with gaming aesthetics and accessibility\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\n\n// Theme configuration\nconst THEME_CONFIG = {\n  STORAGE_KEY: 'gaming-store-theme',\n  THEMES: {\n    LIGHT: 'light',\n    DARK: 'dark',\n    SYSTEM: 'system'\n  },\n  TRANSITION_DURATION: 300,\n  // ms\n  KEYBOARD_SHORTCUT: 'KeyT' // Ctrl+Shift+T\n};\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [theme, setTheme] = useState(THEME_CONFIG.THEMES.SYSTEM);\n  const [systemTheme, setSystemTheme] = useState(THEME_CONFIG.THEMES.LIGHT);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [isSystemTheme, setIsSystemTheme] = useState(true);\n\n  // Detect system theme preference\n  const detectSystemTheme = useCallback(() => {\n    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.LIGHT;\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    return mediaQuery.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n  }, []);\n\n  // Get effective theme (resolves 'system' to actual theme)\n  const getEffectiveTheme = useCallback(themeValue => {\n    if (themeValue === THEME_CONFIG.THEMES.SYSTEM) {\n      return systemTheme;\n    }\n    return themeValue;\n  }, [systemTheme]);\n\n  // Apply theme to document\n  const applyTheme = useCallback((themeValue, withTransition = true) => {\n    if (typeof document === 'undefined') return;\n    const effectiveTheme = getEffectiveTheme(themeValue);\n    const root = document.documentElement;\n\n    // Add transition class for smooth theme switching\n    if (withTransition) {\n      setIsTransitioning(true);\n      root.classList.add('theme-transition');\n\n      // Remove transition class after animation\n      setTimeout(() => {\n        root.classList.remove('theme-transition');\n        setIsTransitioning(false);\n      }, THEME_CONFIG.TRANSITION_DURATION);\n    }\n\n    // Apply theme attribute\n    root.setAttribute('data-theme', effectiveTheme);\n\n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      const themeColors = {\n        [THEME_CONFIG.THEMES.LIGHT]: '#ffffff',\n        [THEME_CONFIG.THEMES.DARK]: '#0a0a0a'\n      };\n      metaThemeColor.setAttribute('content', themeColors[effectiveTheme]);\n    }\n\n    // Gaming-specific enhancements\n    if (effectiveTheme === THEME_CONFIG.THEMES.DARK) {\n      // Enable gaming mode optimizations\n      root.style.setProperty('--gaming-mode', '1');\n\n      // Enhance RGB effects for dark mode\n      root.classList.add('gaming-dark-mode');\n    } else {\n      root.style.setProperty('--gaming-mode', '0');\n      root.classList.remove('gaming-dark-mode');\n    }\n\n    // Announce theme change to screen readers\n    announceThemeChange(effectiveTheme);\n  }, [getEffectiveTheme]);\n\n  // Announce theme change for accessibility\n  const announceThemeChange = useCallback(effectiveTheme => {\n    if (typeof document === 'undefined') return;\n    const announcement = document.createElement('div');\n    announcement.setAttribute('aria-live', 'polite');\n    announcement.setAttribute('aria-atomic', 'true');\n    announcement.className = 'sr-only';\n    announcement.textContent = `Theme changed to ${effectiveTheme} mode`;\n    document.body.appendChild(announcement);\n\n    // Remove announcement after screen readers have processed it\n    setTimeout(() => {\n      document.body.removeChild(announcement);\n    }, 1000);\n  }, []);\n\n  // Save theme preference to localStorage\n  const saveThemePreference = useCallback(themeValue => {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.setItem(THEME_CONFIG.STORAGE_KEY, themeValue);\n    } catch (error) {\n      console.warn('Failed to save theme preference:', error);\n    }\n  }, []);\n\n  // Load theme preference from localStorage\n  const loadThemePreference = useCallback(() => {\n    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.SYSTEM;\n    try {\n      const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);\n      if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {\n        return saved;\n      }\n    } catch (error) {\n      console.warn('Failed to load theme preference:', error);\n    }\n    return THEME_CONFIG.THEMES.SYSTEM;\n  }, []);\n\n  // Toggle theme function\n  const toggleTheme = useCallback(() => {\n    const currentEffective = getEffectiveTheme(theme);\n    let newTheme;\n    if (theme === THEME_CONFIG.THEMES.SYSTEM) {\n      // If on system, switch to opposite of current system theme\n      newTheme = currentEffective === THEME_CONFIG.THEMES.DARK ? THEME_CONFIG.THEMES.LIGHT : THEME_CONFIG.THEMES.DARK;\n    } else {\n      // If on manual theme, switch to opposite\n      newTheme = theme === THEME_CONFIG.THEMES.DARK ? THEME_CONFIG.THEMES.LIGHT : THEME_CONFIG.THEMES.DARK;\n    }\n    setTheme(newTheme);\n    setIsSystemTheme(false);\n    saveThemePreference(newTheme);\n    applyTheme(newTheme);\n\n    // Gaming feedback - trigger RGB pulse effect\n    triggerGamingFeedback();\n  }, [theme, getEffectiveTheme, saveThemePreference, applyTheme]);\n\n  // Set specific theme\n  const setSpecificTheme = useCallback(newTheme => {\n    if (!Object.values(THEME_CONFIG.THEMES).includes(newTheme)) return;\n    setTheme(newTheme);\n    setIsSystemTheme(newTheme === THEME_CONFIG.THEMES.SYSTEM);\n    saveThemePreference(newTheme);\n    applyTheme(newTheme);\n  }, [saveThemePreference, applyTheme]);\n\n  // Gaming feedback effect\n  const triggerGamingFeedback = useCallback(() => {\n    if (typeof document === 'undefined') return;\n\n    // Add temporary RGB pulse class to body\n    document.body.classList.add('theme-switch-pulse');\n    setTimeout(() => {\n      document.body.classList.remove('theme-switch-pulse');\n    }, 600);\n  }, []);\n\n  // Initialize theme on mount\n  useEffect(() => {\n    const initialSystemTheme = detectSystemTheme();\n    setSystemTheme(initialSystemTheme);\n    const savedTheme = loadThemePreference();\n    setTheme(savedTheme);\n    setIsSystemTheme(savedTheme === THEME_CONFIG.THEMES.SYSTEM);\n\n    // Apply theme without transition on initial load\n    applyTheme(savedTheme, false);\n  }, [detectSystemTheme, loadThemePreference, applyTheme]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleSystemThemeChange = e => {\n      const newSystemTheme = e.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n      setSystemTheme(newSystemTheme);\n\n      // If currently using system theme, apply the change\n      if (theme === THEME_CONFIG.THEMES.SYSTEM) {\n        applyTheme(THEME_CONFIG.THEMES.SYSTEM);\n      }\n    };\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    return () => {\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\n    };\n  }, [theme, applyTheme]);\n\n  // Keyboard shortcut support\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n    const handleKeyDown = event => {\n      // Ctrl+Shift+T to toggle theme\n      if (event.ctrlKey && event.shiftKey && event.code === THEME_CONFIG.KEYBOARD_SHORTCUT) {\n        event.preventDefault();\n        toggleTheme();\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [toggleTheme]);\n\n  // Gaming-specific theme utilities\n  const isGamingMode = getEffectiveTheme(theme) === THEME_CONFIG.THEMES.DARK;\n  const shouldUseRGBEffects = isGamingMode;\n  const contextValue = {\n    // Current theme state\n    theme,\n    systemTheme,\n    effectiveTheme: getEffectiveTheme(theme),\n    isSystemTheme,\n    isTransitioning,\n    // Theme actions\n    toggleTheme,\n    setTheme: setSpecificTheme,\n    // Gaming-specific\n    isGamingMode,\n    shouldUseRGBEffects,\n    // Utilities\n    themes: THEME_CONFIG.THEMES,\n    // Accessibility\n    announceThemeChange: message => announceThemeChange(message)\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use theme context\n_s(ThemeProvider, \"+wbnUUs1a9/T18L4PDXhV4gRyFU=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Gaming-specific theme utilities\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const useGamingTheme = () => {\n  _s3();\n  const theme = useTheme();\n  return {\n    ...theme,\n    // Gaming-specific helpers\n    getRGBGlow: (intensity = 'normal') => {\n      if (!theme.shouldUseRGBEffects) return '';\n      const glowMap = {\n        subtle: 'var(--rgb-glow)',\n        normal: 'var(--rgb-glow-strong)',\n        intense: '0 0 50px rgba(255, 179, 102, 0.8)'\n      };\n      return glowMap[intensity] || glowMap.normal;\n    },\n    getGamingAccent: (type = 'primary') => {\n      const accentMap = {\n        primary: 'var(--accent-primary)',\n        secondary: 'var(--accent-secondary)',\n        tertiary: 'var(--accent-tertiary)',\n        neon: 'var(--accent-secondary)',\n        cyber: 'var(--accent-tertiary)'\n      };\n      return accentMap[type] || accentMap.primary;\n    }\n  };\n};\n\n// Theme detection utility for SSR\n_s3(useGamingTheme, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\nexport const getInitialTheme = () => {\n  if (typeof window === 'undefined') return THEME_CONFIG.THEMES.DARK; // Default for SSR\n\n  try {\n    const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);\n    if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {\n      return saved;\n    }\n  } catch (error) {\n    console.warn('Failed to load theme preference:', error);\n  }\n\n  // Fallback to system preference or dark mode for gaming aesthetic\n  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n  return systemPrefersDark ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n};\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useCallback", "jsxDEV", "_jsxDEV", "ThemeContext", "THEME_CONFIG", "STORAGE_KEY", "THEMES", "LIGHT", "DARK", "SYSTEM", "TRANSITION_DURATION", "KEYBOARD_SHORTCUT", "ThemeProvider", "children", "_s", "theme", "setTheme", "systemTheme", "setSystemTheme", "isTransitioning", "setIsTransitioning", "isSystemTheme", "setIsSystemTheme", "detectSystemTheme", "window", "mediaQuery", "matchMedia", "matches", "getEffectiveTheme", "themeValue", "applyTheme", "withTransition", "document", "effectiveTheme", "root", "documentElement", "classList", "add", "setTimeout", "remove", "setAttribute", "metaThemeColor", "querySelector", "themeColors", "style", "setProperty", "announceThemeChange", "announcement", "createElement", "className", "textContent", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "saveThemePreference", "localStorage", "setItem", "error", "console", "warn", "loadThemePreference", "saved", "getItem", "Object", "values", "includes", "toggleTheme", "currentEffective", "newTheme", "triggerGamingFeedback", "setSpecificTheme", "initialSystemTheme", "savedTheme", "handleSystemThemeChange", "e", "newSystemTheme", "addEventListener", "removeEventListener", "handleKeyDown", "event", "ctrl<PERSON>ey", "shift<PERSON>ey", "code", "preventDefault", "isGamingMode", "shouldUseRGBEffects", "contextValue", "themes", "message", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "useGamingTheme", "_s3", "getRGBGlow", "intensity", "glowMap", "subtle", "normal", "intense", "getGamingAccent", "type", "accentMap", "primary", "secondary", "tertiary", "neon", "cyber", "getInitialTheme", "systemPrefersDark", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\n\n// Gaming Store Theme Context - 2025\n// Provides comprehensive theme management with gaming aesthetics and accessibility\n\nconst ThemeContext = createContext();\n\n// Theme configuration\nconst THEME_CONFIG = {\n  STORAGE_KEY: 'gaming-store-theme',\n  THEMES: {\n    LIGHT: 'light',\n    DARK: 'dark',\n    SYSTEM: 'system'\n  },\n  TRANSITION_DURATION: 300, // ms\n  KEYBOARD_SHORTCUT: 'KeyT' // Ctrl+Shift+T\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState(THEME_CONFIG.THEMES.SYSTEM);\n  const [systemTheme, setSystemTheme] = useState(THEME_CONFIG.THEMES.LIGHT);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [isSystemTheme, setIsSystemTheme] = useState(true);\n\n  // Detect system theme preference\n  const detectSystemTheme = useCallback(() => {\n    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.LIGHT;\n    \n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    return mediaQuery.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n  }, []);\n\n  // Get effective theme (resolves 'system' to actual theme)\n  const getEffectiveTheme = useCallback((themeValue) => {\n    if (themeValue === THEME_CONFIG.THEMES.SYSTEM) {\n      return systemTheme;\n    }\n    return themeValue;\n  }, [systemTheme]);\n\n  // Apply theme to document\n  const applyTheme = useCallback((themeValue, withTransition = true) => {\n    if (typeof document === 'undefined') return;\n\n    const effectiveTheme = getEffectiveTheme(themeValue);\n    const root = document.documentElement;\n\n    // Add transition class for smooth theme switching\n    if (withTransition) {\n      setIsTransitioning(true);\n      root.classList.add('theme-transition');\n      \n      // Remove transition class after animation\n      setTimeout(() => {\n        root.classList.remove('theme-transition');\n        setIsTransitioning(false);\n      }, THEME_CONFIG.TRANSITION_DURATION);\n    }\n\n    // Apply theme attribute\n    root.setAttribute('data-theme', effectiveTheme);\n\n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      const themeColors = {\n        [THEME_CONFIG.THEMES.LIGHT]: '#ffffff',\n        [THEME_CONFIG.THEMES.DARK]: '#0a0a0a'\n      };\n      metaThemeColor.setAttribute('content', themeColors[effectiveTheme]);\n    }\n\n    // Gaming-specific enhancements\n    if (effectiveTheme === THEME_CONFIG.THEMES.DARK) {\n      // Enable gaming mode optimizations\n      root.style.setProperty('--gaming-mode', '1');\n      \n      // Enhance RGB effects for dark mode\n      root.classList.add('gaming-dark-mode');\n    } else {\n      root.style.setProperty('--gaming-mode', '0');\n      root.classList.remove('gaming-dark-mode');\n    }\n\n    // Announce theme change to screen readers\n    announceThemeChange(effectiveTheme);\n  }, [getEffectiveTheme]);\n\n  // Announce theme change for accessibility\n  const announceThemeChange = useCallback((effectiveTheme) => {\n    if (typeof document === 'undefined') return;\n\n    const announcement = document.createElement('div');\n    announcement.setAttribute('aria-live', 'polite');\n    announcement.setAttribute('aria-atomic', 'true');\n    announcement.className = 'sr-only';\n    announcement.textContent = `Theme changed to ${effectiveTheme} mode`;\n    \n    document.body.appendChild(announcement);\n    \n    // Remove announcement after screen readers have processed it\n    setTimeout(() => {\n      document.body.removeChild(announcement);\n    }, 1000);\n  }, []);\n\n  // Save theme preference to localStorage\n  const saveThemePreference = useCallback((themeValue) => {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      localStorage.setItem(THEME_CONFIG.STORAGE_KEY, themeValue);\n    } catch (error) {\n      console.warn('Failed to save theme preference:', error);\n    }\n  }, []);\n\n  // Load theme preference from localStorage\n  const loadThemePreference = useCallback(() => {\n    if (typeof window === 'undefined') return THEME_CONFIG.THEMES.SYSTEM;\n    \n    try {\n      const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);\n      if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {\n        return saved;\n      }\n    } catch (error) {\n      console.warn('Failed to load theme preference:', error);\n    }\n    \n    return THEME_CONFIG.THEMES.SYSTEM;\n  }, []);\n\n  // Toggle theme function\n  const toggleTheme = useCallback(() => {\n    const currentEffective = getEffectiveTheme(theme);\n    let newTheme;\n\n    if (theme === THEME_CONFIG.THEMES.SYSTEM) {\n      // If on system, switch to opposite of current system theme\n      newTheme = currentEffective === THEME_CONFIG.THEMES.DARK \n        ? THEME_CONFIG.THEMES.LIGHT \n        : THEME_CONFIG.THEMES.DARK;\n    } else {\n      // If on manual theme, switch to opposite\n      newTheme = theme === THEME_CONFIG.THEMES.DARK \n        ? THEME_CONFIG.THEMES.LIGHT \n        : THEME_CONFIG.THEMES.DARK;\n    }\n\n    setTheme(newTheme);\n    setIsSystemTheme(false);\n    saveThemePreference(newTheme);\n    applyTheme(newTheme);\n\n    // Gaming feedback - trigger RGB pulse effect\n    triggerGamingFeedback();\n  }, [theme, getEffectiveTheme, saveThemePreference, applyTheme]);\n\n  // Set specific theme\n  const setSpecificTheme = useCallback((newTheme) => {\n    if (!Object.values(THEME_CONFIG.THEMES).includes(newTheme)) return;\n\n    setTheme(newTheme);\n    setIsSystemTheme(newTheme === THEME_CONFIG.THEMES.SYSTEM);\n    saveThemePreference(newTheme);\n    applyTheme(newTheme);\n  }, [saveThemePreference, applyTheme]);\n\n  // Gaming feedback effect\n  const triggerGamingFeedback = useCallback(() => {\n    if (typeof document === 'undefined') return;\n\n    // Add temporary RGB pulse class to body\n    document.body.classList.add('theme-switch-pulse');\n    \n    setTimeout(() => {\n      document.body.classList.remove('theme-switch-pulse');\n    }, 600);\n  }, []);\n\n  // Initialize theme on mount\n  useEffect(() => {\n    const initialSystemTheme = detectSystemTheme();\n    setSystemTheme(initialSystemTheme);\n\n    const savedTheme = loadThemePreference();\n    setTheme(savedTheme);\n    setIsSystemTheme(savedTheme === THEME_CONFIG.THEMES.SYSTEM);\n    \n    // Apply theme without transition on initial load\n    applyTheme(savedTheme, false);\n  }, [detectSystemTheme, loadThemePreference, applyTheme]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleSystemThemeChange = (e) => {\n      const newSystemTheme = e.matches ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n      setSystemTheme(newSystemTheme);\n      \n      // If currently using system theme, apply the change\n      if (theme === THEME_CONFIG.THEMES.SYSTEM) {\n        applyTheme(THEME_CONFIG.THEMES.SYSTEM);\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\n    };\n  }, [theme, applyTheme]);\n\n  // Keyboard shortcut support\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleKeyDown = (event) => {\n      // Ctrl+Shift+T to toggle theme\n      if (event.ctrlKey && event.shiftKey && event.code === THEME_CONFIG.KEYBOARD_SHORTCUT) {\n        event.preventDefault();\n        toggleTheme();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    \n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [toggleTheme]);\n\n  // Gaming-specific theme utilities\n  const isGamingMode = getEffectiveTheme(theme) === THEME_CONFIG.THEMES.DARK;\n  const shouldUseRGBEffects = isGamingMode;\n\n  const contextValue = {\n    // Current theme state\n    theme,\n    systemTheme,\n    effectiveTheme: getEffectiveTheme(theme),\n    isSystemTheme,\n    isTransitioning,\n    \n    // Theme actions\n    toggleTheme,\n    setTheme: setSpecificTheme,\n    \n    // Gaming-specific\n    isGamingMode,\n    shouldUseRGBEffects,\n    \n    // Utilities\n    themes: THEME_CONFIG.THEMES,\n    \n    // Accessibility\n    announceThemeChange: (message) => announceThemeChange(message)\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n// Custom hook to use theme context\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  \n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  \n  return context;\n};\n\n// Gaming-specific theme utilities\nexport const useGamingTheme = () => {\n  const theme = useTheme();\n  \n  return {\n    ...theme,\n    // Gaming-specific helpers\n    getRGBGlow: (intensity = 'normal') => {\n      if (!theme.shouldUseRGBEffects) return '';\n      \n      const glowMap = {\n        subtle: 'var(--rgb-glow)',\n        normal: 'var(--rgb-glow-strong)',\n        intense: '0 0 50px rgba(255, 179, 102, 0.8)'\n      };\n      \n      return glowMap[intensity] || glowMap.normal;\n    },\n    \n    getGamingAccent: (type = 'primary') => {\n      const accentMap = {\n        primary: 'var(--accent-primary)',\n        secondary: 'var(--accent-secondary)',\n        tertiary: 'var(--accent-tertiary)',\n        neon: 'var(--accent-secondary)',\n        cyber: 'var(--accent-tertiary)'\n      };\n      \n      return accentMap[type] || accentMap.primary;\n    }\n  };\n};\n\n// Theme detection utility for SSR\nexport const getInitialTheme = () => {\n  if (typeof window === 'undefined') return THEME_CONFIG.THEMES.DARK; // Default for SSR\n  \n  try {\n    const saved = localStorage.getItem(THEME_CONFIG.STORAGE_KEY);\n    if (saved && Object.values(THEME_CONFIG.THEMES).includes(saved)) {\n      return saved;\n    }\n  } catch (error) {\n    console.warn('Failed to load theme preference:', error);\n  }\n  \n  // Fallback to system preference or dark mode for gaming aesthetic\n  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n  return systemPrefersDark ? THEME_CONFIG.THEMES.DARK : THEME_CONFIG.THEMES.LIGHT;\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;;AAE1F;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,YAAY,gBAAGP,aAAa,CAAC,CAAC;;AAEpC;AACA,MAAMQ,YAAY,GAAG;EACnBC,WAAW,EAAE,oBAAoB;EACjCC,MAAM,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,mBAAmB,EAAE,GAAG;EAAE;EAC1BC,iBAAiB,EAAE,MAAM,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAACK,YAAY,CAACE,MAAM,CAACG,MAAM,CAAC;EAC9D,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAACK,YAAY,CAACE,MAAM,CAACC,KAAK,CAAC;EACzE,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMwB,iBAAiB,GAAGvB,WAAW,CAAC,MAAM;IAC1C,IAAI,OAAOwB,MAAM,KAAK,WAAW,EAAE,OAAOpB,YAAY,CAACE,MAAM,CAACC,KAAK;IAEnE,MAAMkB,UAAU,GAAGD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;IACpE,OAAOD,UAAU,CAACE,OAAO,GAAGvB,YAAY,CAACE,MAAM,CAACE,IAAI,GAAGJ,YAAY,CAACE,MAAM,CAACC,KAAK;EAClF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,iBAAiB,GAAG5B,WAAW,CAAE6B,UAAU,IAAK;IACpD,IAAIA,UAAU,KAAKzB,YAAY,CAACE,MAAM,CAACG,MAAM,EAAE;MAC7C,OAAOQ,WAAW;IACpB;IACA,OAAOY,UAAU;EACnB,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMa,UAAU,GAAG9B,WAAW,CAAC,CAAC6B,UAAU,EAAEE,cAAc,GAAG,IAAI,KAAK;IACpE,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IAErC,MAAMC,cAAc,GAAGL,iBAAiB,CAACC,UAAU,CAAC;IACpD,MAAMK,IAAI,GAAGF,QAAQ,CAACG,eAAe;;IAErC;IACA,IAAIJ,cAAc,EAAE;MAClBX,kBAAkB,CAAC,IAAI,CAAC;MACxBc,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;;MAEtC;MACAC,UAAU,CAAC,MAAM;QACfJ,IAAI,CAACE,SAAS,CAACG,MAAM,CAAC,kBAAkB,CAAC;QACzCnB,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,EAAEhB,YAAY,CAACM,mBAAmB,CAAC;IACtC;;IAEA;IACAwB,IAAI,CAACM,YAAY,CAAC,YAAY,EAAEP,cAAc,CAAC;;IAE/C;IACA,MAAMQ,cAAc,GAAGT,QAAQ,CAACU,aAAa,CAAC,0BAA0B,CAAC;IACzE,IAAID,cAAc,EAAE;MAClB,MAAME,WAAW,GAAG;QAClB,CAACvC,YAAY,CAACE,MAAM,CAACC,KAAK,GAAG,SAAS;QACtC,CAACH,YAAY,CAACE,MAAM,CAACE,IAAI,GAAG;MAC9B,CAAC;MACDiC,cAAc,CAACD,YAAY,CAAC,SAAS,EAAEG,WAAW,CAACV,cAAc,CAAC,CAAC;IACrE;;IAEA;IACA,IAAIA,cAAc,KAAK7B,YAAY,CAACE,MAAM,CAACE,IAAI,EAAE;MAC/C;MACA0B,IAAI,CAACU,KAAK,CAACC,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC;;MAE5C;MACAX,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACxC,CAAC,MAAM;MACLH,IAAI,CAACU,KAAK,CAACC,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC;MAC5CX,IAAI,CAACE,SAAS,CAACG,MAAM,CAAC,kBAAkB,CAAC;IAC3C;;IAEA;IACAO,mBAAmB,CAACb,cAAc,CAAC;EACrC,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMkB,mBAAmB,GAAG9C,WAAW,CAAEiC,cAAc,IAAK;IAC1D,IAAI,OAAOD,QAAQ,KAAK,WAAW,EAAE;IAErC,MAAMe,YAAY,GAAGf,QAAQ,CAACgB,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACP,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAChDO,YAAY,CAACP,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChDO,YAAY,CAACE,SAAS,GAAG,SAAS;IAClCF,YAAY,CAACG,WAAW,GAAG,oBAAoBjB,cAAc,OAAO;IAEpED,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;;IAEvC;IACAT,UAAU,CAAC,MAAM;MACfN,QAAQ,CAACmB,IAAI,CAACE,WAAW,CAACN,YAAY,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,mBAAmB,GAAGtD,WAAW,CAAE6B,UAAU,IAAK;IACtD,IAAI,OAAOL,MAAM,KAAK,WAAW,EAAE;IAEnC,IAAI;MACF+B,YAAY,CAACC,OAAO,CAACpD,YAAY,CAACC,WAAW,EAAEwB,UAAU,CAAC;IAC5D,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IACzD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,mBAAmB,GAAG5D,WAAW,CAAC,MAAM;IAC5C,IAAI,OAAOwB,MAAM,KAAK,WAAW,EAAE,OAAOpB,YAAY,CAACE,MAAM,CAACG,MAAM;IAEpE,IAAI;MACF,MAAMoD,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC1D,YAAY,CAACC,WAAW,CAAC;MAC5D,IAAIwD,KAAK,IAAIE,MAAM,CAACC,MAAM,CAAC5D,YAAY,CAACE,MAAM,CAAC,CAAC2D,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAC/D,OAAOA,KAAK;MACd;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IACzD;IAEA,OAAOrD,YAAY,CAACE,MAAM,CAACG,MAAM;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyD,WAAW,GAAGlE,WAAW,CAAC,MAAM;IACpC,MAAMmE,gBAAgB,GAAGvC,iBAAiB,CAACb,KAAK,CAAC;IACjD,IAAIqD,QAAQ;IAEZ,IAAIrD,KAAK,KAAKX,YAAY,CAACE,MAAM,CAACG,MAAM,EAAE;MACxC;MACA2D,QAAQ,GAAGD,gBAAgB,KAAK/D,YAAY,CAACE,MAAM,CAACE,IAAI,GACpDJ,YAAY,CAACE,MAAM,CAACC,KAAK,GACzBH,YAAY,CAACE,MAAM,CAACE,IAAI;IAC9B,CAAC,MAAM;MACL;MACA4D,QAAQ,GAAGrD,KAAK,KAAKX,YAAY,CAACE,MAAM,CAACE,IAAI,GACzCJ,YAAY,CAACE,MAAM,CAACC,KAAK,GACzBH,YAAY,CAACE,MAAM,CAACE,IAAI;IAC9B;IAEAQ,QAAQ,CAACoD,QAAQ,CAAC;IAClB9C,gBAAgB,CAAC,KAAK,CAAC;IACvBgC,mBAAmB,CAACc,QAAQ,CAAC;IAC7BtC,UAAU,CAACsC,QAAQ,CAAC;;IAEpB;IACAC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACtD,KAAK,EAAEa,iBAAiB,EAAE0B,mBAAmB,EAAExB,UAAU,CAAC,CAAC;;EAE/D;EACA,MAAMwC,gBAAgB,GAAGtE,WAAW,CAAEoE,QAAQ,IAAK;IACjD,IAAI,CAACL,MAAM,CAACC,MAAM,CAAC5D,YAAY,CAACE,MAAM,CAAC,CAAC2D,QAAQ,CAACG,QAAQ,CAAC,EAAE;IAE5DpD,QAAQ,CAACoD,QAAQ,CAAC;IAClB9C,gBAAgB,CAAC8C,QAAQ,KAAKhE,YAAY,CAACE,MAAM,CAACG,MAAM,CAAC;IACzD6C,mBAAmB,CAACc,QAAQ,CAAC;IAC7BtC,UAAU,CAACsC,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACd,mBAAmB,EAAExB,UAAU,CAAC,CAAC;;EAErC;EACA,MAAMuC,qBAAqB,GAAGrE,WAAW,CAAC,MAAM;IAC9C,IAAI,OAAOgC,QAAQ,KAAK,WAAW,EAAE;;IAErC;IACAA,QAAQ,CAACmB,IAAI,CAACf,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAEjDC,UAAU,CAAC,MAAM;MACfN,QAAQ,CAACmB,IAAI,CAACf,SAAS,CAACG,MAAM,CAAC,oBAAoB,CAAC;IACtD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzC,SAAS,CAAC,MAAM;IACd,MAAMyE,kBAAkB,GAAGhD,iBAAiB,CAAC,CAAC;IAC9CL,cAAc,CAACqD,kBAAkB,CAAC;IAElC,MAAMC,UAAU,GAAGZ,mBAAmB,CAAC,CAAC;IACxC5C,QAAQ,CAACwD,UAAU,CAAC;IACpBlD,gBAAgB,CAACkD,UAAU,KAAKpE,YAAY,CAACE,MAAM,CAACG,MAAM,CAAC;;IAE3D;IACAqB,UAAU,CAAC0C,UAAU,EAAE,KAAK,CAAC;EAC/B,CAAC,EAAE,CAACjD,iBAAiB,EAAEqC,mBAAmB,EAAE9B,UAAU,CAAC,CAAC;;EAExD;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI,OAAO0B,MAAM,KAAK,WAAW,EAAE;IAEnC,MAAMC,UAAU,GAAGD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;IAEpE,MAAM+C,uBAAuB,GAAIC,CAAC,IAAK;MACrC,MAAMC,cAAc,GAAGD,CAAC,CAAC/C,OAAO,GAAGvB,YAAY,CAACE,MAAM,CAACE,IAAI,GAAGJ,YAAY,CAACE,MAAM,CAACC,KAAK;MACvFW,cAAc,CAACyD,cAAc,CAAC;;MAE9B;MACA,IAAI5D,KAAK,KAAKX,YAAY,CAACE,MAAM,CAACG,MAAM,EAAE;QACxCqB,UAAU,CAAC1B,YAAY,CAACE,MAAM,CAACG,MAAM,CAAC;MACxC;IACF,CAAC;IAEDgB,UAAU,CAACmD,gBAAgB,CAAC,QAAQ,EAAEH,uBAAuB,CAAC;IAE9D,OAAO,MAAM;MACXhD,UAAU,CAACoD,mBAAmB,CAAC,QAAQ,EAAEJ,uBAAuB,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,CAAC1D,KAAK,EAAEe,UAAU,CAAC,CAAC;;EAEvB;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI,OAAO0B,MAAM,KAAK,WAAW,EAAE;IAEnC,MAAMsD,aAAa,GAAIC,KAAK,IAAK;MAC/B;MACA,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,IAAI,KAAK9E,YAAY,CAACO,iBAAiB,EAAE;QACpFoE,KAAK,CAACI,cAAc,CAAC,CAAC;QACtBjB,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAEDlC,QAAQ,CAAC4C,gBAAgB,CAAC,SAAS,EAAEE,aAAa,CAAC;IAEnD,OAAO,MAAM;MACX9C,QAAQ,CAAC6C,mBAAmB,CAAC,SAAS,EAAEC,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMkB,YAAY,GAAGxD,iBAAiB,CAACb,KAAK,CAAC,KAAKX,YAAY,CAACE,MAAM,CAACE,IAAI;EAC1E,MAAM6E,mBAAmB,GAAGD,YAAY;EAExC,MAAME,YAAY,GAAG;IACnB;IACAvE,KAAK;IACLE,WAAW;IACXgB,cAAc,EAAEL,iBAAiB,CAACb,KAAK,CAAC;IACxCM,aAAa;IACbF,eAAe;IAEf;IACA+C,WAAW;IACXlD,QAAQ,EAAEsD,gBAAgB;IAE1B;IACAc,YAAY;IACZC,mBAAmB;IAEnB;IACAE,MAAM,EAAEnF,YAAY,CAACE,MAAM;IAE3B;IACAwC,mBAAmB,EAAG0C,OAAO,IAAK1C,mBAAmB,CAAC0C,OAAO;EAC/D,CAAC;EAED,oBACEtF,OAAA,CAACC,YAAY,CAACsF,QAAQ;IAACC,KAAK,EAAEJ,YAAa;IAAAzE,QAAA,EACxCA;EAAQ;IAAA8E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;;AAED;AAAAhF,EAAA,CA5PaF,aAAa;AAAAmF,EAAA,GAAbnF,aAAa;AA6P1B,OAAO,MAAMoF,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGrG,UAAU,CAACM,YAAY,CAAC;EAExC,IAAI,CAAC+F,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EAEA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CAVaD,QAAQ;AAWrB,OAAO,MAAMI,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClC,MAAMtF,KAAK,GAAGiF,QAAQ,CAAC,CAAC;EAExB,OAAO;IACL,GAAGjF,KAAK;IACR;IACAuF,UAAU,EAAEA,CAACC,SAAS,GAAG,QAAQ,KAAK;MACpC,IAAI,CAACxF,KAAK,CAACsE,mBAAmB,EAAE,OAAO,EAAE;MAEzC,MAAMmB,OAAO,GAAG;QACdC,MAAM,EAAE,iBAAiB;QACzBC,MAAM,EAAE,wBAAwB;QAChCC,OAAO,EAAE;MACX,CAAC;MAED,OAAOH,OAAO,CAACD,SAAS,CAAC,IAAIC,OAAO,CAACE,MAAM;IAC7C,CAAC;IAEDE,eAAe,EAAEA,CAACC,IAAI,GAAG,SAAS,KAAK;MACrC,MAAMC,SAAS,GAAG;QAChBC,OAAO,EAAE,uBAAuB;QAChCC,SAAS,EAAE,yBAAyB;QACpCC,QAAQ,EAAE,wBAAwB;QAClCC,IAAI,EAAE,yBAAyB;QAC/BC,KAAK,EAAE;MACT,CAAC;MAED,OAAOL,SAAS,CAACD,IAAI,CAAC,IAAIC,SAAS,CAACC,OAAO;IAC7C;EACF,CAAC;AACH,CAAC;;AAED;AAAAV,GAAA,CAhCaD,cAAc;EAAA,QACXJ,QAAQ;AAAA;AAgCxB,OAAO,MAAMoB,eAAe,GAAGA,CAAA,KAAM;EACnC,IAAI,OAAO5F,MAAM,KAAK,WAAW,EAAE,OAAOpB,YAAY,CAACE,MAAM,CAACE,IAAI,CAAC,CAAC;;EAEpE,IAAI;IACF,MAAMqD,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC1D,YAAY,CAACC,WAAW,CAAC;IAC5D,IAAIwD,KAAK,IAAIE,MAAM,CAACC,MAAM,CAAC5D,YAAY,CAACE,MAAM,CAAC,CAAC2D,QAAQ,CAACJ,KAAK,CAAC,EAAE;MAC/D,OAAOA,KAAK;IACd;EACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;EACzD;;EAEA;EACA,MAAM4D,iBAAiB,GAAG7F,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;EACnF,OAAO0F,iBAAiB,GAAGjH,YAAY,CAACE,MAAM,CAACE,IAAI,GAAGJ,YAAY,CAACE,MAAM,CAACC,KAAK;AACjF,CAAC;AAED,eAAeJ,YAAY;AAAC,IAAA4F,EAAA;AAAAuB,YAAA,CAAAvB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}