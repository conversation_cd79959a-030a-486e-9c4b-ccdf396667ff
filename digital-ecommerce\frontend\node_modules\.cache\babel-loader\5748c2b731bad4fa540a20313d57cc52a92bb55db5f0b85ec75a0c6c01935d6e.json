{"ast": null, "code": "const animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement = \"\") => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n  const map = animationMaps.get(element) || new Map();\n  animationMaps.set(element, map);\n  return map;\n}\nexport { animationMapKey, getAnimationMap };", "map": {"version": 3, "names": ["animationMaps", "WeakMap", "animationMapKey", "name", "pseudoElement", "getAnimationMap", "element", "map", "get", "Map", "set"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/utils/active-animations.mjs"], "sourcesContent": ["const animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement = \"\") => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n\nexport { animationMapKey, getAnimationMap };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,aAAa,GAAG,EAAE,KAAK,GAAGD,IAAI,IAAIC,aAAa,EAAE;AAChF,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,MAAMC,GAAG,GAAGP,aAAa,CAACQ,GAAG,CAACF,OAAO,CAAC,IAAI,IAAIG,GAAG,CAAC,CAAC;EACnDT,aAAa,CAACU,GAAG,CAACJ,OAAO,EAAEC,GAAG,CAAC;EAC/B,OAAOA,GAAG;AACd;AAEA,SAASL,eAAe,EAAEG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}