{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ConfirmationModal.js\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmationModal = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title = \"Confirm Action\",\n  message = \"Are you sure you want to proceed?\",\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  type = \"warning\" // warning, danger, info\n}) => {\n  if (!isOpen) return null;\n  const getIconColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'text-red-600';\n      case 'info':\n        return 'text-blue-600';\n      default:\n        return 'text-yellow-600';\n    }\n  };\n  const getConfirmButtonColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';\n      case 'info':\n        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';\n      default:\n        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      exit: {\n        opacity: 0\n      },\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0.9,\n          opacity: 0\n        },\n        onClick: e => e.stopPropagation(),\n        className: \"w-full max-w-md bg-white rounded-xl shadow-xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-2 rounded-full bg-gray-100`,\n              children: /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                className: `w-6 h-6 ${getIconColor()}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n            children: cancelText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onConfirm,\n            className: `px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${getConfirmButtonColor()}`,\n            children: confirmText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmationModal;\nexport default ConfirmationModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmationModal\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "ExclamationTriangleIcon", "XMarkIcon", "jsxDEV", "_jsxDEV", "ConfirmationModal", "isOpen", "onClose", "onConfirm", "title", "message", "confirmText", "cancelText", "type", "getIconColor", "getConfirmButtonColor", "children", "div", "initial", "opacity", "animate", "exit", "className", "onClick", "scale", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ConfirmationModal.js"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ExclamationTriangleIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\nconst ConfirmationModal = ({ \n  isOpen, \n  onClose, \n  onConfirm, \n  title = \"Confirm Action\", \n  message = \"Are you sure you want to proceed?\", \n  confirmText = \"Confirm\", \n  cancelText = \"Cancel\",\n  type = \"warning\" // warning, danger, info\n}) => {\n  if (!isOpen) return null;\n\n  const getIconColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'text-red-600';\n      case 'info':\n        return 'text-blue-600';\n      default:\n        return 'text-yellow-600';\n    }\n  };\n\n  const getConfirmButtonColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';\n      case 'info':\n        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';\n      default:\n        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          onClick={(e) => e.stopPropagation()}\n          className=\"w-full max-w-md bg-white rounded-xl shadow-xl overflow-hidden\"\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div className=\"flex items-center space-x-3\">\n              <div className={`p-2 rounded-full bg-gray-100`}>\n                <ExclamationTriangleIcon className={`w-6 h-6 ${getIconColor()}`} />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                {title}\n              </h3>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            <p className=\"text-gray-600\">\n              {message}\n            </p>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\"\n            >\n              {cancelText}\n            </button>\n            <button\n              onClick={onConfirm}\n              className={`px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${getConfirmButtonColor()}`}\n            >\n              {confirmText}\n            </button>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default ConfirmationModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,uBAAuB,EACvBC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,KAAK,GAAG,gBAAgB;EACxBC,OAAO,GAAG,mCAAmC;EAC7CC,WAAW,GAAG,SAAS;EACvBC,UAAU,GAAG,QAAQ;EACrBC,IAAI,GAAG,SAAS,CAAC;AACnB,CAAC,KAAK;EACJ,IAAI,CAACP,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQD,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,eAAe;MACxB;QACE,OAAO,iBAAiB;IAC5B;EACF,CAAC;EAED,MAAME,qBAAqB,GAAGA,CAAA,KAAM;IAClC,QAAQF,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,gDAAgD;MACzD,KAAK,MAAM;QACT,OAAO,mDAAmD;MAC5D;QACE,OAAO,yDAAyD;IACpE;EACF,CAAC;EAED,oBACET,OAAA,CAACJ,eAAe;IAAAgB,QAAA,eACdZ,OAAA,CAACL,MAAM,CAACkB,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,IAAI,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACrBG,SAAS,EAAC,gFAAgF;MAC1FC,OAAO,EAAEhB,OAAQ;MAAAS,QAAA,eAEjBZ,OAAA,CAACL,MAAM,CAACkB,GAAG;QACTC,OAAO,EAAE;UAAEM,KAAK,EAAE,GAAG;UAAEL,OAAO,EAAE;QAAE,CAAE;QACpCC,OAAO,EAAE;UAAEI,KAAK,EAAE,CAAC;UAAEL,OAAO,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEG,KAAK,EAAE,GAAG;UAAEL,OAAO,EAAE;QAAE,CAAE;QACjCI,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;QACpCJ,SAAS,EAAC,+DAA+D;QAAAN,QAAA,gBAGzEZ,OAAA;UAAKkB,SAAS,EAAC,gEAAgE;UAAAN,QAAA,gBAC7EZ,OAAA;YAAKkB,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBAC1CZ,OAAA;cAAKkB,SAAS,EAAE,8BAA+B;cAAAN,QAAA,eAC7CZ,OAAA,CAACH,uBAAuB;gBAACqB,SAAS,EAAE,WAAWR,YAAY,CAAC,CAAC;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN1B,OAAA;cAAIkB,SAAS,EAAC,qCAAqC;cAAAN,QAAA,EAChDP;YAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN1B,OAAA;YACEmB,OAAO,EAAEhB,OAAQ;YACjBe,SAAS,EAAC,oEAAoE;YAAAN,QAAA,eAE9EZ,OAAA,CAACF,SAAS;cAACoB,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1B,OAAA;UAAKkB,SAAS,EAAC,KAAK;UAAAN,QAAA,eAClBZ,OAAA;YAAGkB,SAAS,EAAC,eAAe;YAAAN,QAAA,EACzBN;UAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN1B,OAAA;UAAKkB,SAAS,EAAC,iFAAiF;UAAAN,QAAA,gBAC9FZ,OAAA;YACEmB,OAAO,EAAEhB,OAAQ;YACjBe,SAAS,EAAC,iLAAiL;YAAAN,QAAA,EAE1LJ;UAAU;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACT1B,OAAA;YACEmB,OAAO,EAAEf,SAAU;YACnBc,SAAS,EAAE,2GAA2GP,qBAAqB,CAAC,CAAC,EAAG;YAAAC,QAAA,EAE/IL;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAACC,EAAA,GA9FI1B,iBAAiB;AAgGvB,eAAeA,iBAAiB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}