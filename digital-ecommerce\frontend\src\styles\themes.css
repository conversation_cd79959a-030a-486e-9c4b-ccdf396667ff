/* Gaming Store Theme System - 2025 */
/* Preserves all text properties while enabling seamless theme switching */

/* ===== FONT PRESERVATION ===== */
/* Lock font properties to prevent theme changes from affecting text appearance */
:root {
  /* Font System - Never changes with theme */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-gaming: 'Orbitron', 'Courier New', monospace;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
}

/* ===== LIGHT THEME (DEFAULT) ===== */
:root {
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-elevated: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;
  
  /* Gaming Accent Colors */
  --accent-primary: #FFB366; /* Gaming Orange */
  --accent-secondary: #00ff88; /* Neon Green */
  --accent-tertiary: #00d4ff; /* Cyber Blue */
  --accent-purple: #a855f7; /* RGB Purple */
  --accent-pink: #ec4899; /* RGB Pink */
  
  /* Interactive States */
  --accent-hover: #ff9f47;
  --accent-active: #e6a159;
  --accent-focus: #FFB366;
  
  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #FFB366;
  --border-error: #ef4444;
  --border-success: #22c55e;
  
  /* Status Colors */
  --status-success: #22c55e;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  
  /* Gaming RGB Effects */
  --rgb-glow: 0 0 20px rgba(255, 179, 102, 0.3);
  --rgb-glow-strong: 0 0 30px rgba(255, 179, 102, 0.5);
  --neon-glow: 0 0 15px rgba(0, 255, 136, 0.4);
  --cyber-glow: 0 0 15px rgba(0, 212, 255, 0.4);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-gaming: 0 8px 32px rgba(255, 179, 102, 0.15);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  --transition-theme: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
  /* Background Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --bg-card: #1e1e1e;
  --bg-elevated: #262626;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-inverse: #0f172a;
  
  /* Gaming Accent Colors - Slightly more vibrant for dark mode */
  --accent-primary: #FFB366;
  --accent-secondary: #00ff88;
  --accent-tertiary: #00d4ff;
  --accent-purple: #c084fc;
  --accent-pink: #f472b6;
  
  /* Interactive States */
  --accent-hover: #ffc785;
  --accent-active: #ff9f47;
  --accent-focus: #FFB366;
  
  /* Border Colors */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-focus: #FFB366;
  --border-error: #f87171;
  --border-success: #4ade80;
  
  /* Status Colors - Adjusted for dark mode */
  --status-success: #4ade80;
  --status-warning: #fbbf24;
  --status-error: #f87171;
  --status-info: #60a5fa;
  
  /* Gaming RGB Effects - Enhanced for dark mode */
  --rgb-glow: 0 0 25px rgba(255, 179, 102, 0.5);
  --rgb-glow-strong: 0 0 40px rgba(255, 179, 102, 0.7);
  --neon-glow: 0 0 20px rgba(0, 255, 136, 0.6);
  --cyber-glow: 0 0 20px rgba(0, 212, 255, 0.6);
  
  /* Shadows - Enhanced for dark mode */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  --shadow-gaming: 0 8px 32px rgba(255, 179, 102, 0.25);
}

/* ===== SYSTEM PREFERENCE DETECTION ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Apply dark theme variables when system prefers dark and no theme is set */
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --bg-card: #1e1e1e;
    --bg-elevated: #262626;
    --bg-overlay: rgba(0, 0, 0, 0.8);
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --rgb-glow: 0 0 25px rgba(255, 179, 102, 0.5);
    --rgb-glow-strong: 0 0 40px rgba(255, 179, 102, 0.7);
    --neon-glow: 0 0 20px rgba(0, 255, 136, 0.6);
    --cyber-glow: 0 0 20px rgba(0, 212, 255, 0.6);
    --shadow-gaming: 0 8px 32px rgba(255, 179, 102, 0.25);
  }
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
  
  /* Keep essential transitions for theme switching */
  [data-theme-transitioning] {
    transition: background-color 0.1s ease, color 0.1s ease !important;
  }
}

/* ===== BASE ELEMENT STYLES ===== */
/* Apply theme variables to core elements while preserving text properties */

html {
  background-color: var(--bg-primary);
  transition: background-color var(--transition-theme);
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  transition: background-color var(--transition-theme), color var(--transition-theme);
}

/* ===== GAMING AESTHETIC ENHANCEMENTS ===== */
/* RGB glow effects for gaming elements */
.gaming-glow {
  box-shadow: var(--rgb-glow);
  transition: box-shadow var(--transition-normal);
}

.gaming-glow:hover {
  box-shadow: var(--rgb-glow-strong);
}

.neon-accent {
  box-shadow: var(--neon-glow);
}

.cyber-accent {
  box-shadow: var(--cyber-glow);
}

/* Gaming button styles */
.btn-gaming {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: var(--text-inverse);
  border: none;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-normal);
  box-shadow: var(--rgb-glow);
}

.btn-gaming:hover {
  transform: translateY(-2px);
  box-shadow: var(--rgb-glow-strong);
}

/* Gaming card styles */
.card-gaming {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-gaming);
  transition: all var(--transition-normal);
}

.card-gaming:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl), var(--rgb-glow);
  transform: translateY(-4px);
}

/* ===== HIGH CONTRAST MODE SUPPORT ===== */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
  }
  
  [data-theme="dark"] {
    --border-primary: #ffffff;
    --border-secondary: #ffffff;
    --text-secondary: #ffffff;
    --text-tertiary: #ffffff;
  }
}

/* ===== THEME TRANSITION CLASSES ===== */
.theme-transition {
  transition: 
    background-color var(--transition-theme),
    color var(--transition-theme),
    border-color var(--transition-theme),
    box-shadow var(--transition-theme);
}

.theme-transition-fast {
  transition: 
    background-color var(--transition-fast),
    color var(--transition-fast),
    border-color var(--transition-fast);
}

/* ===== GAMING STORE SPECIFIC STYLES ===== */
/* PC Gaming section enhancements */
.pc-gaming-hero {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border: 1px solid var(--border-primary);
}

.product-card-gaming {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.product-card-gaming:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-lg), var(--rgb-glow);
}

/* RGB product highlights */
.rgb-product {
  position: relative;
  overflow: hidden;
}

.rgb-product::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 179, 102, 0.1),
    rgba(0, 255, 136, 0.1),
    rgba(0, 212, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.rgb-product:hover::before {
  left: 100%;
}

/* Gaming performance indicators */
.performance-badge {
  background: linear-gradient(135deg, var(--accent-secondary), var(--accent-tertiary));
  color: var(--text-inverse);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xs);
  padding: 4px 8px;
  border-radius: 6px;
  box-shadow: var(--neon-glow);
}

/* Microsoft/Xbox integration styles */
.xbox-integration {
  background: linear-gradient(135deg, #107c10, #0e6e0e);
  color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 0 15px rgba(16, 124, 16, 0.3);
}

[data-theme="dark"] .xbox-integration {
  box-shadow: 0 0 20px rgba(16, 124, 16, 0.5);
}

/* ===== GAMING THEME SWITCH EFFECTS ===== */
/* RGB pulse effect when switching themes */
@keyframes rgbPulse {
  0% { box-shadow: 0 0 5px var(--accent-primary); }
  25% { box-shadow: 0 0 20px var(--accent-secondary); }
  50% { box-shadow: 0 0 30px var(--accent-tertiary); }
  75% { box-shadow: 0 0 20px var(--accent-purple); }
  100% { box-shadow: 0 0 5px var(--accent-primary); }
}

.theme-switch-pulse {
  animation: rgbPulse 0.6s ease-in-out;
}

/* Gaming dark mode enhancements */
.gaming-dark-mode {
  --enhanced-glow: 0 0 40px rgba(255, 179, 102, 0.3);
}

.gaming-dark-mode .gaming-glow {
  box-shadow: var(--enhanced-glow);
}

.gaming-dark-mode .gaming-glow:hover {
  box-shadow: 0 0 60px rgba(255, 179, 102, 0.5);
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for gaming theme */
.gaming-focus:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(255, 179, 102, 0.2);
}

[data-theme="dark"] .gaming-focus:focus {
  box-shadow: 0 0 0 4px rgba(255, 179, 102, 0.3);
}

/* ===== WINDOWS 11 / XBOX INTEGRATION ===== */
/* Sync with Windows 11 theme colors */
@media (prefers-color-scheme: dark) {
  .windows-integration {
    background: linear-gradient(135deg, #0078d4, #106ebe);
    color: #ffffff;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 0 15px rgba(0, 120, 212, 0.3);
  }
}

.windows-integration {
  background: linear-gradient(135deg, #0078d4, #005a9e);
  color: #ffffff;
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 0 10px rgba(0, 120, 212, 0.2);
}

/* ===== GAMING PERFORMANCE OPTIMIZATIONS ===== */
/* GPU acceleration for smooth transitions */
.theme-transition,
.gaming-glow,
.card-gaming,
.btn-gaming {
  transform: translateZ(0);
  will-change: transform, box-shadow, background-color;
}

/* Optimize for 120Hz+ gaming displays */
@media (min-resolution: 120dpi) {
  .theme-transition {
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

/* ===== MOBILE GAMING OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  .gaming-glow {
    box-shadow: var(--shadow-md); /* Reduce glow effects on mobile for performance */
  }

  .gaming-glow:hover {
    box-shadow: var(--rgb-glow);
  }

  /* Touch-friendly theme toggle */
  .theme-toggle-mobile {
    min-width: 44px;
    min-height: 44px;
  }
}

/* ===== GAMING MONITOR SUPPORT ===== */
/* HDR and wide gamut display support */
@media (color-gamut: p3) {
  :root {
    --accent-primary: color(display-p3 1 0.7 0.4); /* Enhanced orange for P3 displays */
    --accent-secondary: color(display-p3 0 1 0.53); /* Enhanced green for P3 displays */
    --accent-tertiary: color(display-p3 0 0.83 1); /* Enhanced blue for P3 displays */
  }
}

/* Ultra-wide monitor support */
@media (min-aspect-ratio: 21/9) {
  .gaming-container {
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* ===== THEME TOGGLE SPECIFIC STYLES ===== */
.theme-toggle-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.theme-toggle-button {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.theme-toggle-button:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--rgb-glow);
  transform: translateY(-1px);
}

.theme-toggle-button:focus {
  outline: none;
  ring: 2px solid var(--accent-primary);
  ring-offset: 2px;
  ring-offset-color: var(--bg-primary);
}

.theme-toggle-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Gaming RGB border animation */
@keyframes rgbBorder {
  0% { border-color: var(--accent-primary); }
  25% { border-color: var(--accent-secondary); }
  50% { border-color: var(--accent-tertiary); }
  75% { border-color: var(--accent-purple); }
  100% { border-color: var(--accent-primary); }
}

.theme-toggle-button.rgb-active {
  animation: rgbBorder 2s linear infinite;
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .theme-toggle-container {
    display: none !important;
  }
}
