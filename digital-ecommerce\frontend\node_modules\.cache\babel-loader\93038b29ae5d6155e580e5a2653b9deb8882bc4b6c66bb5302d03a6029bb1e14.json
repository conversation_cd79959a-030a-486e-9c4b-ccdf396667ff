{"ast": null, "code": "import { secondsToMilliseconds } from 'motion-utils';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { NativeAnimation } from './NativeAnimation.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { replaceStringEasing } from './waapi/utils/unsupported-easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nclass NativeAnimationExtended extends NativeAnimation {\n  constructor(options) {\n    /**\n     * The base NativeAnimation function only supports a subset\n     * of Motion easings, and WAAPI also only supports some\n     * easing functions via string/cubic-bezier definitions.\n     *\n     * This function replaces those unsupported easing functions\n     * with a JS easing function. This will later get compiled\n     * to a linear() easing function.\n     */\n    replaceStringEasing(options);\n    /**\n     * Ensure we replace the transition type with a generator function\n     * before passing to WAAPI.\n     *\n     * TODO: Does this have a better home? It could be shared with\n     * JSAnimation.\n     */\n    replaceTransitionType(options);\n    super(options);\n    if (options.startTime) {\n      this.startTime = options.startTime;\n    }\n    this.options = options;\n  }\n  /**\n   * WAAPI doesn't natively have any interruption capabilities.\n   *\n   * Rather than read commited styles back out of the DOM, we can\n   * create a renderless JS animation and sample it twice to calculate\n   * its current value, \"previous\" value, and therefore allow\n   * Motion to calculate velocity for any subsequent animation.\n   */\n  updateMotionValue(value) {\n    const {\n      motionValue,\n      onUpdate,\n      onComplete,\n      element,\n      ...options\n    } = this.options;\n    if (!motionValue) return;\n    if (value !== undefined) {\n      motionValue.set(value);\n      return;\n    }\n    const sampleAnimation = new JSAnimation({\n      ...options,\n      autoplay: false\n    });\n    const sampleTime = secondsToMilliseconds(this.finishedTime ?? this.time);\n    motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n    sampleAnimation.stop();\n  }\n}\nexport { NativeAnimationExtended };", "map": {"version": 3, "names": ["secondsToMilliseconds", "JSAnimation", "NativeAnimation", "replaceTransitionType", "replaceStringEasing", "sampleDelta", "NativeAnimationExtended", "constructor", "options", "startTime", "updateMotionValue", "value", "motionValue", "onUpdate", "onComplete", "element", "undefined", "set", "sampleAnimation", "autoplay", "sampleTime", "finishedTime", "time", "setWithVelocity", "sample", "stop"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/NativeAnimationExtended.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { NativeAnimation } from './NativeAnimation.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { replaceStringEasing } from './waapi/utils/unsupported-easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nclass NativeAnimationExtended extends NativeAnimation {\n    constructor(options) {\n        /**\n         * The base NativeAnimation function only supports a subset\n         * of Motion easings, and WAAPI also only supports some\n         * easing functions via string/cubic-bezier definitions.\n         *\n         * This function replaces those unsupported easing functions\n         * with a JS easing function. This will later get compiled\n         * to a linear() easing function.\n         */\n        replaceStringEasing(options);\n        /**\n         * Ensure we replace the transition type with a generator function\n         * before passing to WAAPI.\n         *\n         * TODO: Does this have a better home? It could be shared with\n         * JSAnimation.\n         */\n        replaceTransitionType(options);\n        super(options);\n        if (options.startTime) {\n            this.startTime = options.startTime;\n        }\n        this.options = options;\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    updateMotionValue(value) {\n        const { motionValue, onUpdate, onComplete, element, ...options } = this.options;\n        if (!motionValue)\n            return;\n        if (value !== undefined) {\n            motionValue.set(value);\n            return;\n        }\n        const sampleAnimation = new JSAnimation({\n            ...options,\n            autoplay: false,\n        });\n        const sampleTime = secondsToMilliseconds(this.finishedTime ?? this.time);\n        motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n        sampleAnimation.stop();\n    }\n}\n\nexport { NativeAnimationExtended };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,cAAc;AACpD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,mBAAmB,QAAQ,sCAAsC;;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;AACxB,MAAMC,uBAAuB,SAASJ,eAAe,CAAC;EAClDK,WAAWA,CAACC,OAAO,EAAE;IACjB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQJ,mBAAmB,CAACI,OAAO,CAAC;IAC5B;AACR;AACA;AACA;AACA;AACA;AACA;IACQL,qBAAqB,CAACK,OAAO,CAAC;IAC9B,KAAK,CAACA,OAAO,CAAC;IACd,IAAIA,OAAO,CAACC,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAGD,OAAO,CAACC,SAAS;IACtC;IACA,IAAI,CAACD,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,iBAAiBA,CAACC,KAAK,EAAE;IACrB,MAAM;MAAEC,WAAW;MAAEC,QAAQ;MAAEC,UAAU;MAAEC,OAAO;MAAE,GAAGP;IAAQ,CAAC,GAAG,IAAI,CAACA,OAAO;IAC/E,IAAI,CAACI,WAAW,EACZ;IACJ,IAAID,KAAK,KAAKK,SAAS,EAAE;MACrBJ,WAAW,CAACK,GAAG,CAACN,KAAK,CAAC;MACtB;IACJ;IACA,MAAMO,eAAe,GAAG,IAAIjB,WAAW,CAAC;MACpC,GAAGO,OAAO;MACVW,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMC,UAAU,GAAGpB,qBAAqB,CAAC,IAAI,CAACqB,YAAY,IAAI,IAAI,CAACC,IAAI,CAAC;IACxEV,WAAW,CAACW,eAAe,CAACL,eAAe,CAACM,MAAM,CAACJ,UAAU,GAAGf,WAAW,CAAC,CAACM,KAAK,EAAEO,eAAe,CAACM,MAAM,CAACJ,UAAU,CAAC,CAACT,KAAK,EAAEN,WAAW,CAAC;IAC1Ia,eAAe,CAACO,IAAI,CAAC,CAAC;EAC1B;AACJ;AAEA,SAASnB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}