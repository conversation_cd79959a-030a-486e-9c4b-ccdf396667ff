{"ast": null, "code": "import { px } from 'motion-dom';\nfunction pixelsToPercent(pixels, axis) {\n  if (axis.max === axis.min) return 0;\n  return pixels / (axis.max - axis.min) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n  correct: (latest, node) => {\n    if (!node.target) return latest;\n    /**\n     * If latest is a string, if it's a percentage we can return immediately as it's\n     * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n     */\n    if (typeof latest === \"string\") {\n      if (px.test(latest)) {\n        latest = parseFloat(latest);\n      } else {\n        return latest;\n      }\n    }\n    /**\n     * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n     * pixel value as a percentage of each axis\n     */\n    const x = pixelsToPercent(latest, node.target.x);\n    const y = pixelsToPercent(latest, node.target.y);\n    return `${x}% ${y}%`;\n  }\n};\nexport { correctBorderRadius, pixelsToPercent };", "map": {"version": 3, "names": ["px", "pixelsToPercent", "pixels", "axis", "max", "min", "correctBorderRadius", "correct", "latest", "node", "target", "test", "parseFloat", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs"], "sourcesContent": ["import { px } from 'motion-dom';\n\nfunction pixelsToPercent(pixels, axis) {\n    if (axis.max === axis.min)\n        return 0;\n    return (pixels / (axis.max - axis.min)) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n    correct: (latest, node) => {\n        if (!node.target)\n            return latest;\n        /**\n         * If latest is a string, if it's a percentage we can return immediately as it's\n         * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n         */\n        if (typeof latest === \"string\") {\n            if (px.test(latest)) {\n                latest = parseFloat(latest);\n            }\n            else {\n                return latest;\n            }\n        }\n        /**\n         * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n         * pixel value as a percentage of each axis\n         */\n        const x = pixelsToPercent(latest, node.target.x);\n        const y = pixelsToPercent(latest, node.target.y);\n        return `${x}% ${y}%`;\n    },\n};\n\nexport { correctBorderRadius, pixelsToPercent };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,YAAY;AAE/B,SAASC,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACC,GAAG,KAAKD,IAAI,CAACE,GAAG,EACrB,OAAO,CAAC;EACZ,OAAQH,MAAM,IAAIC,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAC,GAAI,GAAG;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxBC,OAAO,EAAEA,CAACC,MAAM,EAAEC,IAAI,KAAK;IACvB,IAAI,CAACA,IAAI,CAACC,MAAM,EACZ,OAAOF,MAAM;IACjB;AACR;AACA;AACA;IACQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIR,EAAE,CAACW,IAAI,CAACH,MAAM,CAAC,EAAE;QACjBA,MAAM,GAAGI,UAAU,CAACJ,MAAM,CAAC;MAC/B,CAAC,MACI;QACD,OAAOA,MAAM;MACjB;IACJ;IACA;AACR;AACA;AACA;IACQ,MAAMK,CAAC,GAAGZ,eAAe,CAACO,MAAM,EAAEC,IAAI,CAACC,MAAM,CAACG,CAAC,CAAC;IAChD,MAAMC,CAAC,GAAGb,eAAe,CAACO,MAAM,EAAEC,IAAI,CAACC,MAAM,CAACI,CAAC,CAAC;IAChD,OAAO,GAAGD,CAAC,KAAKC,CAAC,GAAG;EACxB;AACJ,CAAC;AAED,SAASR,mBAAmB,EAAEL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}